function xd(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in e)){const l=Object.getOwnPropertyDescriptor(r,o);l&&Object.defineProperty(e,o,l.get?l:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const l of o)if(l.type==="childList")for(const i of l.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&r(i)}).observe(document,{childList:!0,subtree:!0});function n(o){const l={};return o.integrity&&(l.integrity=o.integrity),o.referrerPolicy&&(l.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?l.credentials="include":o.crossOrigin==="anonymous"?l.credentials="omit":l.credentials="same-origin",l}function r(o){if(o.ep)return;o.ep=!0;const l=n(o);fetch(o.href,l)}})();function kd(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Aa={exports:{}},To={},Ua={exports:{}},b={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var kr=Symbol.for("react.element"),Sd=Symbol.for("react.portal"),Cd=Symbol.for("react.fragment"),Ed=Symbol.for("react.strict_mode"),Nd=Symbol.for("react.profiler"),_d=Symbol.for("react.provider"),Pd=Symbol.for("react.context"),zd=Symbol.for("react.forward_ref"),jd=Symbol.for("react.suspense"),Td=Symbol.for("react.memo"),Rd=Symbol.for("react.lazy"),vs=Symbol.iterator;function Ld(e){return e===null||typeof e!="object"?null:(e=vs&&e[vs]||e["@@iterator"],typeof e=="function"?e:null)}var $a={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Va=Object.assign,Ba={};function Rn(e,t,n){this.props=e,this.context=t,this.refs=Ba,this.updater=n||$a}Rn.prototype.isReactComponent={};Rn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Rn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Wa(){}Wa.prototype=Rn.prototype;function yi(e,t,n){this.props=e,this.context=t,this.refs=Ba,this.updater=n||$a}var wi=yi.prototype=new Wa;wi.constructor=yi;Va(wi,Rn.prototype);wi.isPureReactComponent=!0;var ys=Array.isArray,Ha=Object.prototype.hasOwnProperty,xi={current:null},Qa={key:!0,ref:!0,__self:!0,__source:!0};function Ga(e,t,n){var r,o={},l=null,i=null;if(t!=null)for(r in t.ref!==void 0&&(i=t.ref),t.key!==void 0&&(l=""+t.key),t)Ha.call(t,r)&&!Qa.hasOwnProperty(r)&&(o[r]=t[r]);var s=arguments.length-2;if(s===1)o.children=n;else if(1<s){for(var a=Array(s),u=0;u<s;u++)a[u]=arguments[u+2];o.children=a}if(e&&e.defaultProps)for(r in s=e.defaultProps,s)o[r]===void 0&&(o[r]=s[r]);return{$$typeof:kr,type:e,key:l,ref:i,props:o,_owner:xi.current}}function Md(e,t){return{$$typeof:kr,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function ki(e){return typeof e=="object"&&e!==null&&e.$$typeof===kr}function Od(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var ws=/\/+/g;function Qo(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Od(""+e.key):t.toString(36)}function Wr(e,t,n,r,o){var l=typeof e;(l==="undefined"||l==="boolean")&&(e=null);var i=!1;if(e===null)i=!0;else switch(l){case"string":case"number":i=!0;break;case"object":switch(e.$$typeof){case kr:case Sd:i=!0}}if(i)return i=e,o=o(i),e=r===""?"."+Qo(i,0):r,ys(o)?(n="",e!=null&&(n=e.replace(ws,"$&/")+"/"),Wr(o,t,n,"",function(u){return u})):o!=null&&(ki(o)&&(o=Md(o,n+(!o.key||i&&i.key===o.key?"":(""+o.key).replace(ws,"$&/")+"/")+e)),t.push(o)),1;if(i=0,r=r===""?".":r+":",ys(e))for(var s=0;s<e.length;s++){l=e[s];var a=r+Qo(l,s);i+=Wr(l,t,n,a,o)}else if(a=Ld(e),typeof a=="function")for(e=a.call(e),s=0;!(l=e.next()).done;)l=l.value,a=r+Qo(l,s++),i+=Wr(l,t,n,a,o);else if(l==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return i}function Pr(e,t,n){if(e==null)return e;var r=[],o=0;return Wr(e,r,"","",function(l){return t.call(n,l,o++)}),r}function bd(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var ge={current:null},Hr={transition:null},Id={ReactCurrentDispatcher:ge,ReactCurrentBatchConfig:Hr,ReactCurrentOwner:xi};function Ya(){throw Error("act(...) is not supported in production builds of React.")}b.Children={map:Pr,forEach:function(e,t,n){Pr(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Pr(e,function(){t++}),t},toArray:function(e){return Pr(e,function(t){return t})||[]},only:function(e){if(!ki(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};b.Component=Rn;b.Fragment=Cd;b.Profiler=Nd;b.PureComponent=yi;b.StrictMode=Ed;b.Suspense=jd;b.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Id;b.act=Ya;b.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Va({},e.props),o=e.key,l=e.ref,i=e._owner;if(t!=null){if(t.ref!==void 0&&(l=t.ref,i=xi.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(a in t)Ha.call(t,a)&&!Qa.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&s!==void 0?s[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){s=Array(a);for(var u=0;u<a;u++)s[u]=arguments[u+2];r.children=s}return{$$typeof:kr,type:e.type,key:o,ref:l,props:r,_owner:i}};b.createContext=function(e){return e={$$typeof:Pd,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:_d,_context:e},e.Consumer=e};b.createElement=Ga;b.createFactory=function(e){var t=Ga.bind(null,e);return t.type=e,t};b.createRef=function(){return{current:null}};b.forwardRef=function(e){return{$$typeof:zd,render:e}};b.isValidElement=ki;b.lazy=function(e){return{$$typeof:Rd,_payload:{_status:-1,_result:e},_init:bd}};b.memo=function(e,t){return{$$typeof:Td,type:e,compare:t===void 0?null:t}};b.startTransition=function(e){var t=Hr.transition;Hr.transition={};try{e()}finally{Hr.transition=t}};b.unstable_act=Ya;b.useCallback=function(e,t){return ge.current.useCallback(e,t)};b.useContext=function(e){return ge.current.useContext(e)};b.useDebugValue=function(){};b.useDeferredValue=function(e){return ge.current.useDeferredValue(e)};b.useEffect=function(e,t){return ge.current.useEffect(e,t)};b.useId=function(){return ge.current.useId()};b.useImperativeHandle=function(e,t,n){return ge.current.useImperativeHandle(e,t,n)};b.useInsertionEffect=function(e,t){return ge.current.useInsertionEffect(e,t)};b.useLayoutEffect=function(e,t){return ge.current.useLayoutEffect(e,t)};b.useMemo=function(e,t){return ge.current.useMemo(e,t)};b.useReducer=function(e,t,n){return ge.current.useReducer(e,t,n)};b.useRef=function(e){return ge.current.useRef(e)};b.useState=function(e){return ge.current.useState(e)};b.useSyncExternalStore=function(e,t,n){return ge.current.useSyncExternalStore(e,t,n)};b.useTransition=function(){return ge.current.useTransition()};b.version="18.3.1";Ua.exports=b;var S=Ua.exports;const et=kd(S),Fd=xd({__proto__:null,default:et},[S]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Dd=S,Ad=Symbol.for("react.element"),Ud=Symbol.for("react.fragment"),$d=Object.prototype.hasOwnProperty,Vd=Dd.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Bd={key:!0,ref:!0,__self:!0,__source:!0};function Ka(e,t,n){var r,o={},l=null,i=null;n!==void 0&&(l=""+n),t.key!==void 0&&(l=""+t.key),t.ref!==void 0&&(i=t.ref);for(r in t)$d.call(t,r)&&!Bd.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:Ad,type:e,key:l,ref:i,props:o,_owner:Vd.current}}To.Fragment=Ud;To.jsx=Ka;To.jsxs=Ka;Aa.exports=To;var g=Aa.exports,wl={},Xa={exports:{}},ze={},Ja={exports:{}},Za={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(_,R){var L=_.length;_.push(R);e:for(;0<L;){var I=L-1>>>1,ee=_[I];if(0<o(ee,R))_[I]=R,_[L]=ee,L=I;else break e}}function n(_){return _.length===0?null:_[0]}function r(_){if(_.length===0)return null;var R=_[0],L=_.pop();if(L!==R){_[0]=L;e:for(var I=0,ee=_.length,E=ee>>>1;I<E;){var V=2*(I+1)-1,A=_[V],Z=V+1,ht=_[Z];if(0>o(A,L))Z<ee&&0>o(ht,A)?(_[I]=ht,_[Z]=L,I=Z):(_[I]=A,_[V]=L,I=V);else if(Z<ee&&0>o(ht,L))_[I]=ht,_[Z]=L,I=Z;else break e}}return R}function o(_,R){var L=_.sortIndex-R.sortIndex;return L!==0?L:_.id-R.id}if(typeof performance=="object"&&typeof performance.now=="function"){var l=performance;e.unstable_now=function(){return l.now()}}else{var i=Date,s=i.now();e.unstable_now=function(){return i.now()-s}}var a=[],u=[],h=1,m=null,p=3,w=!1,x=!1,v=!1,C=typeof setTimeout=="function"?setTimeout:null,d=typeof clearTimeout=="function"?clearTimeout:null,c=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function f(_){for(var R=n(u);R!==null;){if(R.callback===null)r(u);else if(R.startTime<=_)r(u),R.sortIndex=R.expirationTime,t(a,R);else break;R=n(u)}}function y(_){if(v=!1,f(_),!x)if(n(a)!==null)x=!0,Qe(N);else{var R=n(u);R!==null&&Ge(y,R.startTime-_)}}function N(_,R){x=!1,v&&(v=!1,d(T),T=-1),w=!0;var L=p;try{for(f(R),m=n(a);m!==null&&(!(m.expirationTime>R)||_&&!ie());){var I=m.callback;if(typeof I=="function"){m.callback=null,p=m.priorityLevel;var ee=I(m.expirationTime<=R);R=e.unstable_now(),typeof ee=="function"?m.callback=ee:m===n(a)&&r(a),f(R)}else r(a);m=n(a)}if(m!==null)var E=!0;else{var V=n(u);V!==null&&Ge(y,V.startTime-R),E=!1}return E}finally{m=null,p=L,w=!1}}var P=!1,j=null,T=-1,U=5,M=-1;function ie(){return!(e.unstable_now()-M<U)}function D(){if(j!==null){var _=e.unstable_now();M=_;var R=!0;try{R=j(!0,_)}finally{R?He():(P=!1,j=null)}}else P=!1}var He;if(typeof c=="function")He=function(){c(D)};else if(typeof MessageChannel<"u"){var tt=new MessageChannel,At=tt.port2;tt.port1.onmessage=D,He=function(){At.postMessage(null)}}else He=function(){C(D,0)};function Qe(_){j=_,P||(P=!0,He())}function Ge(_,R){T=C(function(){_(e.unstable_now())},R)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(_){_.callback=null},e.unstable_continueExecution=function(){x||w||(x=!0,Qe(N))},e.unstable_forceFrameRate=function(_){0>_||125<_?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):U=0<_?Math.floor(1e3/_):5},e.unstable_getCurrentPriorityLevel=function(){return p},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(_){switch(p){case 1:case 2:case 3:var R=3;break;default:R=p}var L=p;p=R;try{return _()}finally{p=L}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(_,R){switch(_){case 1:case 2:case 3:case 4:case 5:break;default:_=3}var L=p;p=_;try{return R()}finally{p=L}},e.unstable_scheduleCallback=function(_,R,L){var I=e.unstable_now();switch(typeof L=="object"&&L!==null?(L=L.delay,L=typeof L=="number"&&0<L?I+L:I):L=I,_){case 1:var ee=-1;break;case 2:ee=250;break;case 5:ee=**********;break;case 4:ee=1e4;break;default:ee=5e3}return ee=L+ee,_={id:h++,callback:R,priorityLevel:_,startTime:L,expirationTime:ee,sortIndex:-1},L>I?(_.sortIndex=L,t(u,_),n(a)===null&&_===n(u)&&(v?(d(T),T=-1):v=!0,Ge(y,L-I))):(_.sortIndex=ee,t(a,_),x||w||(x=!0,Qe(N))),_},e.unstable_shouldYield=ie,e.unstable_wrapCallback=function(_){var R=p;return function(){var L=p;p=R;try{return _.apply(this,arguments)}finally{p=L}}}})(Za);Ja.exports=Za;var Wd=Ja.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Hd=S,_e=Wd;function k(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var qa=new Set,or={};function en(e,t){En(e,t),En(e+"Capture",t)}function En(e,t){for(or[e]=t,e=0;e<t.length;e++)qa.add(t[e])}var ut=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),xl=Object.prototype.hasOwnProperty,Qd=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,xs={},ks={};function Gd(e){return xl.call(ks,e)?!0:xl.call(xs,e)?!1:Qd.test(e)?ks[e]=!0:(xs[e]=!0,!1)}function Yd(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Kd(e,t,n,r){if(t===null||typeof t>"u"||Yd(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function ve(e,t,n,r,o,l,i){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=l,this.removeEmptyString=i}var ue={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ue[e]=new ve(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ue[t]=new ve(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ue[e]=new ve(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ue[e]=new ve(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ue[e]=new ve(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ue[e]=new ve(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ue[e]=new ve(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ue[e]=new ve(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ue[e]=new ve(e,5,!1,e.toLowerCase(),null,!1,!1)});var Si=/[\-:]([a-z])/g;function Ci(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Si,Ci);ue[t]=new ve(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Si,Ci);ue[t]=new ve(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Si,Ci);ue[t]=new ve(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ue[e]=new ve(e,1,!1,e.toLowerCase(),null,!1,!1)});ue.xlinkHref=new ve("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ue[e]=new ve(e,1,!1,e.toLowerCase(),null,!0,!0)});function Ei(e,t,n,r){var o=ue.hasOwnProperty(t)?ue[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Kd(t,n,o,r)&&(n=null),r||o===null?Gd(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var pt=Hd.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,zr=Symbol.for("react.element"),on=Symbol.for("react.portal"),ln=Symbol.for("react.fragment"),Ni=Symbol.for("react.strict_mode"),kl=Symbol.for("react.profiler"),eu=Symbol.for("react.provider"),tu=Symbol.for("react.context"),_i=Symbol.for("react.forward_ref"),Sl=Symbol.for("react.suspense"),Cl=Symbol.for("react.suspense_list"),Pi=Symbol.for("react.memo"),xt=Symbol.for("react.lazy"),nu=Symbol.for("react.offscreen"),Ss=Symbol.iterator;function bn(e){return e===null||typeof e!="object"?null:(e=Ss&&e[Ss]||e["@@iterator"],typeof e=="function"?e:null)}var X=Object.assign,Go;function Hn(e){if(Go===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Go=t&&t[1]||""}return`
`+Go+e}var Yo=!1;function Ko(e,t){if(!e||Yo)return"";Yo=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var o=u.stack.split(`
`),l=r.stack.split(`
`),i=o.length-1,s=l.length-1;1<=i&&0<=s&&o[i]!==l[s];)s--;for(;1<=i&&0<=s;i--,s--)if(o[i]!==l[s]){if(i!==1||s!==1)do if(i--,s--,0>s||o[i]!==l[s]){var a=`
`+o[i].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=i&&0<=s);break}}}finally{Yo=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Hn(e):""}function Xd(e){switch(e.tag){case 5:return Hn(e.type);case 16:return Hn("Lazy");case 13:return Hn("Suspense");case 19:return Hn("SuspenseList");case 0:case 2:case 15:return e=Ko(e.type,!1),e;case 11:return e=Ko(e.type.render,!1),e;case 1:return e=Ko(e.type,!0),e;default:return""}}function El(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case ln:return"Fragment";case on:return"Portal";case kl:return"Profiler";case Ni:return"StrictMode";case Sl:return"Suspense";case Cl:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case tu:return(e.displayName||"Context")+".Consumer";case eu:return(e._context.displayName||"Context")+".Provider";case _i:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Pi:return t=e.displayName||null,t!==null?t:El(e.type)||"Memo";case xt:t=e._payload,e=e._init;try{return El(e(t))}catch{}}return null}function Jd(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return El(t);case 8:return t===Ni?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Ot(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function ru(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Zd(e){var t=ru(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,l=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(i){r=""+i,l.call(this,i)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(i){r=""+i},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function jr(e){e._valueTracker||(e._valueTracker=Zd(e))}function ou(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=ru(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function oo(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Nl(e,t){var n=t.checked;return X({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Cs(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Ot(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function lu(e,t){t=t.checked,t!=null&&Ei(e,"checked",t,!1)}function _l(e,t){lu(e,t);var n=Ot(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Pl(e,t.type,n):t.hasOwnProperty("defaultValue")&&Pl(e,t.type,Ot(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Es(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Pl(e,t,n){(t!=="number"||oo(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Qn=Array.isArray;function vn(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Ot(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function zl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(k(91));return X({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Ns(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(k(92));if(Qn(n)){if(1<n.length)throw Error(k(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Ot(n)}}function iu(e,t){var n=Ot(t.value),r=Ot(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function _s(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function su(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function jl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?su(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Tr,au=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Tr=Tr||document.createElement("div"),Tr.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Tr.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function lr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Kn={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},qd=["Webkit","ms","Moz","O"];Object.keys(Kn).forEach(function(e){qd.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Kn[t]=Kn[e]})});function uu(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Kn.hasOwnProperty(e)&&Kn[e]?(""+t).trim():t+"px"}function cu(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=uu(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var ef=X({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Tl(e,t){if(t){if(ef[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(k(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(k(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(k(61))}if(t.style!=null&&typeof t.style!="object")throw Error(k(62))}}function Rl(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Ll=null;function zi(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Ml=null,yn=null,wn=null;function Ps(e){if(e=Er(e)){if(typeof Ml!="function")throw Error(k(280));var t=e.stateNode;t&&(t=bo(t),Ml(e.stateNode,e.type,t))}}function du(e){yn?wn?wn.push(e):wn=[e]:yn=e}function fu(){if(yn){var e=yn,t=wn;if(wn=yn=null,Ps(e),t)for(e=0;e<t.length;e++)Ps(t[e])}}function pu(e,t){return e(t)}function mu(){}var Xo=!1;function hu(e,t,n){if(Xo)return e(t,n);Xo=!0;try{return pu(e,t,n)}finally{Xo=!1,(yn!==null||wn!==null)&&(mu(),fu())}}function ir(e,t){var n=e.stateNode;if(n===null)return null;var r=bo(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(k(231,t,typeof n));return n}var Ol=!1;if(ut)try{var In={};Object.defineProperty(In,"passive",{get:function(){Ol=!0}}),window.addEventListener("test",In,In),window.removeEventListener("test",In,In)}catch{Ol=!1}function tf(e,t,n,r,o,l,i,s,a){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(h){this.onError(h)}}var Xn=!1,lo=null,io=!1,bl=null,nf={onError:function(e){Xn=!0,lo=e}};function rf(e,t,n,r,o,l,i,s,a){Xn=!1,lo=null,tf.apply(nf,arguments)}function of(e,t,n,r,o,l,i,s,a){if(rf.apply(this,arguments),Xn){if(Xn){var u=lo;Xn=!1,lo=null}else throw Error(k(198));io||(io=!0,bl=u)}}function tn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function gu(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function zs(e){if(tn(e)!==e)throw Error(k(188))}function lf(e){var t=e.alternate;if(!t){if(t=tn(e),t===null)throw Error(k(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var l=o.alternate;if(l===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===l.child){for(l=o.child;l;){if(l===n)return zs(o),e;if(l===r)return zs(o),t;l=l.sibling}throw Error(k(188))}if(n.return!==r.return)n=o,r=l;else{for(var i=!1,s=o.child;s;){if(s===n){i=!0,n=o,r=l;break}if(s===r){i=!0,r=o,n=l;break}s=s.sibling}if(!i){for(s=l.child;s;){if(s===n){i=!0,n=l,r=o;break}if(s===r){i=!0,r=l,n=o;break}s=s.sibling}if(!i)throw Error(k(189))}}if(n.alternate!==r)throw Error(k(190))}if(n.tag!==3)throw Error(k(188));return n.stateNode.current===n?e:t}function vu(e){return e=lf(e),e!==null?yu(e):null}function yu(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=yu(e);if(t!==null)return t;e=e.sibling}return null}var wu=_e.unstable_scheduleCallback,js=_e.unstable_cancelCallback,sf=_e.unstable_shouldYield,af=_e.unstable_requestPaint,q=_e.unstable_now,uf=_e.unstable_getCurrentPriorityLevel,ji=_e.unstable_ImmediatePriority,xu=_e.unstable_UserBlockingPriority,so=_e.unstable_NormalPriority,cf=_e.unstable_LowPriority,ku=_e.unstable_IdlePriority,Ro=null,Ze=null;function df(e){if(Ze&&typeof Ze.onCommitFiberRoot=="function")try{Ze.onCommitFiberRoot(Ro,e,void 0,(e.current.flags&128)===128)}catch{}}var Ve=Math.clz32?Math.clz32:mf,ff=Math.log,pf=Math.LN2;function mf(e){return e>>>=0,e===0?32:31-(ff(e)/pf|0)|0}var Rr=64,Lr=4194304;function Gn(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ao(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,l=e.pingedLanes,i=n&268435455;if(i!==0){var s=i&~o;s!==0?r=Gn(s):(l&=i,l!==0&&(r=Gn(l)))}else i=n&~o,i!==0?r=Gn(i):l!==0&&(r=Gn(l));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,l=t&-t,o>=l||o===16&&(l&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Ve(t),o=1<<n,r|=e[n],t&=~o;return r}function hf(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function gf(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,l=e.pendingLanes;0<l;){var i=31-Ve(l),s=1<<i,a=o[i];a===-1?(!(s&n)||s&r)&&(o[i]=hf(s,t)):a<=t&&(e.expiredLanes|=s),l&=~s}}function Il(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Su(){var e=Rr;return Rr<<=1,!(Rr&4194240)&&(Rr=64),e}function Jo(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Sr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Ve(t),e[t]=n}function vf(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-Ve(n),l=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~l}}function Ti(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Ve(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var $=0;function Cu(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Eu,Ri,Nu,_u,Pu,Fl=!1,Mr=[],_t=null,Pt=null,zt=null,sr=new Map,ar=new Map,St=[],yf="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Ts(e,t){switch(e){case"focusin":case"focusout":_t=null;break;case"dragenter":case"dragleave":Pt=null;break;case"mouseover":case"mouseout":zt=null;break;case"pointerover":case"pointerout":sr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":ar.delete(t.pointerId)}}function Fn(e,t,n,r,o,l){return e===null||e.nativeEvent!==l?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:l,targetContainers:[o]},t!==null&&(t=Er(t),t!==null&&Ri(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function wf(e,t,n,r,o){switch(t){case"focusin":return _t=Fn(_t,e,t,n,r,o),!0;case"dragenter":return Pt=Fn(Pt,e,t,n,r,o),!0;case"mouseover":return zt=Fn(zt,e,t,n,r,o),!0;case"pointerover":var l=o.pointerId;return sr.set(l,Fn(sr.get(l)||null,e,t,n,r,o)),!0;case"gotpointercapture":return l=o.pointerId,ar.set(l,Fn(ar.get(l)||null,e,t,n,r,o)),!0}return!1}function zu(e){var t=Bt(e.target);if(t!==null){var n=tn(t);if(n!==null){if(t=n.tag,t===13){if(t=gu(n),t!==null){e.blockedOn=t,Pu(e.priority,function(){Nu(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Qr(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Dl(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Ll=r,n.target.dispatchEvent(r),Ll=null}else return t=Er(n),t!==null&&Ri(t),e.blockedOn=n,!1;t.shift()}return!0}function Rs(e,t,n){Qr(e)&&n.delete(t)}function xf(){Fl=!1,_t!==null&&Qr(_t)&&(_t=null),Pt!==null&&Qr(Pt)&&(Pt=null),zt!==null&&Qr(zt)&&(zt=null),sr.forEach(Rs),ar.forEach(Rs)}function Dn(e,t){e.blockedOn===t&&(e.blockedOn=null,Fl||(Fl=!0,_e.unstable_scheduleCallback(_e.unstable_NormalPriority,xf)))}function ur(e){function t(o){return Dn(o,e)}if(0<Mr.length){Dn(Mr[0],e);for(var n=1;n<Mr.length;n++){var r=Mr[n];r.blockedOn===e&&(r.blockedOn=null)}}for(_t!==null&&Dn(_t,e),Pt!==null&&Dn(Pt,e),zt!==null&&Dn(zt,e),sr.forEach(t),ar.forEach(t),n=0;n<St.length;n++)r=St[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<St.length&&(n=St[0],n.blockedOn===null);)zu(n),n.blockedOn===null&&St.shift()}var xn=pt.ReactCurrentBatchConfig,uo=!0;function kf(e,t,n,r){var o=$,l=xn.transition;xn.transition=null;try{$=1,Li(e,t,n,r)}finally{$=o,xn.transition=l}}function Sf(e,t,n,r){var o=$,l=xn.transition;xn.transition=null;try{$=4,Li(e,t,n,r)}finally{$=o,xn.transition=l}}function Li(e,t,n,r){if(uo){var o=Dl(e,t,n,r);if(o===null)sl(e,t,r,co,n),Ts(e,r);else if(wf(o,e,t,n,r))r.stopPropagation();else if(Ts(e,r),t&4&&-1<yf.indexOf(e)){for(;o!==null;){var l=Er(o);if(l!==null&&Eu(l),l=Dl(e,t,n,r),l===null&&sl(e,t,r,co,n),l===o)break;o=l}o!==null&&r.stopPropagation()}else sl(e,t,r,null,n)}}var co=null;function Dl(e,t,n,r){if(co=null,e=zi(r),e=Bt(e),e!==null)if(t=tn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=gu(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return co=e,null}function ju(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(uf()){case ji:return 1;case xu:return 4;case so:case cf:return 16;case ku:return 536870912;default:return 16}default:return 16}}var Et=null,Mi=null,Gr=null;function Tu(){if(Gr)return Gr;var e,t=Mi,n=t.length,r,o="value"in Et?Et.value:Et.textContent,l=o.length;for(e=0;e<n&&t[e]===o[e];e++);var i=n-e;for(r=1;r<=i&&t[n-r]===o[l-r];r++);return Gr=o.slice(e,1<r?1-r:void 0)}function Yr(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Or(){return!0}function Ls(){return!1}function je(e){function t(n,r,o,l,i){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=l,this.target=i,this.currentTarget=null;for(var s in e)e.hasOwnProperty(s)&&(n=e[s],this[s]=n?n(l):l[s]);return this.isDefaultPrevented=(l.defaultPrevented!=null?l.defaultPrevented:l.returnValue===!1)?Or:Ls,this.isPropagationStopped=Ls,this}return X(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Or)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Or)},persist:function(){},isPersistent:Or}),t}var Ln={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Oi=je(Ln),Cr=X({},Ln,{view:0,detail:0}),Cf=je(Cr),Zo,qo,An,Lo=X({},Cr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:bi,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==An&&(An&&e.type==="mousemove"?(Zo=e.screenX-An.screenX,qo=e.screenY-An.screenY):qo=Zo=0,An=e),Zo)},movementY:function(e){return"movementY"in e?e.movementY:qo}}),Ms=je(Lo),Ef=X({},Lo,{dataTransfer:0}),Nf=je(Ef),_f=X({},Cr,{relatedTarget:0}),el=je(_f),Pf=X({},Ln,{animationName:0,elapsedTime:0,pseudoElement:0}),zf=je(Pf),jf=X({},Ln,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Tf=je(jf),Rf=X({},Ln,{data:0}),Os=je(Rf),Lf={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Mf={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Of={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function bf(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Of[e])?!!t[e]:!1}function bi(){return bf}var If=X({},Cr,{key:function(e){if(e.key){var t=Lf[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Yr(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Mf[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:bi,charCode:function(e){return e.type==="keypress"?Yr(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Yr(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Ff=je(If),Df=X({},Lo,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),bs=je(Df),Af=X({},Cr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:bi}),Uf=je(Af),$f=X({},Ln,{propertyName:0,elapsedTime:0,pseudoElement:0}),Vf=je($f),Bf=X({},Lo,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Wf=je(Bf),Hf=[9,13,27,32],Ii=ut&&"CompositionEvent"in window,Jn=null;ut&&"documentMode"in document&&(Jn=document.documentMode);var Qf=ut&&"TextEvent"in window&&!Jn,Ru=ut&&(!Ii||Jn&&8<Jn&&11>=Jn),Is=" ",Fs=!1;function Lu(e,t){switch(e){case"keyup":return Hf.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Mu(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var sn=!1;function Gf(e,t){switch(e){case"compositionend":return Mu(t);case"keypress":return t.which!==32?null:(Fs=!0,Is);case"textInput":return e=t.data,e===Is&&Fs?null:e;default:return null}}function Yf(e,t){if(sn)return e==="compositionend"||!Ii&&Lu(e,t)?(e=Tu(),Gr=Mi=Et=null,sn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Ru&&t.locale!=="ko"?null:t.data;default:return null}}var Kf={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Ds(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Kf[e.type]:t==="textarea"}function Ou(e,t,n,r){du(r),t=fo(t,"onChange"),0<t.length&&(n=new Oi("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Zn=null,cr=null;function Xf(e){Hu(e,0)}function Mo(e){var t=cn(e);if(ou(t))return e}function Jf(e,t){if(e==="change")return t}var bu=!1;if(ut){var tl;if(ut){var nl="oninput"in document;if(!nl){var As=document.createElement("div");As.setAttribute("oninput","return;"),nl=typeof As.oninput=="function"}tl=nl}else tl=!1;bu=tl&&(!document.documentMode||9<document.documentMode)}function Us(){Zn&&(Zn.detachEvent("onpropertychange",Iu),cr=Zn=null)}function Iu(e){if(e.propertyName==="value"&&Mo(cr)){var t=[];Ou(t,cr,e,zi(e)),hu(Xf,t)}}function Zf(e,t,n){e==="focusin"?(Us(),Zn=t,cr=n,Zn.attachEvent("onpropertychange",Iu)):e==="focusout"&&Us()}function qf(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Mo(cr)}function ep(e,t){if(e==="click")return Mo(t)}function tp(e,t){if(e==="input"||e==="change")return Mo(t)}function np(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var We=typeof Object.is=="function"?Object.is:np;function dr(e,t){if(We(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!xl.call(t,o)||!We(e[o],t[o]))return!1}return!0}function $s(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Vs(e,t){var n=$s(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=$s(n)}}function Fu(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Fu(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Du(){for(var e=window,t=oo();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=oo(e.document)}return t}function Fi(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function rp(e){var t=Du(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Fu(n.ownerDocument.documentElement,n)){if(r!==null&&Fi(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,l=Math.min(r.start,o);r=r.end===void 0?l:Math.min(r.end,o),!e.extend&&l>r&&(o=r,r=l,l=o),o=Vs(n,l);var i=Vs(n,r);o&&i&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),l>r?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var op=ut&&"documentMode"in document&&11>=document.documentMode,an=null,Al=null,qn=null,Ul=!1;function Bs(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Ul||an==null||an!==oo(r)||(r=an,"selectionStart"in r&&Fi(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),qn&&dr(qn,r)||(qn=r,r=fo(Al,"onSelect"),0<r.length&&(t=new Oi("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=an)))}function br(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var un={animationend:br("Animation","AnimationEnd"),animationiteration:br("Animation","AnimationIteration"),animationstart:br("Animation","AnimationStart"),transitionend:br("Transition","TransitionEnd")},rl={},Au={};ut&&(Au=document.createElement("div").style,"AnimationEvent"in window||(delete un.animationend.animation,delete un.animationiteration.animation,delete un.animationstart.animation),"TransitionEvent"in window||delete un.transitionend.transition);function Oo(e){if(rl[e])return rl[e];if(!un[e])return e;var t=un[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Au)return rl[e]=t[n];return e}var Uu=Oo("animationend"),$u=Oo("animationiteration"),Vu=Oo("animationstart"),Bu=Oo("transitionend"),Wu=new Map,Ws="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function It(e,t){Wu.set(e,t),en(t,[e])}for(var ol=0;ol<Ws.length;ol++){var ll=Ws[ol],lp=ll.toLowerCase(),ip=ll[0].toUpperCase()+ll.slice(1);It(lp,"on"+ip)}It(Uu,"onAnimationEnd");It($u,"onAnimationIteration");It(Vu,"onAnimationStart");It("dblclick","onDoubleClick");It("focusin","onFocus");It("focusout","onBlur");It(Bu,"onTransitionEnd");En("onMouseEnter",["mouseout","mouseover"]);En("onMouseLeave",["mouseout","mouseover"]);En("onPointerEnter",["pointerout","pointerover"]);En("onPointerLeave",["pointerout","pointerover"]);en("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));en("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));en("onBeforeInput",["compositionend","keypress","textInput","paste"]);en("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));en("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));en("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Yn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),sp=new Set("cancel close invalid load scroll toggle".split(" ").concat(Yn));function Hs(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,of(r,t,void 0,e),e.currentTarget=null}function Hu(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var l=void 0;if(t)for(var i=r.length-1;0<=i;i--){var s=r[i],a=s.instance,u=s.currentTarget;if(s=s.listener,a!==l&&o.isPropagationStopped())break e;Hs(o,s,u),l=a}else for(i=0;i<r.length;i++){if(s=r[i],a=s.instance,u=s.currentTarget,s=s.listener,a!==l&&o.isPropagationStopped())break e;Hs(o,s,u),l=a}}}if(io)throw e=bl,io=!1,bl=null,e}function H(e,t){var n=t[Hl];n===void 0&&(n=t[Hl]=new Set);var r=e+"__bubble";n.has(r)||(Qu(t,e,2,!1),n.add(r))}function il(e,t,n){var r=0;t&&(r|=4),Qu(n,e,r,t)}var Ir="_reactListening"+Math.random().toString(36).slice(2);function fr(e){if(!e[Ir]){e[Ir]=!0,qa.forEach(function(n){n!=="selectionchange"&&(sp.has(n)||il(n,!1,e),il(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Ir]||(t[Ir]=!0,il("selectionchange",!1,t))}}function Qu(e,t,n,r){switch(ju(t)){case 1:var o=kf;break;case 4:o=Sf;break;default:o=Li}n=o.bind(null,t,n,e),o=void 0,!Ol||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function sl(e,t,n,r,o){var l=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var i=r.tag;if(i===3||i===4){var s=r.stateNode.containerInfo;if(s===o||s.nodeType===8&&s.parentNode===o)break;if(i===4)for(i=r.return;i!==null;){var a=i.tag;if((a===3||a===4)&&(a=i.stateNode.containerInfo,a===o||a.nodeType===8&&a.parentNode===o))return;i=i.return}for(;s!==null;){if(i=Bt(s),i===null)return;if(a=i.tag,a===5||a===6){r=l=i;continue e}s=s.parentNode}}r=r.return}hu(function(){var u=l,h=zi(n),m=[];e:{var p=Wu.get(e);if(p!==void 0){var w=Oi,x=e;switch(e){case"keypress":if(Yr(n)===0)break e;case"keydown":case"keyup":w=Ff;break;case"focusin":x="focus",w=el;break;case"focusout":x="blur",w=el;break;case"beforeblur":case"afterblur":w=el;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":w=Ms;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":w=Nf;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":w=Uf;break;case Uu:case $u:case Vu:w=zf;break;case Bu:w=Vf;break;case"scroll":w=Cf;break;case"wheel":w=Wf;break;case"copy":case"cut":case"paste":w=Tf;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":w=bs}var v=(t&4)!==0,C=!v&&e==="scroll",d=v?p!==null?p+"Capture":null:p;v=[];for(var c=u,f;c!==null;){f=c;var y=f.stateNode;if(f.tag===5&&y!==null&&(f=y,d!==null&&(y=ir(c,d),y!=null&&v.push(pr(c,y,f)))),C)break;c=c.return}0<v.length&&(p=new w(p,x,null,n,h),m.push({event:p,listeners:v}))}}if(!(t&7)){e:{if(p=e==="mouseover"||e==="pointerover",w=e==="mouseout"||e==="pointerout",p&&n!==Ll&&(x=n.relatedTarget||n.fromElement)&&(Bt(x)||x[ct]))break e;if((w||p)&&(p=h.window===h?h:(p=h.ownerDocument)?p.defaultView||p.parentWindow:window,w?(x=n.relatedTarget||n.toElement,w=u,x=x?Bt(x):null,x!==null&&(C=tn(x),x!==C||x.tag!==5&&x.tag!==6)&&(x=null)):(w=null,x=u),w!==x)){if(v=Ms,y="onMouseLeave",d="onMouseEnter",c="mouse",(e==="pointerout"||e==="pointerover")&&(v=bs,y="onPointerLeave",d="onPointerEnter",c="pointer"),C=w==null?p:cn(w),f=x==null?p:cn(x),p=new v(y,c+"leave",w,n,h),p.target=C,p.relatedTarget=f,y=null,Bt(h)===u&&(v=new v(d,c+"enter",x,n,h),v.target=f,v.relatedTarget=C,y=v),C=y,w&&x)t:{for(v=w,d=x,c=0,f=v;f;f=nn(f))c++;for(f=0,y=d;y;y=nn(y))f++;for(;0<c-f;)v=nn(v),c--;for(;0<f-c;)d=nn(d),f--;for(;c--;){if(v===d||d!==null&&v===d.alternate)break t;v=nn(v),d=nn(d)}v=null}else v=null;w!==null&&Qs(m,p,w,v,!1),x!==null&&C!==null&&Qs(m,C,x,v,!0)}}e:{if(p=u?cn(u):window,w=p.nodeName&&p.nodeName.toLowerCase(),w==="select"||w==="input"&&p.type==="file")var N=Jf;else if(Ds(p))if(bu)N=tp;else{N=qf;var P=Zf}else(w=p.nodeName)&&w.toLowerCase()==="input"&&(p.type==="checkbox"||p.type==="radio")&&(N=ep);if(N&&(N=N(e,u))){Ou(m,N,n,h);break e}P&&P(e,p,u),e==="focusout"&&(P=p._wrapperState)&&P.controlled&&p.type==="number"&&Pl(p,"number",p.value)}switch(P=u?cn(u):window,e){case"focusin":(Ds(P)||P.contentEditable==="true")&&(an=P,Al=u,qn=null);break;case"focusout":qn=Al=an=null;break;case"mousedown":Ul=!0;break;case"contextmenu":case"mouseup":case"dragend":Ul=!1,Bs(m,n,h);break;case"selectionchange":if(op)break;case"keydown":case"keyup":Bs(m,n,h)}var j;if(Ii)e:{switch(e){case"compositionstart":var T="onCompositionStart";break e;case"compositionend":T="onCompositionEnd";break e;case"compositionupdate":T="onCompositionUpdate";break e}T=void 0}else sn?Lu(e,n)&&(T="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(T="onCompositionStart");T&&(Ru&&n.locale!=="ko"&&(sn||T!=="onCompositionStart"?T==="onCompositionEnd"&&sn&&(j=Tu()):(Et=h,Mi="value"in Et?Et.value:Et.textContent,sn=!0)),P=fo(u,T),0<P.length&&(T=new Os(T,e,null,n,h),m.push({event:T,listeners:P}),j?T.data=j:(j=Mu(n),j!==null&&(T.data=j)))),(j=Qf?Gf(e,n):Yf(e,n))&&(u=fo(u,"onBeforeInput"),0<u.length&&(h=new Os("onBeforeInput","beforeinput",null,n,h),m.push({event:h,listeners:u}),h.data=j))}Hu(m,t)})}function pr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function fo(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,l=o.stateNode;o.tag===5&&l!==null&&(o=l,l=ir(e,n),l!=null&&r.unshift(pr(e,l,o)),l=ir(e,t),l!=null&&r.push(pr(e,l,o))),e=e.return}return r}function nn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Qs(e,t,n,r,o){for(var l=t._reactName,i=[];n!==null&&n!==r;){var s=n,a=s.alternate,u=s.stateNode;if(a!==null&&a===r)break;s.tag===5&&u!==null&&(s=u,o?(a=ir(n,l),a!=null&&i.unshift(pr(n,a,s))):o||(a=ir(n,l),a!=null&&i.push(pr(n,a,s)))),n=n.return}i.length!==0&&e.push({event:t,listeners:i})}var ap=/\r\n?/g,up=/\u0000|\uFFFD/g;function Gs(e){return(typeof e=="string"?e:""+e).replace(ap,`
`).replace(up,"")}function Fr(e,t,n){if(t=Gs(t),Gs(e)!==t&&n)throw Error(k(425))}function po(){}var $l=null,Vl=null;function Bl(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Wl=typeof setTimeout=="function"?setTimeout:void 0,cp=typeof clearTimeout=="function"?clearTimeout:void 0,Ys=typeof Promise=="function"?Promise:void 0,dp=typeof queueMicrotask=="function"?queueMicrotask:typeof Ys<"u"?function(e){return Ys.resolve(null).then(e).catch(fp)}:Wl;function fp(e){setTimeout(function(){throw e})}function al(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),ur(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);ur(t)}function jt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Ks(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Mn=Math.random().toString(36).slice(2),Je="__reactFiber$"+Mn,mr="__reactProps$"+Mn,ct="__reactContainer$"+Mn,Hl="__reactEvents$"+Mn,pp="__reactListeners$"+Mn,mp="__reactHandles$"+Mn;function Bt(e){var t=e[Je];if(t)return t;for(var n=e.parentNode;n;){if(t=n[ct]||n[Je]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Ks(e);e!==null;){if(n=e[Je])return n;e=Ks(e)}return t}e=n,n=e.parentNode}return null}function Er(e){return e=e[Je]||e[ct],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function cn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(k(33))}function bo(e){return e[mr]||null}var Ql=[],dn=-1;function Ft(e){return{current:e}}function Q(e){0>dn||(e.current=Ql[dn],Ql[dn]=null,dn--)}function B(e,t){dn++,Ql[dn]=e.current,e.current=t}var bt={},pe=Ft(bt),xe=Ft(!1),Kt=bt;function Nn(e,t){var n=e.type.contextTypes;if(!n)return bt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},l;for(l in n)o[l]=t[l];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function ke(e){return e=e.childContextTypes,e!=null}function mo(){Q(xe),Q(pe)}function Xs(e,t,n){if(pe.current!==bt)throw Error(k(168));B(pe,t),B(xe,n)}function Gu(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(k(108,Jd(e)||"Unknown",o));return X({},n,r)}function ho(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||bt,Kt=pe.current,B(pe,e),B(xe,xe.current),!0}function Js(e,t,n){var r=e.stateNode;if(!r)throw Error(k(169));n?(e=Gu(e,t,Kt),r.__reactInternalMemoizedMergedChildContext=e,Q(xe),Q(pe),B(pe,e)):Q(xe),B(xe,n)}var lt=null,Io=!1,ul=!1;function Yu(e){lt===null?lt=[e]:lt.push(e)}function hp(e){Io=!0,Yu(e)}function Dt(){if(!ul&&lt!==null){ul=!0;var e=0,t=$;try{var n=lt;for($=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}lt=null,Io=!1}catch(o){throw lt!==null&&(lt=lt.slice(e+1)),wu(ji,Dt),o}finally{$=t,ul=!1}}return null}var fn=[],pn=0,go=null,vo=0,Re=[],Le=0,Xt=null,it=1,st="";function Ut(e,t){fn[pn++]=vo,fn[pn++]=go,go=e,vo=t}function Ku(e,t,n){Re[Le++]=it,Re[Le++]=st,Re[Le++]=Xt,Xt=e;var r=it;e=st;var o=32-Ve(r)-1;r&=~(1<<o),n+=1;var l=32-Ve(t)+o;if(30<l){var i=o-o%5;l=(r&(1<<i)-1).toString(32),r>>=i,o-=i,it=1<<32-Ve(t)+o|n<<o|r,st=l+e}else it=1<<l|n<<o|r,st=e}function Di(e){e.return!==null&&(Ut(e,1),Ku(e,1,0))}function Ai(e){for(;e===go;)go=fn[--pn],fn[pn]=null,vo=fn[--pn],fn[pn]=null;for(;e===Xt;)Xt=Re[--Le],Re[Le]=null,st=Re[--Le],Re[Le]=null,it=Re[--Le],Re[Le]=null}var Ne=null,Ee=null,G=!1,$e=null;function Xu(e,t){var n=Me(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Zs(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Ne=e,Ee=jt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Ne=e,Ee=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Xt!==null?{id:it,overflow:st}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Me(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Ne=e,Ee=null,!0):!1;default:return!1}}function Gl(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Yl(e){if(G){var t=Ee;if(t){var n=t;if(!Zs(e,t)){if(Gl(e))throw Error(k(418));t=jt(n.nextSibling);var r=Ne;t&&Zs(e,t)?Xu(r,n):(e.flags=e.flags&-4097|2,G=!1,Ne=e)}}else{if(Gl(e))throw Error(k(418));e.flags=e.flags&-4097|2,G=!1,Ne=e}}}function qs(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Ne=e}function Dr(e){if(e!==Ne)return!1;if(!G)return qs(e),G=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Bl(e.type,e.memoizedProps)),t&&(t=Ee)){if(Gl(e))throw Ju(),Error(k(418));for(;t;)Xu(e,t),t=jt(t.nextSibling)}if(qs(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(k(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Ee=jt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Ee=null}}else Ee=Ne?jt(e.stateNode.nextSibling):null;return!0}function Ju(){for(var e=Ee;e;)e=jt(e.nextSibling)}function _n(){Ee=Ne=null,G=!1}function Ui(e){$e===null?$e=[e]:$e.push(e)}var gp=pt.ReactCurrentBatchConfig;function Un(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(k(309));var r=n.stateNode}if(!r)throw Error(k(147,e));var o=r,l=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===l?t.ref:(t=function(i){var s=o.refs;i===null?delete s[l]:s[l]=i},t._stringRef=l,t)}if(typeof e!="string")throw Error(k(284));if(!n._owner)throw Error(k(290,e))}return e}function Ar(e,t){throw e=Object.prototype.toString.call(t),Error(k(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function ea(e){var t=e._init;return t(e._payload)}function Zu(e){function t(d,c){if(e){var f=d.deletions;f===null?(d.deletions=[c],d.flags|=16):f.push(c)}}function n(d,c){if(!e)return null;for(;c!==null;)t(d,c),c=c.sibling;return null}function r(d,c){for(d=new Map;c!==null;)c.key!==null?d.set(c.key,c):d.set(c.index,c),c=c.sibling;return d}function o(d,c){return d=Mt(d,c),d.index=0,d.sibling=null,d}function l(d,c,f){return d.index=f,e?(f=d.alternate,f!==null?(f=f.index,f<c?(d.flags|=2,c):f):(d.flags|=2,c)):(d.flags|=1048576,c)}function i(d){return e&&d.alternate===null&&(d.flags|=2),d}function s(d,c,f,y){return c===null||c.tag!==6?(c=gl(f,d.mode,y),c.return=d,c):(c=o(c,f),c.return=d,c)}function a(d,c,f,y){var N=f.type;return N===ln?h(d,c,f.props.children,y,f.key):c!==null&&(c.elementType===N||typeof N=="object"&&N!==null&&N.$$typeof===xt&&ea(N)===c.type)?(y=o(c,f.props),y.ref=Un(d,c,f),y.return=d,y):(y=to(f.type,f.key,f.props,null,d.mode,y),y.ref=Un(d,c,f),y.return=d,y)}function u(d,c,f,y){return c===null||c.tag!==4||c.stateNode.containerInfo!==f.containerInfo||c.stateNode.implementation!==f.implementation?(c=vl(f,d.mode,y),c.return=d,c):(c=o(c,f.children||[]),c.return=d,c)}function h(d,c,f,y,N){return c===null||c.tag!==7?(c=Yt(f,d.mode,y,N),c.return=d,c):(c=o(c,f),c.return=d,c)}function m(d,c,f){if(typeof c=="string"&&c!==""||typeof c=="number")return c=gl(""+c,d.mode,f),c.return=d,c;if(typeof c=="object"&&c!==null){switch(c.$$typeof){case zr:return f=to(c.type,c.key,c.props,null,d.mode,f),f.ref=Un(d,null,c),f.return=d,f;case on:return c=vl(c,d.mode,f),c.return=d,c;case xt:var y=c._init;return m(d,y(c._payload),f)}if(Qn(c)||bn(c))return c=Yt(c,d.mode,f,null),c.return=d,c;Ar(d,c)}return null}function p(d,c,f,y){var N=c!==null?c.key:null;if(typeof f=="string"&&f!==""||typeof f=="number")return N!==null?null:s(d,c,""+f,y);if(typeof f=="object"&&f!==null){switch(f.$$typeof){case zr:return f.key===N?a(d,c,f,y):null;case on:return f.key===N?u(d,c,f,y):null;case xt:return N=f._init,p(d,c,N(f._payload),y)}if(Qn(f)||bn(f))return N!==null?null:h(d,c,f,y,null);Ar(d,f)}return null}function w(d,c,f,y,N){if(typeof y=="string"&&y!==""||typeof y=="number")return d=d.get(f)||null,s(c,d,""+y,N);if(typeof y=="object"&&y!==null){switch(y.$$typeof){case zr:return d=d.get(y.key===null?f:y.key)||null,a(c,d,y,N);case on:return d=d.get(y.key===null?f:y.key)||null,u(c,d,y,N);case xt:var P=y._init;return w(d,c,f,P(y._payload),N)}if(Qn(y)||bn(y))return d=d.get(f)||null,h(c,d,y,N,null);Ar(c,y)}return null}function x(d,c,f,y){for(var N=null,P=null,j=c,T=c=0,U=null;j!==null&&T<f.length;T++){j.index>T?(U=j,j=null):U=j.sibling;var M=p(d,j,f[T],y);if(M===null){j===null&&(j=U);break}e&&j&&M.alternate===null&&t(d,j),c=l(M,c,T),P===null?N=M:P.sibling=M,P=M,j=U}if(T===f.length)return n(d,j),G&&Ut(d,T),N;if(j===null){for(;T<f.length;T++)j=m(d,f[T],y),j!==null&&(c=l(j,c,T),P===null?N=j:P.sibling=j,P=j);return G&&Ut(d,T),N}for(j=r(d,j);T<f.length;T++)U=w(j,d,T,f[T],y),U!==null&&(e&&U.alternate!==null&&j.delete(U.key===null?T:U.key),c=l(U,c,T),P===null?N=U:P.sibling=U,P=U);return e&&j.forEach(function(ie){return t(d,ie)}),G&&Ut(d,T),N}function v(d,c,f,y){var N=bn(f);if(typeof N!="function")throw Error(k(150));if(f=N.call(f),f==null)throw Error(k(151));for(var P=N=null,j=c,T=c=0,U=null,M=f.next();j!==null&&!M.done;T++,M=f.next()){j.index>T?(U=j,j=null):U=j.sibling;var ie=p(d,j,M.value,y);if(ie===null){j===null&&(j=U);break}e&&j&&ie.alternate===null&&t(d,j),c=l(ie,c,T),P===null?N=ie:P.sibling=ie,P=ie,j=U}if(M.done)return n(d,j),G&&Ut(d,T),N;if(j===null){for(;!M.done;T++,M=f.next())M=m(d,M.value,y),M!==null&&(c=l(M,c,T),P===null?N=M:P.sibling=M,P=M);return G&&Ut(d,T),N}for(j=r(d,j);!M.done;T++,M=f.next())M=w(j,d,T,M.value,y),M!==null&&(e&&M.alternate!==null&&j.delete(M.key===null?T:M.key),c=l(M,c,T),P===null?N=M:P.sibling=M,P=M);return e&&j.forEach(function(D){return t(d,D)}),G&&Ut(d,T),N}function C(d,c,f,y){if(typeof f=="object"&&f!==null&&f.type===ln&&f.key===null&&(f=f.props.children),typeof f=="object"&&f!==null){switch(f.$$typeof){case zr:e:{for(var N=f.key,P=c;P!==null;){if(P.key===N){if(N=f.type,N===ln){if(P.tag===7){n(d,P.sibling),c=o(P,f.props.children),c.return=d,d=c;break e}}else if(P.elementType===N||typeof N=="object"&&N!==null&&N.$$typeof===xt&&ea(N)===P.type){n(d,P.sibling),c=o(P,f.props),c.ref=Un(d,P,f),c.return=d,d=c;break e}n(d,P);break}else t(d,P);P=P.sibling}f.type===ln?(c=Yt(f.props.children,d.mode,y,f.key),c.return=d,d=c):(y=to(f.type,f.key,f.props,null,d.mode,y),y.ref=Un(d,c,f),y.return=d,d=y)}return i(d);case on:e:{for(P=f.key;c!==null;){if(c.key===P)if(c.tag===4&&c.stateNode.containerInfo===f.containerInfo&&c.stateNode.implementation===f.implementation){n(d,c.sibling),c=o(c,f.children||[]),c.return=d,d=c;break e}else{n(d,c);break}else t(d,c);c=c.sibling}c=vl(f,d.mode,y),c.return=d,d=c}return i(d);case xt:return P=f._init,C(d,c,P(f._payload),y)}if(Qn(f))return x(d,c,f,y);if(bn(f))return v(d,c,f,y);Ar(d,f)}return typeof f=="string"&&f!==""||typeof f=="number"?(f=""+f,c!==null&&c.tag===6?(n(d,c.sibling),c=o(c,f),c.return=d,d=c):(n(d,c),c=gl(f,d.mode,y),c.return=d,d=c),i(d)):n(d,c)}return C}var Pn=Zu(!0),qu=Zu(!1),yo=Ft(null),wo=null,mn=null,$i=null;function Vi(){$i=mn=wo=null}function Bi(e){var t=yo.current;Q(yo),e._currentValue=t}function Kl(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function kn(e,t){wo=e,$i=mn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(we=!0),e.firstContext=null)}function be(e){var t=e._currentValue;if($i!==e)if(e={context:e,memoizedValue:t,next:null},mn===null){if(wo===null)throw Error(k(308));mn=e,wo.dependencies={lanes:0,firstContext:e}}else mn=mn.next=e;return t}var Wt=null;function Wi(e){Wt===null?Wt=[e]:Wt.push(e)}function ec(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,Wi(t)):(n.next=o.next,o.next=n),t.interleaved=n,dt(e,r)}function dt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var kt=!1;function Hi(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function tc(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function at(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Tt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,F&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,dt(e,n)}return o=r.interleaved,o===null?(t.next=t,Wi(r)):(t.next=o.next,o.next=t),r.interleaved=t,dt(e,n)}function Kr(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Ti(e,n)}}function ta(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,l=null;if(n=n.firstBaseUpdate,n!==null){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};l===null?o=l=i:l=l.next=i,n=n.next}while(n!==null);l===null?o=l=t:l=l.next=t}else o=l=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:l,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function xo(e,t,n,r){var o=e.updateQueue;kt=!1;var l=o.firstBaseUpdate,i=o.lastBaseUpdate,s=o.shared.pending;if(s!==null){o.shared.pending=null;var a=s,u=a.next;a.next=null,i===null?l=u:i.next=u,i=a;var h=e.alternate;h!==null&&(h=h.updateQueue,s=h.lastBaseUpdate,s!==i&&(s===null?h.firstBaseUpdate=u:s.next=u,h.lastBaseUpdate=a))}if(l!==null){var m=o.baseState;i=0,h=u=a=null,s=l;do{var p=s.lane,w=s.eventTime;if((r&p)===p){h!==null&&(h=h.next={eventTime:w,lane:0,tag:s.tag,payload:s.payload,callback:s.callback,next:null});e:{var x=e,v=s;switch(p=t,w=n,v.tag){case 1:if(x=v.payload,typeof x=="function"){m=x.call(w,m,p);break e}m=x;break e;case 3:x.flags=x.flags&-65537|128;case 0:if(x=v.payload,p=typeof x=="function"?x.call(w,m,p):x,p==null)break e;m=X({},m,p);break e;case 2:kt=!0}}s.callback!==null&&s.lane!==0&&(e.flags|=64,p=o.effects,p===null?o.effects=[s]:p.push(s))}else w={eventTime:w,lane:p,tag:s.tag,payload:s.payload,callback:s.callback,next:null},h===null?(u=h=w,a=m):h=h.next=w,i|=p;if(s=s.next,s===null){if(s=o.shared.pending,s===null)break;p=s,s=p.next,p.next=null,o.lastBaseUpdate=p,o.shared.pending=null}}while(!0);if(h===null&&(a=m),o.baseState=a,o.firstBaseUpdate=u,o.lastBaseUpdate=h,t=o.shared.interleaved,t!==null){o=t;do i|=o.lane,o=o.next;while(o!==t)}else l===null&&(o.shared.lanes=0);Zt|=i,e.lanes=i,e.memoizedState=m}}function na(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(k(191,o));o.call(r)}}}var Nr={},qe=Ft(Nr),hr=Ft(Nr),gr=Ft(Nr);function Ht(e){if(e===Nr)throw Error(k(174));return e}function Qi(e,t){switch(B(gr,t),B(hr,e),B(qe,Nr),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:jl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=jl(t,e)}Q(qe),B(qe,t)}function zn(){Q(qe),Q(hr),Q(gr)}function nc(e){Ht(gr.current);var t=Ht(qe.current),n=jl(t,e.type);t!==n&&(B(hr,e),B(qe,n))}function Gi(e){hr.current===e&&(Q(qe),Q(hr))}var Y=Ft(0);function ko(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var cl=[];function Yi(){for(var e=0;e<cl.length;e++)cl[e]._workInProgressVersionPrimary=null;cl.length=0}var Xr=pt.ReactCurrentDispatcher,dl=pt.ReactCurrentBatchConfig,Jt=0,K=null,ne=null,oe=null,So=!1,er=!1,vr=0,vp=0;function ce(){throw Error(k(321))}function Ki(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!We(e[n],t[n]))return!1;return!0}function Xi(e,t,n,r,o,l){if(Jt=l,K=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Xr.current=e===null||e.memoizedState===null?kp:Sp,e=n(r,o),er){l=0;do{if(er=!1,vr=0,25<=l)throw Error(k(301));l+=1,oe=ne=null,t.updateQueue=null,Xr.current=Cp,e=n(r,o)}while(er)}if(Xr.current=Co,t=ne!==null&&ne.next!==null,Jt=0,oe=ne=K=null,So=!1,t)throw Error(k(300));return e}function Ji(){var e=vr!==0;return vr=0,e}function Xe(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return oe===null?K.memoizedState=oe=e:oe=oe.next=e,oe}function Ie(){if(ne===null){var e=K.alternate;e=e!==null?e.memoizedState:null}else e=ne.next;var t=oe===null?K.memoizedState:oe.next;if(t!==null)oe=t,ne=e;else{if(e===null)throw Error(k(310));ne=e,e={memoizedState:ne.memoizedState,baseState:ne.baseState,baseQueue:ne.baseQueue,queue:ne.queue,next:null},oe===null?K.memoizedState=oe=e:oe=oe.next=e}return oe}function yr(e,t){return typeof t=="function"?t(e):t}function fl(e){var t=Ie(),n=t.queue;if(n===null)throw Error(k(311));n.lastRenderedReducer=e;var r=ne,o=r.baseQueue,l=n.pending;if(l!==null){if(o!==null){var i=o.next;o.next=l.next,l.next=i}r.baseQueue=o=l,n.pending=null}if(o!==null){l=o.next,r=r.baseState;var s=i=null,a=null,u=l;do{var h=u.lane;if((Jt&h)===h)a!==null&&(a=a.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var m={lane:h,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};a===null?(s=a=m,i=r):a=a.next=m,K.lanes|=h,Zt|=h}u=u.next}while(u!==null&&u!==l);a===null?i=r:a.next=s,We(r,t.memoizedState)||(we=!0),t.memoizedState=r,t.baseState=i,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do l=o.lane,K.lanes|=l,Zt|=l,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function pl(e){var t=Ie(),n=t.queue;if(n===null)throw Error(k(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,l=t.memoizedState;if(o!==null){n.pending=null;var i=o=o.next;do l=e(l,i.action),i=i.next;while(i!==o);We(l,t.memoizedState)||(we=!0),t.memoizedState=l,t.baseQueue===null&&(t.baseState=l),n.lastRenderedState=l}return[l,r]}function rc(){}function oc(e,t){var n=K,r=Ie(),o=t(),l=!We(r.memoizedState,o);if(l&&(r.memoizedState=o,we=!0),r=r.queue,Zi(sc.bind(null,n,r,e),[e]),r.getSnapshot!==t||l||oe!==null&&oe.memoizedState.tag&1){if(n.flags|=2048,wr(9,ic.bind(null,n,r,o,t),void 0,null),le===null)throw Error(k(349));Jt&30||lc(n,t,o)}return o}function lc(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=K.updateQueue,t===null?(t={lastEffect:null,stores:null},K.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function ic(e,t,n,r){t.value=n,t.getSnapshot=r,ac(t)&&uc(e)}function sc(e,t,n){return n(function(){ac(t)&&uc(e)})}function ac(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!We(e,n)}catch{return!0}}function uc(e){var t=dt(e,1);t!==null&&Be(t,e,1,-1)}function ra(e){var t=Xe();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:yr,lastRenderedState:e},t.queue=e,e=e.dispatch=xp.bind(null,K,e),[t.memoizedState,e]}function wr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=K.updateQueue,t===null?(t={lastEffect:null,stores:null},K.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function cc(){return Ie().memoizedState}function Jr(e,t,n,r){var o=Xe();K.flags|=e,o.memoizedState=wr(1|t,n,void 0,r===void 0?null:r)}function Fo(e,t,n,r){var o=Ie();r=r===void 0?null:r;var l=void 0;if(ne!==null){var i=ne.memoizedState;if(l=i.destroy,r!==null&&Ki(r,i.deps)){o.memoizedState=wr(t,n,l,r);return}}K.flags|=e,o.memoizedState=wr(1|t,n,l,r)}function oa(e,t){return Jr(8390656,8,e,t)}function Zi(e,t){return Fo(2048,8,e,t)}function dc(e,t){return Fo(4,2,e,t)}function fc(e,t){return Fo(4,4,e,t)}function pc(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function mc(e,t,n){return n=n!=null?n.concat([e]):null,Fo(4,4,pc.bind(null,t,e),n)}function qi(){}function hc(e,t){var n=Ie();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Ki(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function gc(e,t){var n=Ie();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Ki(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function vc(e,t,n){return Jt&21?(We(n,t)||(n=Su(),K.lanes|=n,Zt|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,we=!0),e.memoizedState=n)}function yp(e,t){var n=$;$=n!==0&&4>n?n:4,e(!0);var r=dl.transition;dl.transition={};try{e(!1),t()}finally{$=n,dl.transition=r}}function yc(){return Ie().memoizedState}function wp(e,t,n){var r=Lt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},wc(e))xc(t,n);else if(n=ec(e,t,n,r),n!==null){var o=he();Be(n,e,r,o),kc(n,t,r)}}function xp(e,t,n){var r=Lt(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(wc(e))xc(t,o);else{var l=e.alternate;if(e.lanes===0&&(l===null||l.lanes===0)&&(l=t.lastRenderedReducer,l!==null))try{var i=t.lastRenderedState,s=l(i,n);if(o.hasEagerState=!0,o.eagerState=s,We(s,i)){var a=t.interleaved;a===null?(o.next=o,Wi(t)):(o.next=a.next,a.next=o),t.interleaved=o;return}}catch{}finally{}n=ec(e,t,o,r),n!==null&&(o=he(),Be(n,e,r,o),kc(n,t,r))}}function wc(e){var t=e.alternate;return e===K||t!==null&&t===K}function xc(e,t){er=So=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function kc(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Ti(e,n)}}var Co={readContext:be,useCallback:ce,useContext:ce,useEffect:ce,useImperativeHandle:ce,useInsertionEffect:ce,useLayoutEffect:ce,useMemo:ce,useReducer:ce,useRef:ce,useState:ce,useDebugValue:ce,useDeferredValue:ce,useTransition:ce,useMutableSource:ce,useSyncExternalStore:ce,useId:ce,unstable_isNewReconciler:!1},kp={readContext:be,useCallback:function(e,t){return Xe().memoizedState=[e,t===void 0?null:t],e},useContext:be,useEffect:oa,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Jr(4194308,4,pc.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Jr(4194308,4,e,t)},useInsertionEffect:function(e,t){return Jr(4,2,e,t)},useMemo:function(e,t){var n=Xe();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Xe();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=wp.bind(null,K,e),[r.memoizedState,e]},useRef:function(e){var t=Xe();return e={current:e},t.memoizedState=e},useState:ra,useDebugValue:qi,useDeferredValue:function(e){return Xe().memoizedState=e},useTransition:function(){var e=ra(!1),t=e[0];return e=yp.bind(null,e[1]),Xe().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=K,o=Xe();if(G){if(n===void 0)throw Error(k(407));n=n()}else{if(n=t(),le===null)throw Error(k(349));Jt&30||lc(r,t,n)}o.memoizedState=n;var l={value:n,getSnapshot:t};return o.queue=l,oa(sc.bind(null,r,l,e),[e]),r.flags|=2048,wr(9,ic.bind(null,r,l,n,t),void 0,null),n},useId:function(){var e=Xe(),t=le.identifierPrefix;if(G){var n=st,r=it;n=(r&~(1<<32-Ve(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=vr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=vp++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Sp={readContext:be,useCallback:hc,useContext:be,useEffect:Zi,useImperativeHandle:mc,useInsertionEffect:dc,useLayoutEffect:fc,useMemo:gc,useReducer:fl,useRef:cc,useState:function(){return fl(yr)},useDebugValue:qi,useDeferredValue:function(e){var t=Ie();return vc(t,ne.memoizedState,e)},useTransition:function(){var e=fl(yr)[0],t=Ie().memoizedState;return[e,t]},useMutableSource:rc,useSyncExternalStore:oc,useId:yc,unstable_isNewReconciler:!1},Cp={readContext:be,useCallback:hc,useContext:be,useEffect:Zi,useImperativeHandle:mc,useInsertionEffect:dc,useLayoutEffect:fc,useMemo:gc,useReducer:pl,useRef:cc,useState:function(){return pl(yr)},useDebugValue:qi,useDeferredValue:function(e){var t=Ie();return ne===null?t.memoizedState=e:vc(t,ne.memoizedState,e)},useTransition:function(){var e=pl(yr)[0],t=Ie().memoizedState;return[e,t]},useMutableSource:rc,useSyncExternalStore:oc,useId:yc,unstable_isNewReconciler:!1};function Ae(e,t){if(e&&e.defaultProps){t=X({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Xl(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:X({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Do={isMounted:function(e){return(e=e._reactInternals)?tn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=he(),o=Lt(e),l=at(r,o);l.payload=t,n!=null&&(l.callback=n),t=Tt(e,l,o),t!==null&&(Be(t,e,o,r),Kr(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=he(),o=Lt(e),l=at(r,o);l.tag=1,l.payload=t,n!=null&&(l.callback=n),t=Tt(e,l,o),t!==null&&(Be(t,e,o,r),Kr(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=he(),r=Lt(e),o=at(n,r);o.tag=2,t!=null&&(o.callback=t),t=Tt(e,o,r),t!==null&&(Be(t,e,r,n),Kr(t,e,r))}};function la(e,t,n,r,o,l,i){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,l,i):t.prototype&&t.prototype.isPureReactComponent?!dr(n,r)||!dr(o,l):!0}function Sc(e,t,n){var r=!1,o=bt,l=t.contextType;return typeof l=="object"&&l!==null?l=be(l):(o=ke(t)?Kt:pe.current,r=t.contextTypes,l=(r=r!=null)?Nn(e,o):bt),t=new t(n,l),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Do,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=l),t}function ia(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Do.enqueueReplaceState(t,t.state,null)}function Jl(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},Hi(e);var l=t.contextType;typeof l=="object"&&l!==null?o.context=be(l):(l=ke(t)?Kt:pe.current,o.context=Nn(e,l)),o.state=e.memoizedState,l=t.getDerivedStateFromProps,typeof l=="function"&&(Xl(e,t,l,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&Do.enqueueReplaceState(o,o.state,null),xo(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function jn(e,t){try{var n="",r=t;do n+=Xd(r),r=r.return;while(r);var o=n}catch(l){o=`
Error generating stack: `+l.message+`
`+l.stack}return{value:e,source:t,stack:o,digest:null}}function ml(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Zl(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Ep=typeof WeakMap=="function"?WeakMap:Map;function Cc(e,t,n){n=at(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){No||(No=!0,ai=r),Zl(e,t)},n}function Ec(e,t,n){n=at(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){Zl(e,t)}}var l=e.stateNode;return l!==null&&typeof l.componentDidCatch=="function"&&(n.callback=function(){Zl(e,t),typeof r!="function"&&(Rt===null?Rt=new Set([this]):Rt.add(this));var i=t.stack;this.componentDidCatch(t.value,{componentStack:i!==null?i:""})}),n}function sa(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Ep;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=Dp.bind(null,e,t,n),t.then(e,e))}function aa(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function ua(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=at(-1,1),t.tag=2,Tt(n,t,1))),n.lanes|=1),e)}var Np=pt.ReactCurrentOwner,we=!1;function me(e,t,n,r){t.child=e===null?qu(t,null,n,r):Pn(t,e.child,n,r)}function ca(e,t,n,r,o){n=n.render;var l=t.ref;return kn(t,o),r=Xi(e,t,n,r,l,o),n=Ji(),e!==null&&!we?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,ft(e,t,o)):(G&&n&&Di(t),t.flags|=1,me(e,t,r,o),t.child)}function da(e,t,n,r,o){if(e===null){var l=n.type;return typeof l=="function"&&!ss(l)&&l.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=l,Nc(e,t,l,r,o)):(e=to(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(l=e.child,!(e.lanes&o)){var i=l.memoizedProps;if(n=n.compare,n=n!==null?n:dr,n(i,r)&&e.ref===t.ref)return ft(e,t,o)}return t.flags|=1,e=Mt(l,r),e.ref=t.ref,e.return=t,t.child=e}function Nc(e,t,n,r,o){if(e!==null){var l=e.memoizedProps;if(dr(l,r)&&e.ref===t.ref)if(we=!1,t.pendingProps=r=l,(e.lanes&o)!==0)e.flags&131072&&(we=!0);else return t.lanes=e.lanes,ft(e,t,o)}return ql(e,t,n,r,o)}function _c(e,t,n){var r=t.pendingProps,o=r.children,l=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},B(gn,Ce),Ce|=n;else{if(!(n&1073741824))return e=l!==null?l.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,B(gn,Ce),Ce|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=l!==null?l.baseLanes:n,B(gn,Ce),Ce|=r}else l!==null?(r=l.baseLanes|n,t.memoizedState=null):r=n,B(gn,Ce),Ce|=r;return me(e,t,o,n),t.child}function Pc(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function ql(e,t,n,r,o){var l=ke(n)?Kt:pe.current;return l=Nn(t,l),kn(t,o),n=Xi(e,t,n,r,l,o),r=Ji(),e!==null&&!we?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,ft(e,t,o)):(G&&r&&Di(t),t.flags|=1,me(e,t,n,o),t.child)}function fa(e,t,n,r,o){if(ke(n)){var l=!0;ho(t)}else l=!1;if(kn(t,o),t.stateNode===null)Zr(e,t),Sc(t,n,r),Jl(t,n,r,o),r=!0;else if(e===null){var i=t.stateNode,s=t.memoizedProps;i.props=s;var a=i.context,u=n.contextType;typeof u=="object"&&u!==null?u=be(u):(u=ke(n)?Kt:pe.current,u=Nn(t,u));var h=n.getDerivedStateFromProps,m=typeof h=="function"||typeof i.getSnapshotBeforeUpdate=="function";m||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(s!==r||a!==u)&&ia(t,i,r,u),kt=!1;var p=t.memoizedState;i.state=p,xo(t,r,i,o),a=t.memoizedState,s!==r||p!==a||xe.current||kt?(typeof h=="function"&&(Xl(t,n,h,r),a=t.memoizedState),(s=kt||la(t,n,s,r,p,a,u))?(m||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(t.flags|=4194308)):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),i.props=r,i.state=a,i.context=u,r=s):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{i=t.stateNode,tc(e,t),s=t.memoizedProps,u=t.type===t.elementType?s:Ae(t.type,s),i.props=u,m=t.pendingProps,p=i.context,a=n.contextType,typeof a=="object"&&a!==null?a=be(a):(a=ke(n)?Kt:pe.current,a=Nn(t,a));var w=n.getDerivedStateFromProps;(h=typeof w=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(s!==m||p!==a)&&ia(t,i,r,a),kt=!1,p=t.memoizedState,i.state=p,xo(t,r,i,o);var x=t.memoizedState;s!==m||p!==x||xe.current||kt?(typeof w=="function"&&(Xl(t,n,w,r),x=t.memoizedState),(u=kt||la(t,n,u,r,p,x,a)||!1)?(h||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(r,x,a),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(r,x,a)),typeof i.componentDidUpdate=="function"&&(t.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof i.componentDidUpdate!="function"||s===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=x),i.props=r,i.state=x,i.context=a,r=u):(typeof i.componentDidUpdate!="function"||s===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),r=!1)}return ei(e,t,n,r,l,o)}function ei(e,t,n,r,o,l){Pc(e,t);var i=(t.flags&128)!==0;if(!r&&!i)return o&&Js(t,n,!1),ft(e,t,l);r=t.stateNode,Np.current=t;var s=i&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&i?(t.child=Pn(t,e.child,null,l),t.child=Pn(t,null,s,l)):me(e,t,s,l),t.memoizedState=r.state,o&&Js(t,n,!0),t.child}function zc(e){var t=e.stateNode;t.pendingContext?Xs(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Xs(e,t.context,!1),Qi(e,t.containerInfo)}function pa(e,t,n,r,o){return _n(),Ui(o),t.flags|=256,me(e,t,n,r),t.child}var ti={dehydrated:null,treeContext:null,retryLane:0};function ni(e){return{baseLanes:e,cachePool:null,transitions:null}}function jc(e,t,n){var r=t.pendingProps,o=Y.current,l=!1,i=(t.flags&128)!==0,s;if((s=i)||(s=e!==null&&e.memoizedState===null?!1:(o&2)!==0),s?(l=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),B(Y,o&1),e===null)return Yl(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(i=r.children,e=r.fallback,l?(r=t.mode,l=t.child,i={mode:"hidden",children:i},!(r&1)&&l!==null?(l.childLanes=0,l.pendingProps=i):l=$o(i,r,0,null),e=Yt(e,r,n,null),l.return=t,e.return=t,l.sibling=e,t.child=l,t.child.memoizedState=ni(n),t.memoizedState=ti,e):es(t,i));if(o=e.memoizedState,o!==null&&(s=o.dehydrated,s!==null))return _p(e,t,i,r,s,o,n);if(l){l=r.fallback,i=t.mode,o=e.child,s=o.sibling;var a={mode:"hidden",children:r.children};return!(i&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=Mt(o,a),r.subtreeFlags=o.subtreeFlags&14680064),s!==null?l=Mt(s,l):(l=Yt(l,i,n,null),l.flags|=2),l.return=t,r.return=t,r.sibling=l,t.child=r,r=l,l=t.child,i=e.child.memoizedState,i=i===null?ni(n):{baseLanes:i.baseLanes|n,cachePool:null,transitions:i.transitions},l.memoizedState=i,l.childLanes=e.childLanes&~n,t.memoizedState=ti,r}return l=e.child,e=l.sibling,r=Mt(l,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function es(e,t){return t=$o({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Ur(e,t,n,r){return r!==null&&Ui(r),Pn(t,e.child,null,n),e=es(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function _p(e,t,n,r,o,l,i){if(n)return t.flags&256?(t.flags&=-257,r=ml(Error(k(422))),Ur(e,t,i,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(l=r.fallback,o=t.mode,r=$o({mode:"visible",children:r.children},o,0,null),l=Yt(l,o,i,null),l.flags|=2,r.return=t,l.return=t,r.sibling=l,t.child=r,t.mode&1&&Pn(t,e.child,null,i),t.child.memoizedState=ni(i),t.memoizedState=ti,l);if(!(t.mode&1))return Ur(e,t,i,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var s=r.dgst;return r=s,l=Error(k(419)),r=ml(l,r,void 0),Ur(e,t,i,r)}if(s=(i&e.childLanes)!==0,we||s){if(r=le,r!==null){switch(i&-i){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|i)?0:o,o!==0&&o!==l.retryLane&&(l.retryLane=o,dt(e,o),Be(r,e,o,-1))}return is(),r=ml(Error(k(421))),Ur(e,t,i,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=Ap.bind(null,e),o._reactRetry=t,null):(e=l.treeContext,Ee=jt(o.nextSibling),Ne=t,G=!0,$e=null,e!==null&&(Re[Le++]=it,Re[Le++]=st,Re[Le++]=Xt,it=e.id,st=e.overflow,Xt=t),t=es(t,r.children),t.flags|=4096,t)}function ma(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Kl(e.return,t,n)}function hl(e,t,n,r,o){var l=e.memoizedState;l===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(l.isBackwards=t,l.rendering=null,l.renderingStartTime=0,l.last=r,l.tail=n,l.tailMode=o)}function Tc(e,t,n){var r=t.pendingProps,o=r.revealOrder,l=r.tail;if(me(e,t,r.children,n),r=Y.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&ma(e,n,t);else if(e.tag===19)ma(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(B(Y,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&ko(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),hl(t,!1,o,n,l);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&ko(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}hl(t,!0,n,null,l);break;case"together":hl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Zr(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function ft(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Zt|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(k(153));if(t.child!==null){for(e=t.child,n=Mt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Mt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Pp(e,t,n){switch(t.tag){case 3:zc(t),_n();break;case 5:nc(t);break;case 1:ke(t.type)&&ho(t);break;case 4:Qi(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;B(yo,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(B(Y,Y.current&1),t.flags|=128,null):n&t.child.childLanes?jc(e,t,n):(B(Y,Y.current&1),e=ft(e,t,n),e!==null?e.sibling:null);B(Y,Y.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Tc(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),B(Y,Y.current),r)break;return null;case 22:case 23:return t.lanes=0,_c(e,t,n)}return ft(e,t,n)}var Rc,ri,Lc,Mc;Rc=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};ri=function(){};Lc=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,Ht(qe.current);var l=null;switch(n){case"input":o=Nl(e,o),r=Nl(e,r),l=[];break;case"select":o=X({},o,{value:void 0}),r=X({},r,{value:void 0}),l=[];break;case"textarea":o=zl(e,o),r=zl(e,r),l=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=po)}Tl(n,r);var i;n=null;for(u in o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&o[u]!=null)if(u==="style"){var s=o[u];for(i in s)s.hasOwnProperty(i)&&(n||(n={}),n[i]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(or.hasOwnProperty(u)?l||(l=[]):(l=l||[]).push(u,null));for(u in r){var a=r[u];if(s=o!=null?o[u]:void 0,r.hasOwnProperty(u)&&a!==s&&(a!=null||s!=null))if(u==="style")if(s){for(i in s)!s.hasOwnProperty(i)||a&&a.hasOwnProperty(i)||(n||(n={}),n[i]="");for(i in a)a.hasOwnProperty(i)&&s[i]!==a[i]&&(n||(n={}),n[i]=a[i])}else n||(l||(l=[]),l.push(u,n)),n=a;else u==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,s=s?s.__html:void 0,a!=null&&s!==a&&(l=l||[]).push(u,a)):u==="children"?typeof a!="string"&&typeof a!="number"||(l=l||[]).push(u,""+a):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(or.hasOwnProperty(u)?(a!=null&&u==="onScroll"&&H("scroll",e),l||s===a||(l=[])):(l=l||[]).push(u,a))}n&&(l=l||[]).push("style",n);var u=l;(t.updateQueue=u)&&(t.flags|=4)}};Mc=function(e,t,n,r){n!==r&&(t.flags|=4)};function $n(e,t){if(!G)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function de(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function zp(e,t,n){var r=t.pendingProps;switch(Ai(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return de(t),null;case 1:return ke(t.type)&&mo(),de(t),null;case 3:return r=t.stateNode,zn(),Q(xe),Q(pe),Yi(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Dr(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,$e!==null&&(di($e),$e=null))),ri(e,t),de(t),null;case 5:Gi(t);var o=Ht(gr.current);if(n=t.type,e!==null&&t.stateNode!=null)Lc(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(k(166));return de(t),null}if(e=Ht(qe.current),Dr(t)){r=t.stateNode,n=t.type;var l=t.memoizedProps;switch(r[Je]=t,r[mr]=l,e=(t.mode&1)!==0,n){case"dialog":H("cancel",r),H("close",r);break;case"iframe":case"object":case"embed":H("load",r);break;case"video":case"audio":for(o=0;o<Yn.length;o++)H(Yn[o],r);break;case"source":H("error",r);break;case"img":case"image":case"link":H("error",r),H("load",r);break;case"details":H("toggle",r);break;case"input":Cs(r,l),H("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!l.multiple},H("invalid",r);break;case"textarea":Ns(r,l),H("invalid",r)}Tl(n,l),o=null;for(var i in l)if(l.hasOwnProperty(i)){var s=l[i];i==="children"?typeof s=="string"?r.textContent!==s&&(l.suppressHydrationWarning!==!0&&Fr(r.textContent,s,e),o=["children",s]):typeof s=="number"&&r.textContent!==""+s&&(l.suppressHydrationWarning!==!0&&Fr(r.textContent,s,e),o=["children",""+s]):or.hasOwnProperty(i)&&s!=null&&i==="onScroll"&&H("scroll",r)}switch(n){case"input":jr(r),Es(r,l,!0);break;case"textarea":jr(r),_s(r);break;case"select":case"option":break;default:typeof l.onClick=="function"&&(r.onclick=po)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{i=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=su(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=i.createElement(n,{is:r.is}):(e=i.createElement(n),n==="select"&&(i=e,r.multiple?i.multiple=!0:r.size&&(i.size=r.size))):e=i.createElementNS(e,n),e[Je]=t,e[mr]=r,Rc(e,t,!1,!1),t.stateNode=e;e:{switch(i=Rl(n,r),n){case"dialog":H("cancel",e),H("close",e),o=r;break;case"iframe":case"object":case"embed":H("load",e),o=r;break;case"video":case"audio":for(o=0;o<Yn.length;o++)H(Yn[o],e);o=r;break;case"source":H("error",e),o=r;break;case"img":case"image":case"link":H("error",e),H("load",e),o=r;break;case"details":H("toggle",e),o=r;break;case"input":Cs(e,r),o=Nl(e,r),H("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=X({},r,{value:void 0}),H("invalid",e);break;case"textarea":Ns(e,r),o=zl(e,r),H("invalid",e);break;default:o=r}Tl(n,o),s=o;for(l in s)if(s.hasOwnProperty(l)){var a=s[l];l==="style"?cu(e,a):l==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&au(e,a)):l==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&lr(e,a):typeof a=="number"&&lr(e,""+a):l!=="suppressContentEditableWarning"&&l!=="suppressHydrationWarning"&&l!=="autoFocus"&&(or.hasOwnProperty(l)?a!=null&&l==="onScroll"&&H("scroll",e):a!=null&&Ei(e,l,a,i))}switch(n){case"input":jr(e),Es(e,r,!1);break;case"textarea":jr(e),_s(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Ot(r.value));break;case"select":e.multiple=!!r.multiple,l=r.value,l!=null?vn(e,!!r.multiple,l,!1):r.defaultValue!=null&&vn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=po)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return de(t),null;case 6:if(e&&t.stateNode!=null)Mc(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(k(166));if(n=Ht(gr.current),Ht(qe.current),Dr(t)){if(r=t.stateNode,n=t.memoizedProps,r[Je]=t,(l=r.nodeValue!==n)&&(e=Ne,e!==null))switch(e.tag){case 3:Fr(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Fr(r.nodeValue,n,(e.mode&1)!==0)}l&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Je]=t,t.stateNode=r}return de(t),null;case 13:if(Q(Y),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(G&&Ee!==null&&t.mode&1&&!(t.flags&128))Ju(),_n(),t.flags|=98560,l=!1;else if(l=Dr(t),r!==null&&r.dehydrated!==null){if(e===null){if(!l)throw Error(k(318));if(l=t.memoizedState,l=l!==null?l.dehydrated:null,!l)throw Error(k(317));l[Je]=t}else _n(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;de(t),l=!1}else $e!==null&&(di($e),$e=null),l=!0;if(!l)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||Y.current&1?re===0&&(re=3):is())),t.updateQueue!==null&&(t.flags|=4),de(t),null);case 4:return zn(),ri(e,t),e===null&&fr(t.stateNode.containerInfo),de(t),null;case 10:return Bi(t.type._context),de(t),null;case 17:return ke(t.type)&&mo(),de(t),null;case 19:if(Q(Y),l=t.memoizedState,l===null)return de(t),null;if(r=(t.flags&128)!==0,i=l.rendering,i===null)if(r)$n(l,!1);else{if(re!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(i=ko(e),i!==null){for(t.flags|=128,$n(l,!1),r=i.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)l=n,e=r,l.flags&=14680066,i=l.alternate,i===null?(l.childLanes=0,l.lanes=e,l.child=null,l.subtreeFlags=0,l.memoizedProps=null,l.memoizedState=null,l.updateQueue=null,l.dependencies=null,l.stateNode=null):(l.childLanes=i.childLanes,l.lanes=i.lanes,l.child=i.child,l.subtreeFlags=0,l.deletions=null,l.memoizedProps=i.memoizedProps,l.memoizedState=i.memoizedState,l.updateQueue=i.updateQueue,l.type=i.type,e=i.dependencies,l.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return B(Y,Y.current&1|2),t.child}e=e.sibling}l.tail!==null&&q()>Tn&&(t.flags|=128,r=!0,$n(l,!1),t.lanes=4194304)}else{if(!r)if(e=ko(i),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),$n(l,!0),l.tail===null&&l.tailMode==="hidden"&&!i.alternate&&!G)return de(t),null}else 2*q()-l.renderingStartTime>Tn&&n!==1073741824&&(t.flags|=128,r=!0,$n(l,!1),t.lanes=4194304);l.isBackwards?(i.sibling=t.child,t.child=i):(n=l.last,n!==null?n.sibling=i:t.child=i,l.last=i)}return l.tail!==null?(t=l.tail,l.rendering=t,l.tail=t.sibling,l.renderingStartTime=q(),t.sibling=null,n=Y.current,B(Y,r?n&1|2:n&1),t):(de(t),null);case 22:case 23:return ls(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ce&1073741824&&(de(t),t.subtreeFlags&6&&(t.flags|=8192)):de(t),null;case 24:return null;case 25:return null}throw Error(k(156,t.tag))}function jp(e,t){switch(Ai(t),t.tag){case 1:return ke(t.type)&&mo(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return zn(),Q(xe),Q(pe),Yi(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Gi(t),null;case 13:if(Q(Y),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(k(340));_n()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return Q(Y),null;case 4:return zn(),null;case 10:return Bi(t.type._context),null;case 22:case 23:return ls(),null;case 24:return null;default:return null}}var $r=!1,fe=!1,Tp=typeof WeakSet=="function"?WeakSet:Set,z=null;function hn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){J(e,t,r)}else n.current=null}function oi(e,t,n){try{n()}catch(r){J(e,t,r)}}var ha=!1;function Rp(e,t){if($l=uo,e=Du(),Fi(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,l=r.focusNode;r=r.focusOffset;try{n.nodeType,l.nodeType}catch{n=null;break e}var i=0,s=-1,a=-1,u=0,h=0,m=e,p=null;t:for(;;){for(var w;m!==n||o!==0&&m.nodeType!==3||(s=i+o),m!==l||r!==0&&m.nodeType!==3||(a=i+r),m.nodeType===3&&(i+=m.nodeValue.length),(w=m.firstChild)!==null;)p=m,m=w;for(;;){if(m===e)break t;if(p===n&&++u===o&&(s=i),p===l&&++h===r&&(a=i),(w=m.nextSibling)!==null)break;m=p,p=m.parentNode}m=w}n=s===-1||a===-1?null:{start:s,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(Vl={focusedElem:e,selectionRange:n},uo=!1,z=t;z!==null;)if(t=z,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,z=e;else for(;z!==null;){t=z;try{var x=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(x!==null){var v=x.memoizedProps,C=x.memoizedState,d=t.stateNode,c=d.getSnapshotBeforeUpdate(t.elementType===t.type?v:Ae(t.type,v),C);d.__reactInternalSnapshotBeforeUpdate=c}break;case 3:var f=t.stateNode.containerInfo;f.nodeType===1?f.textContent="":f.nodeType===9&&f.documentElement&&f.removeChild(f.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(k(163))}}catch(y){J(t,t.return,y)}if(e=t.sibling,e!==null){e.return=t.return,z=e;break}z=t.return}return x=ha,ha=!1,x}function tr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var l=o.destroy;o.destroy=void 0,l!==void 0&&oi(t,n,l)}o=o.next}while(o!==r)}}function Ao(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function li(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Oc(e){var t=e.alternate;t!==null&&(e.alternate=null,Oc(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Je],delete t[mr],delete t[Hl],delete t[pp],delete t[mp])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function bc(e){return e.tag===5||e.tag===3||e.tag===4}function ga(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||bc(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function ii(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=po));else if(r!==4&&(e=e.child,e!==null))for(ii(e,t,n),e=e.sibling;e!==null;)ii(e,t,n),e=e.sibling}function si(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(si(e,t,n),e=e.sibling;e!==null;)si(e,t,n),e=e.sibling}var se=null,Ue=!1;function gt(e,t,n){for(n=n.child;n!==null;)Ic(e,t,n),n=n.sibling}function Ic(e,t,n){if(Ze&&typeof Ze.onCommitFiberUnmount=="function")try{Ze.onCommitFiberUnmount(Ro,n)}catch{}switch(n.tag){case 5:fe||hn(n,t);case 6:var r=se,o=Ue;se=null,gt(e,t,n),se=r,Ue=o,se!==null&&(Ue?(e=se,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):se.removeChild(n.stateNode));break;case 18:se!==null&&(Ue?(e=se,n=n.stateNode,e.nodeType===8?al(e.parentNode,n):e.nodeType===1&&al(e,n),ur(e)):al(se,n.stateNode));break;case 4:r=se,o=Ue,se=n.stateNode.containerInfo,Ue=!0,gt(e,t,n),se=r,Ue=o;break;case 0:case 11:case 14:case 15:if(!fe&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var l=o,i=l.destroy;l=l.tag,i!==void 0&&(l&2||l&4)&&oi(n,t,i),o=o.next}while(o!==r)}gt(e,t,n);break;case 1:if(!fe&&(hn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(s){J(n,t,s)}gt(e,t,n);break;case 21:gt(e,t,n);break;case 22:n.mode&1?(fe=(r=fe)||n.memoizedState!==null,gt(e,t,n),fe=r):gt(e,t,n);break;default:gt(e,t,n)}}function va(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Tp),t.forEach(function(r){var o=Up.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function Fe(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var l=e,i=t,s=i;e:for(;s!==null;){switch(s.tag){case 5:se=s.stateNode,Ue=!1;break e;case 3:se=s.stateNode.containerInfo,Ue=!0;break e;case 4:se=s.stateNode.containerInfo,Ue=!0;break e}s=s.return}if(se===null)throw Error(k(160));Ic(l,i,o),se=null,Ue=!1;var a=o.alternate;a!==null&&(a.return=null),o.return=null}catch(u){J(o,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Fc(t,e),t=t.sibling}function Fc(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Fe(t,e),Ye(e),r&4){try{tr(3,e,e.return),Ao(3,e)}catch(v){J(e,e.return,v)}try{tr(5,e,e.return)}catch(v){J(e,e.return,v)}}break;case 1:Fe(t,e),Ye(e),r&512&&n!==null&&hn(n,n.return);break;case 5:if(Fe(t,e),Ye(e),r&512&&n!==null&&hn(n,n.return),e.flags&32){var o=e.stateNode;try{lr(o,"")}catch(v){J(e,e.return,v)}}if(r&4&&(o=e.stateNode,o!=null)){var l=e.memoizedProps,i=n!==null?n.memoizedProps:l,s=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{s==="input"&&l.type==="radio"&&l.name!=null&&lu(o,l),Rl(s,i);var u=Rl(s,l);for(i=0;i<a.length;i+=2){var h=a[i],m=a[i+1];h==="style"?cu(o,m):h==="dangerouslySetInnerHTML"?au(o,m):h==="children"?lr(o,m):Ei(o,h,m,u)}switch(s){case"input":_l(o,l);break;case"textarea":iu(o,l);break;case"select":var p=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!l.multiple;var w=l.value;w!=null?vn(o,!!l.multiple,w,!1):p!==!!l.multiple&&(l.defaultValue!=null?vn(o,!!l.multiple,l.defaultValue,!0):vn(o,!!l.multiple,l.multiple?[]:"",!1))}o[mr]=l}catch(v){J(e,e.return,v)}}break;case 6:if(Fe(t,e),Ye(e),r&4){if(e.stateNode===null)throw Error(k(162));o=e.stateNode,l=e.memoizedProps;try{o.nodeValue=l}catch(v){J(e,e.return,v)}}break;case 3:if(Fe(t,e),Ye(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{ur(t.containerInfo)}catch(v){J(e,e.return,v)}break;case 4:Fe(t,e),Ye(e);break;case 13:Fe(t,e),Ye(e),o=e.child,o.flags&8192&&(l=o.memoizedState!==null,o.stateNode.isHidden=l,!l||o.alternate!==null&&o.alternate.memoizedState!==null||(rs=q())),r&4&&va(e);break;case 22:if(h=n!==null&&n.memoizedState!==null,e.mode&1?(fe=(u=fe)||h,Fe(t,e),fe=u):Fe(t,e),Ye(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!h&&e.mode&1)for(z=e,h=e.child;h!==null;){for(m=z=h;z!==null;){switch(p=z,w=p.child,p.tag){case 0:case 11:case 14:case 15:tr(4,p,p.return);break;case 1:hn(p,p.return);var x=p.stateNode;if(typeof x.componentWillUnmount=="function"){r=p,n=p.return;try{t=r,x.props=t.memoizedProps,x.state=t.memoizedState,x.componentWillUnmount()}catch(v){J(r,n,v)}}break;case 5:hn(p,p.return);break;case 22:if(p.memoizedState!==null){wa(m);continue}}w!==null?(w.return=p,z=w):wa(m)}h=h.sibling}e:for(h=null,m=e;;){if(m.tag===5){if(h===null){h=m;try{o=m.stateNode,u?(l=o.style,typeof l.setProperty=="function"?l.setProperty("display","none","important"):l.display="none"):(s=m.stateNode,a=m.memoizedProps.style,i=a!=null&&a.hasOwnProperty("display")?a.display:null,s.style.display=uu("display",i))}catch(v){J(e,e.return,v)}}}else if(m.tag===6){if(h===null)try{m.stateNode.nodeValue=u?"":m.memoizedProps}catch(v){J(e,e.return,v)}}else if((m.tag!==22&&m.tag!==23||m.memoizedState===null||m===e)&&m.child!==null){m.child.return=m,m=m.child;continue}if(m===e)break e;for(;m.sibling===null;){if(m.return===null||m.return===e)break e;h===m&&(h=null),m=m.return}h===m&&(h=null),m.sibling.return=m.return,m=m.sibling}}break;case 19:Fe(t,e),Ye(e),r&4&&va(e);break;case 21:break;default:Fe(t,e),Ye(e)}}function Ye(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(bc(n)){var r=n;break e}n=n.return}throw Error(k(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(lr(o,""),r.flags&=-33);var l=ga(e);si(e,l,o);break;case 3:case 4:var i=r.stateNode.containerInfo,s=ga(e);ii(e,s,i);break;default:throw Error(k(161))}}catch(a){J(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Lp(e,t,n){z=e,Dc(e)}function Dc(e,t,n){for(var r=(e.mode&1)!==0;z!==null;){var o=z,l=o.child;if(o.tag===22&&r){var i=o.memoizedState!==null||$r;if(!i){var s=o.alternate,a=s!==null&&s.memoizedState!==null||fe;s=$r;var u=fe;if($r=i,(fe=a)&&!u)for(z=o;z!==null;)i=z,a=i.child,i.tag===22&&i.memoizedState!==null?xa(o):a!==null?(a.return=i,z=a):xa(o);for(;l!==null;)z=l,Dc(l),l=l.sibling;z=o,$r=s,fe=u}ya(e)}else o.subtreeFlags&8772&&l!==null?(l.return=o,z=l):ya(e)}}function ya(e){for(;z!==null;){var t=z;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:fe||Ao(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!fe)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:Ae(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var l=t.updateQueue;l!==null&&na(t,l,r);break;case 3:var i=t.updateQueue;if(i!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}na(t,i,n)}break;case 5:var s=t.stateNode;if(n===null&&t.flags&4){n=s;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var h=u.memoizedState;if(h!==null){var m=h.dehydrated;m!==null&&ur(m)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(k(163))}fe||t.flags&512&&li(t)}catch(p){J(t,t.return,p)}}if(t===e){z=null;break}if(n=t.sibling,n!==null){n.return=t.return,z=n;break}z=t.return}}function wa(e){for(;z!==null;){var t=z;if(t===e){z=null;break}var n=t.sibling;if(n!==null){n.return=t.return,z=n;break}z=t.return}}function xa(e){for(;z!==null;){var t=z;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Ao(4,t)}catch(a){J(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(a){J(t,o,a)}}var l=t.return;try{li(t)}catch(a){J(t,l,a)}break;case 5:var i=t.return;try{li(t)}catch(a){J(t,i,a)}}}catch(a){J(t,t.return,a)}if(t===e){z=null;break}var s=t.sibling;if(s!==null){s.return=t.return,z=s;break}z=t.return}}var Mp=Math.ceil,Eo=pt.ReactCurrentDispatcher,ts=pt.ReactCurrentOwner,Oe=pt.ReactCurrentBatchConfig,F=0,le=null,te=null,ae=0,Ce=0,gn=Ft(0),re=0,xr=null,Zt=0,Uo=0,ns=0,nr=null,ye=null,rs=0,Tn=1/0,ot=null,No=!1,ai=null,Rt=null,Vr=!1,Nt=null,_o=0,rr=0,ui=null,qr=-1,eo=0;function he(){return F&6?q():qr!==-1?qr:qr=q()}function Lt(e){return e.mode&1?F&2&&ae!==0?ae&-ae:gp.transition!==null?(eo===0&&(eo=Su()),eo):(e=$,e!==0||(e=window.event,e=e===void 0?16:ju(e.type)),e):1}function Be(e,t,n,r){if(50<rr)throw rr=0,ui=null,Error(k(185));Sr(e,n,r),(!(F&2)||e!==le)&&(e===le&&(!(F&2)&&(Uo|=n),re===4&&Ct(e,ae)),Se(e,r),n===1&&F===0&&!(t.mode&1)&&(Tn=q()+500,Io&&Dt()))}function Se(e,t){var n=e.callbackNode;gf(e,t);var r=ao(e,e===le?ae:0);if(r===0)n!==null&&js(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&js(n),t===1)e.tag===0?hp(ka.bind(null,e)):Yu(ka.bind(null,e)),dp(function(){!(F&6)&&Dt()}),n=null;else{switch(Cu(r)){case 1:n=ji;break;case 4:n=xu;break;case 16:n=so;break;case 536870912:n=ku;break;default:n=so}n=Qc(n,Ac.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Ac(e,t){if(qr=-1,eo=0,F&6)throw Error(k(327));var n=e.callbackNode;if(Sn()&&e.callbackNode!==n)return null;var r=ao(e,e===le?ae:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Po(e,r);else{t=r;var o=F;F|=2;var l=$c();(le!==e||ae!==t)&&(ot=null,Tn=q()+500,Gt(e,t));do try{Ip();break}catch(s){Uc(e,s)}while(!0);Vi(),Eo.current=l,F=o,te!==null?t=0:(le=null,ae=0,t=re)}if(t!==0){if(t===2&&(o=Il(e),o!==0&&(r=o,t=ci(e,o))),t===1)throw n=xr,Gt(e,0),Ct(e,r),Se(e,q()),n;if(t===6)Ct(e,r);else{if(o=e.current.alternate,!(r&30)&&!Op(o)&&(t=Po(e,r),t===2&&(l=Il(e),l!==0&&(r=l,t=ci(e,l))),t===1))throw n=xr,Gt(e,0),Ct(e,r),Se(e,q()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(k(345));case 2:$t(e,ye,ot);break;case 3:if(Ct(e,r),(r&130023424)===r&&(t=rs+500-q(),10<t)){if(ao(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){he(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=Wl($t.bind(null,e,ye,ot),t);break}$t(e,ye,ot);break;case 4:if(Ct(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var i=31-Ve(r);l=1<<i,i=t[i],i>o&&(o=i),r&=~l}if(r=o,r=q()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Mp(r/1960))-r,10<r){e.timeoutHandle=Wl($t.bind(null,e,ye,ot),r);break}$t(e,ye,ot);break;case 5:$t(e,ye,ot);break;default:throw Error(k(329))}}}return Se(e,q()),e.callbackNode===n?Ac.bind(null,e):null}function ci(e,t){var n=nr;return e.current.memoizedState.isDehydrated&&(Gt(e,t).flags|=256),e=Po(e,t),e!==2&&(t=ye,ye=n,t!==null&&di(t)),e}function di(e){ye===null?ye=e:ye.push.apply(ye,e)}function Op(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],l=o.getSnapshot;o=o.value;try{if(!We(l(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Ct(e,t){for(t&=~ns,t&=~Uo,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Ve(t),r=1<<n;e[n]=-1,t&=~r}}function ka(e){if(F&6)throw Error(k(327));Sn();var t=ao(e,0);if(!(t&1))return Se(e,q()),null;var n=Po(e,t);if(e.tag!==0&&n===2){var r=Il(e);r!==0&&(t=r,n=ci(e,r))}if(n===1)throw n=xr,Gt(e,0),Ct(e,t),Se(e,q()),n;if(n===6)throw Error(k(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,$t(e,ye,ot),Se(e,q()),null}function os(e,t){var n=F;F|=1;try{return e(t)}finally{F=n,F===0&&(Tn=q()+500,Io&&Dt())}}function qt(e){Nt!==null&&Nt.tag===0&&!(F&6)&&Sn();var t=F;F|=1;var n=Oe.transition,r=$;try{if(Oe.transition=null,$=1,e)return e()}finally{$=r,Oe.transition=n,F=t,!(F&6)&&Dt()}}function ls(){Ce=gn.current,Q(gn)}function Gt(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,cp(n)),te!==null)for(n=te.return;n!==null;){var r=n;switch(Ai(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&mo();break;case 3:zn(),Q(xe),Q(pe),Yi();break;case 5:Gi(r);break;case 4:zn();break;case 13:Q(Y);break;case 19:Q(Y);break;case 10:Bi(r.type._context);break;case 22:case 23:ls()}n=n.return}if(le=e,te=e=Mt(e.current,null),ae=Ce=t,re=0,xr=null,ns=Uo=Zt=0,ye=nr=null,Wt!==null){for(t=0;t<Wt.length;t++)if(n=Wt[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,l=n.pending;if(l!==null){var i=l.next;l.next=o,r.next=i}n.pending=r}Wt=null}return e}function Uc(e,t){do{var n=te;try{if(Vi(),Xr.current=Co,So){for(var r=K.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}So=!1}if(Jt=0,oe=ne=K=null,er=!1,vr=0,ts.current=null,n===null||n.return===null){re=1,xr=t,te=null;break}e:{var l=e,i=n.return,s=n,a=t;if(t=ae,s.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var u=a,h=s,m=h.tag;if(!(h.mode&1)&&(m===0||m===11||m===15)){var p=h.alternate;p?(h.updateQueue=p.updateQueue,h.memoizedState=p.memoizedState,h.lanes=p.lanes):(h.updateQueue=null,h.memoizedState=null)}var w=aa(i);if(w!==null){w.flags&=-257,ua(w,i,s,l,t),w.mode&1&&sa(l,u,t),t=w,a=u;var x=t.updateQueue;if(x===null){var v=new Set;v.add(a),t.updateQueue=v}else x.add(a);break e}else{if(!(t&1)){sa(l,u,t),is();break e}a=Error(k(426))}}else if(G&&s.mode&1){var C=aa(i);if(C!==null){!(C.flags&65536)&&(C.flags|=256),ua(C,i,s,l,t),Ui(jn(a,s));break e}}l=a=jn(a,s),re!==4&&(re=2),nr===null?nr=[l]:nr.push(l),l=i;do{switch(l.tag){case 3:l.flags|=65536,t&=-t,l.lanes|=t;var d=Cc(l,a,t);ta(l,d);break e;case 1:s=a;var c=l.type,f=l.stateNode;if(!(l.flags&128)&&(typeof c.getDerivedStateFromError=="function"||f!==null&&typeof f.componentDidCatch=="function"&&(Rt===null||!Rt.has(f)))){l.flags|=65536,t&=-t,l.lanes|=t;var y=Ec(l,s,t);ta(l,y);break e}}l=l.return}while(l!==null)}Bc(n)}catch(N){t=N,te===n&&n!==null&&(te=n=n.return);continue}break}while(!0)}function $c(){var e=Eo.current;return Eo.current=Co,e===null?Co:e}function is(){(re===0||re===3||re===2)&&(re=4),le===null||!(Zt&268435455)&&!(Uo&268435455)||Ct(le,ae)}function Po(e,t){var n=F;F|=2;var r=$c();(le!==e||ae!==t)&&(ot=null,Gt(e,t));do try{bp();break}catch(o){Uc(e,o)}while(!0);if(Vi(),F=n,Eo.current=r,te!==null)throw Error(k(261));return le=null,ae=0,re}function bp(){for(;te!==null;)Vc(te)}function Ip(){for(;te!==null&&!sf();)Vc(te)}function Vc(e){var t=Hc(e.alternate,e,Ce);e.memoizedProps=e.pendingProps,t===null?Bc(e):te=t,ts.current=null}function Bc(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=jp(n,t),n!==null){n.flags&=32767,te=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{re=6,te=null;return}}else if(n=zp(n,t,Ce),n!==null){te=n;return}if(t=t.sibling,t!==null){te=t;return}te=t=e}while(t!==null);re===0&&(re=5)}function $t(e,t,n){var r=$,o=Oe.transition;try{Oe.transition=null,$=1,Fp(e,t,n,r)}finally{Oe.transition=o,$=r}return null}function Fp(e,t,n,r){do Sn();while(Nt!==null);if(F&6)throw Error(k(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(k(177));e.callbackNode=null,e.callbackPriority=0;var l=n.lanes|n.childLanes;if(vf(e,l),e===le&&(te=le=null,ae=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Vr||(Vr=!0,Qc(so,function(){return Sn(),null})),l=(n.flags&15990)!==0,n.subtreeFlags&15990||l){l=Oe.transition,Oe.transition=null;var i=$;$=1;var s=F;F|=4,ts.current=null,Rp(e,n),Fc(n,e),rp(Vl),uo=!!$l,Vl=$l=null,e.current=n,Lp(n),af(),F=s,$=i,Oe.transition=l}else e.current=n;if(Vr&&(Vr=!1,Nt=e,_o=o),l=e.pendingLanes,l===0&&(Rt=null),df(n.stateNode),Se(e,q()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(No)throw No=!1,e=ai,ai=null,e;return _o&1&&e.tag!==0&&Sn(),l=e.pendingLanes,l&1?e===ui?rr++:(rr=0,ui=e):rr=0,Dt(),null}function Sn(){if(Nt!==null){var e=Cu(_o),t=Oe.transition,n=$;try{if(Oe.transition=null,$=16>e?16:e,Nt===null)var r=!1;else{if(e=Nt,Nt=null,_o=0,F&6)throw Error(k(331));var o=F;for(F|=4,z=e.current;z!==null;){var l=z,i=l.child;if(z.flags&16){var s=l.deletions;if(s!==null){for(var a=0;a<s.length;a++){var u=s[a];for(z=u;z!==null;){var h=z;switch(h.tag){case 0:case 11:case 15:tr(8,h,l)}var m=h.child;if(m!==null)m.return=h,z=m;else for(;z!==null;){h=z;var p=h.sibling,w=h.return;if(Oc(h),h===u){z=null;break}if(p!==null){p.return=w,z=p;break}z=w}}}var x=l.alternate;if(x!==null){var v=x.child;if(v!==null){x.child=null;do{var C=v.sibling;v.sibling=null,v=C}while(v!==null)}}z=l}}if(l.subtreeFlags&2064&&i!==null)i.return=l,z=i;else e:for(;z!==null;){if(l=z,l.flags&2048)switch(l.tag){case 0:case 11:case 15:tr(9,l,l.return)}var d=l.sibling;if(d!==null){d.return=l.return,z=d;break e}z=l.return}}var c=e.current;for(z=c;z!==null;){i=z;var f=i.child;if(i.subtreeFlags&2064&&f!==null)f.return=i,z=f;else e:for(i=c;z!==null;){if(s=z,s.flags&2048)try{switch(s.tag){case 0:case 11:case 15:Ao(9,s)}}catch(N){J(s,s.return,N)}if(s===i){z=null;break e}var y=s.sibling;if(y!==null){y.return=s.return,z=y;break e}z=s.return}}if(F=o,Dt(),Ze&&typeof Ze.onPostCommitFiberRoot=="function")try{Ze.onPostCommitFiberRoot(Ro,e)}catch{}r=!0}return r}finally{$=n,Oe.transition=t}}return!1}function Sa(e,t,n){t=jn(n,t),t=Cc(e,t,1),e=Tt(e,t,1),t=he(),e!==null&&(Sr(e,1,t),Se(e,t))}function J(e,t,n){if(e.tag===3)Sa(e,e,n);else for(;t!==null;){if(t.tag===3){Sa(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Rt===null||!Rt.has(r))){e=jn(n,e),e=Ec(t,e,1),t=Tt(t,e,1),e=he(),t!==null&&(Sr(t,1,e),Se(t,e));break}}t=t.return}}function Dp(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=he(),e.pingedLanes|=e.suspendedLanes&n,le===e&&(ae&n)===n&&(re===4||re===3&&(ae&130023424)===ae&&500>q()-rs?Gt(e,0):ns|=n),Se(e,t)}function Wc(e,t){t===0&&(e.mode&1?(t=Lr,Lr<<=1,!(Lr&130023424)&&(Lr=4194304)):t=1);var n=he();e=dt(e,t),e!==null&&(Sr(e,t,n),Se(e,n))}function Ap(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Wc(e,n)}function Up(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(k(314))}r!==null&&r.delete(t),Wc(e,n)}var Hc;Hc=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||xe.current)we=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return we=!1,Pp(e,t,n);we=!!(e.flags&131072)}else we=!1,G&&t.flags&1048576&&Ku(t,vo,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Zr(e,t),e=t.pendingProps;var o=Nn(t,pe.current);kn(t,n),o=Xi(null,t,r,e,o,n);var l=Ji();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,ke(r)?(l=!0,ho(t)):l=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,Hi(t),o.updater=Do,t.stateNode=o,o._reactInternals=t,Jl(t,r,e,n),t=ei(null,t,r,!0,l,n)):(t.tag=0,G&&l&&Di(t),me(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Zr(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=Vp(r),e=Ae(r,e),o){case 0:t=ql(null,t,r,e,n);break e;case 1:t=fa(null,t,r,e,n);break e;case 11:t=ca(null,t,r,e,n);break e;case 14:t=da(null,t,r,Ae(r.type,e),n);break e}throw Error(k(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ae(r,o),ql(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ae(r,o),fa(e,t,r,o,n);case 3:e:{if(zc(t),e===null)throw Error(k(387));r=t.pendingProps,l=t.memoizedState,o=l.element,tc(e,t),xo(t,r,null,n);var i=t.memoizedState;if(r=i.element,l.isDehydrated)if(l={element:r,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=l,t.memoizedState=l,t.flags&256){o=jn(Error(k(423)),t),t=pa(e,t,r,n,o);break e}else if(r!==o){o=jn(Error(k(424)),t),t=pa(e,t,r,n,o);break e}else for(Ee=jt(t.stateNode.containerInfo.firstChild),Ne=t,G=!0,$e=null,n=qu(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(_n(),r===o){t=ft(e,t,n);break e}me(e,t,r,n)}t=t.child}return t;case 5:return nc(t),e===null&&Yl(t),r=t.type,o=t.pendingProps,l=e!==null?e.memoizedProps:null,i=o.children,Bl(r,o)?i=null:l!==null&&Bl(r,l)&&(t.flags|=32),Pc(e,t),me(e,t,i,n),t.child;case 6:return e===null&&Yl(t),null;case 13:return jc(e,t,n);case 4:return Qi(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Pn(t,null,r,n):me(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ae(r,o),ca(e,t,r,o,n);case 7:return me(e,t,t.pendingProps,n),t.child;case 8:return me(e,t,t.pendingProps.children,n),t.child;case 12:return me(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,l=t.memoizedProps,i=o.value,B(yo,r._currentValue),r._currentValue=i,l!==null)if(We(l.value,i)){if(l.children===o.children&&!xe.current){t=ft(e,t,n);break e}}else for(l=t.child,l!==null&&(l.return=t);l!==null;){var s=l.dependencies;if(s!==null){i=l.child;for(var a=s.firstContext;a!==null;){if(a.context===r){if(l.tag===1){a=at(-1,n&-n),a.tag=2;var u=l.updateQueue;if(u!==null){u=u.shared;var h=u.pending;h===null?a.next=a:(a.next=h.next,h.next=a),u.pending=a}}l.lanes|=n,a=l.alternate,a!==null&&(a.lanes|=n),Kl(l.return,n,t),s.lanes|=n;break}a=a.next}}else if(l.tag===10)i=l.type===t.type?null:l.child;else if(l.tag===18){if(i=l.return,i===null)throw Error(k(341));i.lanes|=n,s=i.alternate,s!==null&&(s.lanes|=n),Kl(i,n,t),i=l.sibling}else i=l.child;if(i!==null)i.return=l;else for(i=l;i!==null;){if(i===t){i=null;break}if(l=i.sibling,l!==null){l.return=i.return,i=l;break}i=i.return}l=i}me(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,kn(t,n),o=be(o),r=r(o),t.flags|=1,me(e,t,r,n),t.child;case 14:return r=t.type,o=Ae(r,t.pendingProps),o=Ae(r.type,o),da(e,t,r,o,n);case 15:return Nc(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ae(r,o),Zr(e,t),t.tag=1,ke(r)?(e=!0,ho(t)):e=!1,kn(t,n),Sc(t,r,o),Jl(t,r,o,n),ei(null,t,r,!0,e,n);case 19:return Tc(e,t,n);case 22:return _c(e,t,n)}throw Error(k(156,t.tag))};function Qc(e,t){return wu(e,t)}function $p(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Me(e,t,n,r){return new $p(e,t,n,r)}function ss(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Vp(e){if(typeof e=="function")return ss(e)?1:0;if(e!=null){if(e=e.$$typeof,e===_i)return 11;if(e===Pi)return 14}return 2}function Mt(e,t){var n=e.alternate;return n===null?(n=Me(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function to(e,t,n,r,o,l){var i=2;if(r=e,typeof e=="function")ss(e)&&(i=1);else if(typeof e=="string")i=5;else e:switch(e){case ln:return Yt(n.children,o,l,t);case Ni:i=8,o|=8;break;case kl:return e=Me(12,n,t,o|2),e.elementType=kl,e.lanes=l,e;case Sl:return e=Me(13,n,t,o),e.elementType=Sl,e.lanes=l,e;case Cl:return e=Me(19,n,t,o),e.elementType=Cl,e.lanes=l,e;case nu:return $o(n,o,l,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case eu:i=10;break e;case tu:i=9;break e;case _i:i=11;break e;case Pi:i=14;break e;case xt:i=16,r=null;break e}throw Error(k(130,e==null?e:typeof e,""))}return t=Me(i,n,t,o),t.elementType=e,t.type=r,t.lanes=l,t}function Yt(e,t,n,r){return e=Me(7,e,r,t),e.lanes=n,e}function $o(e,t,n,r){return e=Me(22,e,r,t),e.elementType=nu,e.lanes=n,e.stateNode={isHidden:!1},e}function gl(e,t,n){return e=Me(6,e,null,t),e.lanes=n,e}function vl(e,t,n){return t=Me(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Bp(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Jo(0),this.expirationTimes=Jo(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Jo(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function as(e,t,n,r,o,l,i,s,a){return e=new Bp(e,t,n,s,a),t===1?(t=1,l===!0&&(t|=8)):t=0,l=Me(3,null,null,t),e.current=l,l.stateNode=e,l.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Hi(l),e}function Wp(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:on,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Gc(e){if(!e)return bt;e=e._reactInternals;e:{if(tn(e)!==e||e.tag!==1)throw Error(k(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(ke(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(k(171))}if(e.tag===1){var n=e.type;if(ke(n))return Gu(e,n,t)}return t}function Yc(e,t,n,r,o,l,i,s,a){return e=as(n,r,!0,e,o,l,i,s,a),e.context=Gc(null),n=e.current,r=he(),o=Lt(n),l=at(r,o),l.callback=t??null,Tt(n,l,o),e.current.lanes=o,Sr(e,o,r),Se(e,r),e}function Vo(e,t,n,r){var o=t.current,l=he(),i=Lt(o);return n=Gc(n),t.context===null?t.context=n:t.pendingContext=n,t=at(l,i),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Tt(o,t,i),e!==null&&(Be(e,o,i,l),Kr(e,o,i)),i}function zo(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Ca(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function us(e,t){Ca(e,t),(e=e.alternate)&&Ca(e,t)}function Hp(){return null}var Kc=typeof reportError=="function"?reportError:function(e){console.error(e)};function cs(e){this._internalRoot=e}Bo.prototype.render=cs.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(k(409));Vo(e,t,null,null)};Bo.prototype.unmount=cs.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;qt(function(){Vo(null,e,null,null)}),t[ct]=null}};function Bo(e){this._internalRoot=e}Bo.prototype.unstable_scheduleHydration=function(e){if(e){var t=_u();e={blockedOn:null,target:e,priority:t};for(var n=0;n<St.length&&t!==0&&t<St[n].priority;n++);St.splice(n,0,e),n===0&&zu(e)}};function ds(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Wo(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Ea(){}function Qp(e,t,n,r,o){if(o){if(typeof r=="function"){var l=r;r=function(){var u=zo(i);l.call(u)}}var i=Yc(t,r,e,0,null,!1,!1,"",Ea);return e._reactRootContainer=i,e[ct]=i.current,fr(e.nodeType===8?e.parentNode:e),qt(),i}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var s=r;r=function(){var u=zo(a);s.call(u)}}var a=as(e,0,!1,null,null,!1,!1,"",Ea);return e._reactRootContainer=a,e[ct]=a.current,fr(e.nodeType===8?e.parentNode:e),qt(function(){Vo(t,a,n,r)}),a}function Ho(e,t,n,r,o){var l=n._reactRootContainer;if(l){var i=l;if(typeof o=="function"){var s=o;o=function(){var a=zo(i);s.call(a)}}Vo(t,i,e,o)}else i=Qp(n,t,e,o,r);return zo(i)}Eu=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Gn(t.pendingLanes);n!==0&&(Ti(t,n|1),Se(t,q()),!(F&6)&&(Tn=q()+500,Dt()))}break;case 13:qt(function(){var r=dt(e,1);if(r!==null){var o=he();Be(r,e,1,o)}}),us(e,1)}};Ri=function(e){if(e.tag===13){var t=dt(e,134217728);if(t!==null){var n=he();Be(t,e,134217728,n)}us(e,134217728)}};Nu=function(e){if(e.tag===13){var t=Lt(e),n=dt(e,t);if(n!==null){var r=he();Be(n,e,t,r)}us(e,t)}};_u=function(){return $};Pu=function(e,t){var n=$;try{return $=e,t()}finally{$=n}};Ml=function(e,t,n){switch(t){case"input":if(_l(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=bo(r);if(!o)throw Error(k(90));ou(r),_l(r,o)}}}break;case"textarea":iu(e,n);break;case"select":t=n.value,t!=null&&vn(e,!!n.multiple,t,!1)}};pu=os;mu=qt;var Gp={usingClientEntryPoint:!1,Events:[Er,cn,bo,du,fu,os]},Vn={findFiberByHostInstance:Bt,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Yp={bundleType:Vn.bundleType,version:Vn.version,rendererPackageName:Vn.rendererPackageName,rendererConfig:Vn.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:pt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=vu(e),e===null?null:e.stateNode},findFiberByHostInstance:Vn.findFiberByHostInstance||Hp,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Br=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Br.isDisabled&&Br.supportsFiber)try{Ro=Br.inject(Yp),Ze=Br}catch{}}ze.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Gp;ze.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!ds(t))throw Error(k(200));return Wp(e,t,null,n)};ze.createRoot=function(e,t){if(!ds(e))throw Error(k(299));var n=!1,r="",o=Kc;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=as(e,1,!1,null,null,n,!1,r,o),e[ct]=t.current,fr(e.nodeType===8?e.parentNode:e),new cs(t)};ze.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(k(188)):(e=Object.keys(e).join(","),Error(k(268,e)));return e=vu(t),e=e===null?null:e.stateNode,e};ze.flushSync=function(e){return qt(e)};ze.hydrate=function(e,t,n){if(!Wo(t))throw Error(k(200));return Ho(null,e,t,!0,n)};ze.hydrateRoot=function(e,t,n){if(!ds(e))throw Error(k(405));var r=n!=null&&n.hydratedSources||null,o=!1,l="",i=Kc;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(l=n.identifierPrefix),n.onRecoverableError!==void 0&&(i=n.onRecoverableError)),t=Yc(t,null,e,1,n??null,o,!1,l,i),e[ct]=t.current,fr(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new Bo(t)};ze.render=function(e,t,n){if(!Wo(t))throw Error(k(200));return Ho(null,e,t,!1,n)};ze.unmountComponentAtNode=function(e){if(!Wo(e))throw Error(k(40));return e._reactRootContainer?(qt(function(){Ho(null,null,e,!1,function(){e._reactRootContainer=null,e[ct]=null})}),!0):!1};ze.unstable_batchedUpdates=os;ze.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Wo(n))throw Error(k(200));if(e==null||e._reactInternals===void 0)throw Error(k(38));return Ho(e,t,n,!1,r)};ze.version="18.3.1-next-f1338f8080-20240426";function Xc(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Xc)}catch(e){console.error(e)}}Xc(),Xa.exports=ze;var Kp=Xa.exports,Na=Kp;wl.createRoot=Na.createRoot,wl.hydrateRoot=Na.hydrateRoot;/**
 * @remix-run/router v1.20.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function jo(){return jo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},jo.apply(this,arguments)}var Qt;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(Qt||(Qt={}));const _a="popstate";function Xp(e){e===void 0&&(e={});function t(r,o){let{pathname:l,search:i,hash:s}=r.location;return fi("",{pathname:l,search:i,hash:s},o.state&&o.state.usr||null,o.state&&o.state.key||"default")}function n(r,o){return typeof o=="string"?o:Zc(o)}return Zp(t,n,null,e)}function Jc(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Jp(){return Math.random().toString(36).substr(2,8)}function Pa(e,t){return{usr:e.state,key:e.key,idx:t}}function fi(e,t,n,r){return n===void 0&&(n=null),jo({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?qc(t):t,{state:n,key:t&&t.key||r||Jp()})}function Zc(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function qc(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function Zp(e,t,n,r){r===void 0&&(r={});let{window:o=document.defaultView,v5Compat:l=!1}=r,i=o.history,s=Qt.Pop,a=null,u=h();u==null&&(u=0,i.replaceState(jo({},i.state,{idx:u}),""));function h(){return(i.state||{idx:null}).idx}function m(){s=Qt.Pop;let C=h(),d=C==null?null:C-u;u=C,a&&a({action:s,location:v.location,delta:d})}function p(C,d){s=Qt.Push;let c=fi(v.location,C,d);u=h()+1;let f=Pa(c,u),y=v.createHref(c);try{i.pushState(f,"",y)}catch(N){if(N instanceof DOMException&&N.name==="DataCloneError")throw N;o.location.assign(y)}l&&a&&a({action:s,location:v.location,delta:1})}function w(C,d){s=Qt.Replace;let c=fi(v.location,C,d);u=h();let f=Pa(c,u),y=v.createHref(c);i.replaceState(f,"",y),l&&a&&a({action:s,location:v.location,delta:0})}function x(C){let d=o.location.origin!=="null"?o.location.origin:o.location.href,c=typeof C=="string"?C:Zc(C);return c=c.replace(/ $/,"%20"),Jc(d,"No window.location.(origin|href) available to create URL for href: "+c),new URL(c,d)}let v={get action(){return s},get location(){return e(o,i)},listen(C){if(a)throw new Error("A history only accepts one active listener");return o.addEventListener(_a,m),a=C,()=>{o.removeEventListener(_a,m),a=null}},createHref(C){return t(o,C)},createURL:x,encodeLocation(C){let d=x(C);return{pathname:d.pathname,search:d.search,hash:d.hash}},push:p,replace:w,go(C){return i.go(C)}};return v}var za;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(za||(za={}));function qp(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}const ed=["post","put","patch","delete"];new Set(ed);const em=["get",...ed];new Set(em);/**
 * React Router v6.27.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function pi(){return pi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},pi.apply(this,arguments)}const tm=S.createContext(null),td=S.createContext(null);function nm(){return S.useContext(td)!=null}function rm(e){let{basename:t="/",children:n=null,location:r,navigationType:o=Qt.Pop,navigator:l,static:i=!1,future:s}=e;nm()&&Jc(!1);let a=t.replace(/^\/*/,"/"),u=S.useMemo(()=>({basename:a,navigator:l,static:i,future:pi({v7_relativeSplatPath:!1},s)}),[a,s,l,i]);typeof r=="string"&&(r=qc(r));let{pathname:h="/",search:m="",hash:p="",state:w=null,key:x="default"}=r,v=S.useMemo(()=>{let C=qp(h,a);return C==null?null:{location:{pathname:C,search:m,hash:p,state:w,key:x},navigationType:o}},[a,h,m,p,w,x,o]);return v==null?null:S.createElement(tm.Provider,{value:u},S.createElement(td.Provider,{children:n,value:v}))}new Promise(()=>{});/**
 * React Router DOM v6.27.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */const om="6";try{window.__reactRouterVersion=om}catch{}const lm="startTransition",ja=Fd[lm];function im(e){let{basename:t,children:n,future:r,window:o}=e,l=S.useRef();l.current==null&&(l.current=Xp({window:o,v5Compat:!0}));let i=l.current,[s,a]=S.useState({action:i.action,location:i.location}),{v7_startTransition:u}=r||{},h=S.useCallback(m=>{u&&ja?ja(()=>a(m)):a(m)},[a,u]);return S.useLayoutEffect(()=>i.listen(h),[i,h]),S.createElement(rm,{basename:t,children:n,location:s.location,navigationType:s.action,navigator:i,future:r})}var Ta;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Ta||(Ta={}));var Ra;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(Ra||(Ra={}));function nd(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=nd(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function rd(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=nd(e))&&(r&&(r+=" "),r+=t);return r}const fs="-",sm=e=>{const t=um(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:i=>{const s=i.split(fs);return s[0]===""&&s.length!==1&&s.shift(),od(s,t)||am(i)},getConflictingClassGroupIds:(i,s)=>{const a=n[i]||[];return s&&r[i]?[...a,...r[i]]:a}}},od=(e,t)=>{var i;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),o=r?od(e.slice(1),r):void 0;if(o)return o;if(t.validators.length===0)return;const l=e.join(fs);return(i=t.validators.find(({validator:s})=>s(l)))==null?void 0:i.classGroupId},La=/^\[(.+)\]$/,am=e=>{if(La.test(e)){const t=La.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},um=e=>{const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return dm(Object.entries(e.classGroups),n).forEach(([l,i])=>{mi(i,r,l,t)}),r},mi=(e,t,n,r)=>{e.forEach(o=>{if(typeof o=="string"){const l=o===""?t:Ma(t,o);l.classGroupId=n;return}if(typeof o=="function"){if(cm(o)){mi(o(r),t,n,r);return}t.validators.push({validator:o,classGroupId:n});return}Object.entries(o).forEach(([l,i])=>{mi(i,Ma(t,l),n,r)})})},Ma=(e,t)=>{let n=e;return t.split(fs).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},cm=e=>e.isThemeGetter,dm=(e,t)=>t?e.map(([n,r])=>{const o=r.map(l=>typeof l=="string"?t+l:typeof l=="object"?Object.fromEntries(Object.entries(l).map(([i,s])=>[t+i,s])):l);return[n,o]}):e,fm=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const o=(l,i)=>{n.set(l,i),t++,t>e&&(t=0,r=n,n=new Map)};return{get(l){let i=n.get(l);if(i!==void 0)return i;if((i=r.get(l))!==void 0)return o(l,i),i},set(l,i){n.has(l)?n.set(l,i):o(l,i)}}},ld="!",pm=e=>{const{separator:t,experimentalParseClassName:n}=e,r=t.length===1,o=t[0],l=t.length,i=s=>{const a=[];let u=0,h=0,m;for(let C=0;C<s.length;C++){let d=s[C];if(u===0){if(d===o&&(r||s.slice(C,C+l)===t)){a.push(s.slice(h,C)),h=C+l;continue}if(d==="/"){m=C;continue}}d==="["?u++:d==="]"&&u--}const p=a.length===0?s:s.substring(h),w=p.startsWith(ld),x=w?p.substring(1):p,v=m&&m>h?m-h:void 0;return{modifiers:a,hasImportantModifier:w,baseClassName:x,maybePostfixModifierPosition:v}};return n?s=>n({className:s,parseClassName:i}):i},mm=e=>{if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t},hm=e=>({cache:fm(e.cacheSize),parseClassName:pm(e),...sm(e)}),gm=/\s+/,vm=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:o}=t,l=[],i=e.trim().split(gm);let s="";for(let a=i.length-1;a>=0;a-=1){const u=i[a],{modifiers:h,hasImportantModifier:m,baseClassName:p,maybePostfixModifierPosition:w}=n(u);let x=!!w,v=r(x?p.substring(0,w):p);if(!v){if(!x){s=u+(s.length>0?" "+s:s);continue}if(v=r(p),!v){s=u+(s.length>0?" "+s:s);continue}x=!1}const C=mm(h).join(":"),d=m?C+ld:C,c=d+v;if(l.includes(c))continue;l.push(c);const f=o(v,x);for(let y=0;y<f.length;++y){const N=f[y];l.push(d+N)}s=u+(s.length>0?" "+s:s)}return s};function ym(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=id(t))&&(r&&(r+=" "),r+=n);return r}const id=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=id(e[r]))&&(n&&(n+=" "),n+=t);return n};function wm(e,...t){let n,r,o,l=i;function i(a){const u=t.reduce((h,m)=>m(h),e());return n=hm(u),r=n.cache.get,o=n.cache.set,l=s,s(a)}function s(a){const u=r(a);if(u)return u;const h=vm(a,n);return o(a,h),h}return function(){return l(ym.apply(null,arguments))}}const W=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},sd=/^\[(?:([a-z-]+):)?(.+)\]$/i,xm=/^\d+\/\d+$/,km=new Set(["px","full","screen"]),Sm=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Cm=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Em=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Nm=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,_m=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,nt=e=>Cn(e)||km.has(e)||xm.test(e),vt=e=>On(e,"length",Om),Cn=e=>!!e&&!Number.isNaN(Number(e)),yl=e=>On(e,"number",Cn),Bn=e=>!!e&&Number.isInteger(Number(e)),Pm=e=>e.endsWith("%")&&Cn(e.slice(0,-1)),O=e=>sd.test(e),yt=e=>Sm.test(e),zm=new Set(["length","size","percentage"]),jm=e=>On(e,zm,ad),Tm=e=>On(e,"position",ad),Rm=new Set(["image","url"]),Lm=e=>On(e,Rm,Im),Mm=e=>On(e,"",bm),Wn=()=>!0,On=(e,t,n)=>{const r=sd.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1},Om=e=>Cm.test(e)&&!Em.test(e),ad=()=>!1,bm=e=>Nm.test(e),Im=e=>_m.test(e),Fm=()=>{const e=W("colors"),t=W("spacing"),n=W("blur"),r=W("brightness"),o=W("borderColor"),l=W("borderRadius"),i=W("borderSpacing"),s=W("borderWidth"),a=W("contrast"),u=W("grayscale"),h=W("hueRotate"),m=W("invert"),p=W("gap"),w=W("gradientColorStops"),x=W("gradientColorStopPositions"),v=W("inset"),C=W("margin"),d=W("opacity"),c=W("padding"),f=W("saturate"),y=W("scale"),N=W("sepia"),P=W("skew"),j=W("space"),T=W("translate"),U=()=>["auto","contain","none"],M=()=>["auto","hidden","clip","visible","scroll"],ie=()=>["auto",O,t],D=()=>[O,t],He=()=>["",nt,vt],tt=()=>["auto",Cn,O],At=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],Qe=()=>["solid","dashed","dotted","double","none"],Ge=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],_=()=>["start","end","center","between","around","evenly","stretch"],R=()=>["","0",O],L=()=>["auto","avoid","all","avoid-page","page","left","right","column"],I=()=>[Cn,O];return{cacheSize:500,separator:":",theme:{colors:[Wn],spacing:[nt,vt],blur:["none","",yt,O],brightness:I(),borderColor:[e],borderRadius:["none","","full",yt,O],borderSpacing:D(),borderWidth:He(),contrast:I(),grayscale:R(),hueRotate:I(),invert:R(),gap:D(),gradientColorStops:[e],gradientColorStopPositions:[Pm,vt],inset:ie(),margin:ie(),opacity:I(),padding:D(),saturate:I(),scale:I(),sepia:R(),skew:I(),space:D(),translate:D()},classGroups:{aspect:[{aspect:["auto","square","video",O]}],container:["container"],columns:[{columns:[yt]}],"break-after":[{"break-after":L()}],"break-before":[{"break-before":L()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...At(),O]}],overflow:[{overflow:M()}],"overflow-x":[{"overflow-x":M()}],"overflow-y":[{"overflow-y":M()}],overscroll:[{overscroll:U()}],"overscroll-x":[{"overscroll-x":U()}],"overscroll-y":[{"overscroll-y":U()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[v]}],"inset-x":[{"inset-x":[v]}],"inset-y":[{"inset-y":[v]}],start:[{start:[v]}],end:[{end:[v]}],top:[{top:[v]}],right:[{right:[v]}],bottom:[{bottom:[v]}],left:[{left:[v]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Bn,O]}],basis:[{basis:ie()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",O]}],grow:[{grow:R()}],shrink:[{shrink:R()}],order:[{order:["first","last","none",Bn,O]}],"grid-cols":[{"grid-cols":[Wn]}],"col-start-end":[{col:["auto",{span:["full",Bn,O]},O]}],"col-start":[{"col-start":tt()}],"col-end":[{"col-end":tt()}],"grid-rows":[{"grid-rows":[Wn]}],"row-start-end":[{row:["auto",{span:[Bn,O]},O]}],"row-start":[{"row-start":tt()}],"row-end":[{"row-end":tt()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",O]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",O]}],gap:[{gap:[p]}],"gap-x":[{"gap-x":[p]}],"gap-y":[{"gap-y":[p]}],"justify-content":[{justify:["normal",..._()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",..._(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[..._(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[c]}],px:[{px:[c]}],py:[{py:[c]}],ps:[{ps:[c]}],pe:[{pe:[c]}],pt:[{pt:[c]}],pr:[{pr:[c]}],pb:[{pb:[c]}],pl:[{pl:[c]}],m:[{m:[C]}],mx:[{mx:[C]}],my:[{my:[C]}],ms:[{ms:[C]}],me:[{me:[C]}],mt:[{mt:[C]}],mr:[{mr:[C]}],mb:[{mb:[C]}],ml:[{ml:[C]}],"space-x":[{"space-x":[j]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[j]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",O,t]}],"min-w":[{"min-w":[O,t,"min","max","fit"]}],"max-w":[{"max-w":[O,t,"none","full","min","max","fit","prose",{screen:[yt]},yt]}],h:[{h:[O,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[O,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[O,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[O,t,"auto","min","max","fit"]}],"font-size":[{text:["base",yt,vt]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",yl]}],"font-family":[{font:[Wn]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",O]}],"line-clamp":[{"line-clamp":["none",Cn,yl]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",nt,O]}],"list-image":[{"list-image":["none",O]}],"list-style-type":[{list:["none","disc","decimal",O]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[d]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[d]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...Qe(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",nt,vt]}],"underline-offset":[{"underline-offset":["auto",nt,O]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:D()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",O]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",O]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[d]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...At(),Tm]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",jm]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},Lm]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[x]}],"gradient-via-pos":[{via:[x]}],"gradient-to-pos":[{to:[x]}],"gradient-from":[{from:[w]}],"gradient-via":[{via:[w]}],"gradient-to":[{to:[w]}],rounded:[{rounded:[l]}],"rounded-s":[{"rounded-s":[l]}],"rounded-e":[{"rounded-e":[l]}],"rounded-t":[{"rounded-t":[l]}],"rounded-r":[{"rounded-r":[l]}],"rounded-b":[{"rounded-b":[l]}],"rounded-l":[{"rounded-l":[l]}],"rounded-ss":[{"rounded-ss":[l]}],"rounded-se":[{"rounded-se":[l]}],"rounded-ee":[{"rounded-ee":[l]}],"rounded-es":[{"rounded-es":[l]}],"rounded-tl":[{"rounded-tl":[l]}],"rounded-tr":[{"rounded-tr":[l]}],"rounded-br":[{"rounded-br":[l]}],"rounded-bl":[{"rounded-bl":[l]}],"border-w":[{border:[s]}],"border-w-x":[{"border-x":[s]}],"border-w-y":[{"border-y":[s]}],"border-w-s":[{"border-s":[s]}],"border-w-e":[{"border-e":[s]}],"border-w-t":[{"border-t":[s]}],"border-w-r":[{"border-r":[s]}],"border-w-b":[{"border-b":[s]}],"border-w-l":[{"border-l":[s]}],"border-opacity":[{"border-opacity":[d]}],"border-style":[{border:[...Qe(),"hidden"]}],"divide-x":[{"divide-x":[s]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[s]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[d]}],"divide-style":[{divide:Qe()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...Qe()]}],"outline-offset":[{"outline-offset":[nt,O]}],"outline-w":[{outline:[nt,vt]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:He()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[d]}],"ring-offset-w":[{"ring-offset":[nt,vt]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",yt,Mm]}],"shadow-color":[{shadow:[Wn]}],opacity:[{opacity:[d]}],"mix-blend":[{"mix-blend":[...Ge(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":Ge()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[a]}],"drop-shadow":[{"drop-shadow":["","none",yt,O]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[h]}],invert:[{invert:[m]}],saturate:[{saturate:[f]}],sepia:[{sepia:[N]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[a]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[h]}],"backdrop-invert":[{"backdrop-invert":[m]}],"backdrop-opacity":[{"backdrop-opacity":[d]}],"backdrop-saturate":[{"backdrop-saturate":[f]}],"backdrop-sepia":[{"backdrop-sepia":[N]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[i]}],"border-spacing-x":[{"border-spacing-x":[i]}],"border-spacing-y":[{"border-spacing-y":[i]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",O]}],duration:[{duration:I()}],ease:[{ease:["linear","in","out","in-out",O]}],delay:[{delay:I()}],animate:[{animate:["none","spin","ping","pulse","bounce",O]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[y]}],"scale-x":[{"scale-x":[y]}],"scale-y":[{"scale-y":[y]}],rotate:[{rotate:[Bn,O]}],"translate-x":[{"translate-x":[T]}],"translate-y":[{"translate-y":[T]}],"skew-x":[{"skew-x":[P]}],"skew-y":[{"skew-y":[P]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",O]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",O]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":D()}],"scroll-mx":[{"scroll-mx":D()}],"scroll-my":[{"scroll-my":D()}],"scroll-ms":[{"scroll-ms":D()}],"scroll-me":[{"scroll-me":D()}],"scroll-mt":[{"scroll-mt":D()}],"scroll-mr":[{"scroll-mr":D()}],"scroll-mb":[{"scroll-mb":D()}],"scroll-ml":[{"scroll-ml":D()}],"scroll-p":[{"scroll-p":D()}],"scroll-px":[{"scroll-px":D()}],"scroll-py":[{"scroll-py":D()}],"scroll-ps":[{"scroll-ps":D()}],"scroll-pe":[{"scroll-pe":D()}],"scroll-pt":[{"scroll-pt":D()}],"scroll-pr":[{"scroll-pr":D()}],"scroll-pb":[{"scroll-pb":D()}],"scroll-pl":[{"scroll-pl":D()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",O]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[nt,vt,yl]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},Dm=wm(Fm);function Pe(...e){return Dm(rd(e))}const Vt=S.forwardRef(({className:e,...t},n)=>g.jsx("div",{ref:n,className:Pe("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));Vt.displayName="Card";const no=S.forwardRef(({className:e,...t},n)=>g.jsx("div",{ref:n,className:Pe("flex flex-col space-y-1.5 p-6",e),...t}));no.displayName="CardHeader";const ro=S.forwardRef(({className:e,...t},n)=>g.jsx("h3",{ref:n,className:Pe("text-2xl font-semibold leading-none tracking-tight",e),...t}));ro.displayName="CardTitle";const Am=S.forwardRef(({className:e,...t},n)=>g.jsx("p",{ref:n,className:Pe("text-sm text-muted-foreground",e),...t}));Am.displayName="CardDescription";const rn=S.forwardRef(({className:e,...t},n)=>g.jsx("div",{ref:n,className:Pe("p-6 pt-0",e),...t}));rn.displayName="CardContent";const Um=S.forwardRef(({className:e,...t},n)=>g.jsx("div",{ref:n,className:Pe("flex items-center p-6 pt-0",e),...t}));Um.displayName="CardFooter";function $m(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function ud(...e){return t=>e.forEach(n=>$m(n,t))}function Vm(...e){return S.useCallback(ud(...e),e)}var ps=S.forwardRef((e,t)=>{const{children:n,...r}=e,o=S.Children.toArray(n),l=o.find(Wm);if(l){const i=l.props.children,s=o.map(a=>a===l?S.Children.count(i)>1?S.Children.only(null):S.isValidElement(i)?i.props.children:null:a);return g.jsx(hi,{...r,ref:t,children:S.isValidElement(i)?S.cloneElement(i,void 0,s):null})}return g.jsx(hi,{...r,ref:t,children:n})});ps.displayName="Slot";var hi=S.forwardRef((e,t)=>{const{children:n,...r}=e;if(S.isValidElement(n)){const o=Qm(n);return S.cloneElement(n,{...Hm(r,n.props),ref:t?ud(t,o):o})}return S.Children.count(n)>1?S.Children.only(null):null});hi.displayName="SlotClone";var Bm=({children:e})=>g.jsx(g.Fragment,{children:e});function Wm(e){return S.isValidElement(e)&&e.type===Bm}function Hm(e,t){const n={...t};for(const r in t){const o=e[r],l=t[r];/^on[A-Z]/.test(r)?o&&l?n[r]=(...s)=>{l(...s),o(...s)}:o&&(n[r]=o):r==="style"?n[r]={...o,...l}:r==="className"&&(n[r]=[o,l].filter(Boolean).join(" "))}return{...e,...n}}function Qm(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}const Oa=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,ba=rd,ms=(e,t)=>n=>{var r;if((t==null?void 0:t.variants)==null)return ba(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:o,defaultVariants:l}=t,i=Object.keys(o).map(u=>{const h=n==null?void 0:n[u],m=l==null?void 0:l[u];if(h===null)return null;const p=Oa(h)||Oa(m);return o[u][p]}),s=n&&Object.entries(n).reduce((u,h)=>{let[m,p]=h;return p===void 0||(u[m]=p),u},{}),a=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((u,h)=>{let{class:m,className:p,...w}=h;return Object.entries(w).every(x=>{let[v,C]=x;return Array.isArray(C)?C.includes({...l,...s}[v]):{...l,...s}[v]===C})?[...u,m,p]:u},[]);return ba(e,i,a,n==null?void 0:n.class,n==null?void 0:n.className)},Gm=ms("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),Te=S.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,...o},l)=>{const i=r?ps:"button";return g.jsx(i,{className:Pe(Gm({variant:t,size:n,className:e})),ref:l,...o})});Te.displayName="Button";const wt=S.forwardRef(({className:e,type:t,...n},r)=>g.jsx("input",{type:t,className:Pe("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:r,...n}));wt.displayName="Input";const gi=S.forwardRef(({className:e,...t},n)=>g.jsx("textarea",{className:Pe("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:n,...t}));gi.displayName="Textarea";const Ym=ms("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function Ia({className:e,variant:t,...n}){return g.jsx("div",{className:Pe(Ym({variant:t}),e),...n})}function Km(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function Xm(e,t=[]){let n=[];function r(l,i){const s=S.createContext(i),a=n.length;n=[...n,i];const u=m=>{var d;const{scope:p,children:w,...x}=m,v=((d=p==null?void 0:p[e])==null?void 0:d[a])||s,C=S.useMemo(()=>x,Object.values(x));return g.jsx(v.Provider,{value:C,children:w})};u.displayName=l+"Provider";function h(m,p){var v;const w=((v=p==null?void 0:p[e])==null?void 0:v[a])||s,x=S.useContext(w);if(x)return x;if(i!==void 0)return i;throw new Error(`\`${m}\` must be used within \`${l}\``)}return[u,h]}const o=()=>{const l=n.map(i=>S.createContext(i));return function(s){const a=(s==null?void 0:s[e])||l;return S.useMemo(()=>({[`__scope${e}`]:{...s,[e]:a}}),[s,a])}};return o.scopeName=e,[r,Jm(o,...t)]}function Jm(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(l){const i=r.reduce((s,{useScope:a,scopeName:u})=>{const m=a(l)[`__scope${u}`];return{...s,...m}},{});return S.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}function cd(e){const t=S.useRef(e);return S.useEffect(()=>{t.current=e}),S.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function Zm({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,o]=qm({defaultProp:t,onChange:n}),l=e!==void 0,i=l?e:r,s=cd(n),a=S.useCallback(u=>{if(l){const m=typeof u=="function"?u(e):u;m!==e&&s(m)}else o(u)},[l,e,o,s]);return[i,a]}function qm({defaultProp:e,onChange:t}){const n=S.useState(e),[r]=n,o=S.useRef(r),l=cd(t);return S.useEffect(()=>{o.current!==r&&(l(r),o.current=r)},[r,o,l]),n}function eh(e){const t=S.useRef({value:e,previous:e});return S.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}var th=globalThis!=null&&globalThis.document?S.useLayoutEffect:()=>{};function nh(e){const[t,n]=S.useState(void 0);return th(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const l=o[0];let i,s;if("borderBoxSize"in l){const a=l.borderBoxSize,u=Array.isArray(a)?a[0]:a;i=u.inlineSize,s=u.blockSize}else i=e.offsetWidth,s=e.offsetHeight;n({width:i,height:s})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var rh=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],hs=rh.reduce((e,t)=>{const n=S.forwardRef((r,o)=>{const{asChild:l,...i}=r,s=l?ps:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),g.jsx(s,{...i,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{}),gs="Switch",[oh,zh]=Xm(gs),[lh,ih]=oh(gs),dd=S.forwardRef((e,t)=>{const{__scopeSwitch:n,name:r,checked:o,defaultChecked:l,required:i,disabled:s,value:a="on",onCheckedChange:u,form:h,...m}=e,[p,w]=S.useState(null),x=Vm(t,f=>w(f)),v=S.useRef(!1),C=p?h||!!p.closest("form"):!0,[d=!1,c]=Zm({prop:o,defaultProp:l,onChange:u});return g.jsxs(lh,{scope:n,checked:d,disabled:s,children:[g.jsx(hs.button,{type:"button",role:"switch","aria-checked":d,"aria-required":i,"data-state":md(d),"data-disabled":s?"":void 0,disabled:s,value:a,...m,ref:x,onClick:Km(e.onClick,f=>{c(y=>!y),C&&(v.current=f.isPropagationStopped(),v.current||f.stopPropagation())})}),C&&g.jsx(sh,{control:p,bubbles:!v.current,name:r,value:a,checked:d,required:i,disabled:s,form:h,style:{transform:"translateX(-100%)"}})]})});dd.displayName=gs;var fd="SwitchThumb",pd=S.forwardRef((e,t)=>{const{__scopeSwitch:n,...r}=e,o=ih(fd,n);return g.jsx(hs.span,{"data-state":md(o.checked),"data-disabled":o.disabled?"":void 0,...r,ref:t})});pd.displayName=fd;var sh=e=>{const{control:t,checked:n,bubbles:r=!0,...o}=e,l=S.useRef(null),i=eh(n),s=nh(t);return S.useEffect(()=>{const a=l.current,u=window.HTMLInputElement.prototype,m=Object.getOwnPropertyDescriptor(u,"checked").set;if(i!==n&&m){const p=new Event("click",{bubbles:r});m.call(a,n),a.dispatchEvent(p)}},[i,n,r]),g.jsx("input",{type:"checkbox","aria-hidden":!0,defaultChecked:n,...o,tabIndex:-1,ref:l,style:{...e.style,...s,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function md(e){return e?"checked":"unchecked"}var hd=dd,ah=pd;const gd=S.forwardRef(({className:e,...t},n)=>g.jsx(hd,{className:Pe("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...t,ref:n,children:g.jsx(ah,{className:Pe("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));gd.displayName=hd.displayName;var uh="Label",vd=S.forwardRef((e,t)=>g.jsx(hs.label,{...e,ref:t,onMouseDown:n=>{var o;n.target.closest("button, input, select, textarea")||((o=e.onMouseDown)==null||o.call(e,n),!n.defaultPrevented&&n.detail>1&&n.preventDefault())}}));vd.displayName=uh;var yd=vd;const ch=ms("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),rt=S.forwardRef(({className:e,...t},n)=>g.jsx(yd,{ref:n,className:Pe(ch(),e),...t}));rt.displayName=yd.displayName;/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dh=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),wd=(...e)=>e.filter((t,n,r)=>!!t&&t.trim()!==""&&r.indexOf(t)===n).join(" ").trim();/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var fh={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ph=S.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:o="",children:l,iconNode:i,...s},a)=>S.createElement("svg",{ref:a,...fh,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:wd("lucide",o),...s},[...i.map(([u,h])=>S.createElement(u,h)),...Array.isArray(l)?l:[l]]));/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mt=(e,t)=>{const n=S.forwardRef(({className:r,...o},l)=>S.createElement(ph,{ref:l,iconNode:t,className:wd(`lucide-${dh(e)}`,r),...o}));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mh=mt("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fa=mt("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hh=mt("LockOpen",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 9.9-1",key:"1mm8w8"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Da=mt("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gh=mt("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vh=mt("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yh=mt("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wh=mt("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xh=mt("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);Array(12).fill(0);et.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},et.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"}));et.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},et.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"}));et.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},et.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"}));et.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},et.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"}));var vi=1,kh=class{constructor(){this.subscribe=e=>(this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);this.subscribers.splice(t,1)}),this.publish=e=>{this.subscribers.forEach(t=>t(e))},this.addToast=e=>{this.publish(e),this.toasts=[...this.toasts,e]},this.create=e=>{var t;let{message:n,...r}=e,o=typeof(e==null?void 0:e.id)=="number"||((t=e.id)==null?void 0:t.length)>0?e.id:vi++,l=this.toasts.find(s=>s.id===o),i=e.dismissible===void 0?!0:e.dismissible;return l?this.toasts=this.toasts.map(s=>s.id===o?(this.publish({...s,...e,id:o,title:n}),{...s,...e,id:o,dismissible:i,title:n}):s):this.addToast({title:n,...r,dismissible:i,id:o}),o},this.dismiss=e=>(e||this.toasts.forEach(t=>{this.subscribers.forEach(n=>n({id:t.id,dismiss:!0}))}),this.subscribers.forEach(t=>t({id:e,dismiss:!0})),e),this.message=(e,t)=>this.create({...t,message:e}),this.error=(e,t)=>this.create({...t,message:e,type:"error"}),this.success=(e,t)=>this.create({...t,type:"success",message:e}),this.info=(e,t)=>this.create({...t,type:"info",message:e}),this.warning=(e,t)=>this.create({...t,type:"warning",message:e}),this.loading=(e,t)=>this.create({...t,type:"loading",message:e}),this.promise=(e,t)=>{if(!t)return;let n;t.loading!==void 0&&(n=this.create({...t,promise:e,type:"loading",message:t.loading,description:typeof t.description!="function"?t.description:void 0}));let r=e instanceof Promise?e:e(),o=n!==void 0;return r.then(async l=>{if(Ch(l)&&!l.ok){o=!1;let i=typeof t.error=="function"?await t.error(`HTTP error! status: ${l.status}`):t.error,s=typeof t.description=="function"?await t.description(`HTTP error! status: ${l.status}`):t.description;this.create({id:n,type:"error",message:i,description:s})}else if(t.success!==void 0){o=!1;let i=typeof t.success=="function"?await t.success(l):t.success,s=typeof t.description=="function"?await t.description(l):t.description;this.create({id:n,type:"success",message:i,description:s})}}).catch(async l=>{if(t.error!==void 0){o=!1;let i=typeof t.error=="function"?await t.error(l):t.error,s=typeof t.description=="function"?await t.description(l):t.description;this.create({id:n,type:"error",message:i,description:s})}}).finally(()=>{var l;o&&(this.dismiss(n),n=void 0),(l=t.finally)==null||l.call(t)}),n},this.custom=(e,t)=>{let n=(t==null?void 0:t.id)||vi++;return this.create({jsx:e(n),id:n,...t}),n},this.subscribers=[],this.toasts=[]}},De=new kh,Sh=(e,t)=>{let n=(t==null?void 0:t.id)||vi++;return De.addToast({title:e,...t,id:n}),n},Ch=e=>e&&typeof e=="object"&&"ok"in e&&typeof e.ok=="boolean"&&"status"in e&&typeof e.status=="number",Eh=Sh,Nh=()=>De.toasts,Ke=Object.assign(Eh,{success:De.success,info:De.info,warning:De.warning,error:De.error,custom:De.custom,message:De.message,promise:De.promise,dismiss:De.dismiss,loading:De.loading},{getHistory:Nh});function _h(e,{insertAt:t}={}){if(typeof document>"u")return;let n=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css",t==="top"&&n.firstChild?n.insertBefore(r,n.firstChild):n.appendChild(r),r.styleSheet?r.styleSheet.cssText=e:r.appendChild(document.createTextNode(e))}_h(`:where(html[dir="ltr"]),:where([data-sonner-toaster][dir="ltr"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir="rtl"]),:where([data-sonner-toaster][dir="rtl"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999}:where([data-sonner-toaster][data-x-position="right"]){right:max(var(--offset),env(safe-area-inset-right))}:where([data-sonner-toaster][data-x-position="left"]){left:max(var(--offset),env(safe-area-inset-left))}:where([data-sonner-toaster][data-x-position="center"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position="top"]){top:max(var(--offset),env(safe-area-inset-top))}:where([data-sonner-toaster][data-y-position="bottom"]){bottom:max(var(--offset),env(safe-area-inset-bottom))}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled="true"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position="top"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position="bottom"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise="true"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme="dark"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;background:var(--gray1);color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled="true"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping="true"]):before{content:"";position:absolute;left:0;right:0;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position="top"][data-swiping="true"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position="bottom"][data-swiping="true"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping="false"][data-removed="true"]):before{content:"";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:"";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted="true"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded="false"][data-front="false"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded="false"][data-front="false"][data-styled="true"])>*{opacity:0}:where([data-sonner-toast][data-visible="false"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted="true"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed="true"][data-front="true"][data-swipe-out="false"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="false"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed="true"][data-front="false"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount, 0px));transition:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation:swipe-out .2s ease-out forwards}@keyframes swipe-out{0%{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount)));opacity:1}to{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount) + var(--lift) * -100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;--mobile-offset: 16px;right:var(--mobile-offset);left:var(--mobile-offset);width:100%}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset)}[data-sonner-toaster][data-y-position=bottom]{bottom:20px}[data-sonner-toaster][data-y-position=top]{top:20px}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset);right:var(--mobile-offset);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}
`);const Ph=()=>{const[e,t]=S.useState(!1),[n,r]=S.useState(""),[o,l]=S.useState([]),[i,s]=S.useState(!0),[a,u]=S.useState(""),[h,m]=S.useState(""),[p,w]=S.useState(""),[x,v]=S.useState("Marketing"),[C,d]=S.useState(""),[c,f]=S.useState(""),[y,N]=S.useState(!0),[P,j]=S.useState(null);S.useEffect(()=>{e&&T()},[e]);const T=()=>{s(!0);const E=localStorage.getItem("blog_posts"),V=E?JSON.parse(E):[];l(V),s(!1)},U=E=>{localStorage.setItem("blog_posts",JSON.stringify(E)),window.dispatchEvent(new Event("blog_posts_updated"))},M=()=>{{Ke.error("Admin password not configured. Set VITE_MARKETING_ADMIN_PASSWORD in .env");return}},ie=()=>{t(!1);try{window.location.hash==="#blog-admin"&&window.history.replaceState({},"",window.location.pathname+window.location.search+"");const E=window.location.pathname||"";if(E.endsWith("/blog-admin")){const V=E.replace(/\/blog-admin$/,"")||"/";window.history.replaceState({},"",V+window.location.search+window.location.hash)}}catch{}},D=()=>{if(!a||!h)return Ke.error("Title and content required");const E=a.toLowerCase().replace(/[^a-z0-9\s]/g,"").replace(/\s+/g,"-"),V=new Date().toISOString(),A={id:Date.now().toString(),title:a,slug:E,content:h,excerpt:p,featured_image:C,author_name:"Marketing Admin",category:x,tags:c.split(",").map(ht=>ht.trim()).filter(Boolean),reading_time:Math.max(1,Math.round(h.length/800*3)),view_count:0,published_at:V,created_at:V,published:y},Z=JSON.parse(localStorage.getItem("blog_posts")||"[]");if(P){const ht=Z.map(_r=>_r.id===P?{..._r,...A,id:P,published_at:_r.published_at||V}:_r);U(ht),Ke.success("Post updated"),j(null)}else Z.unshift(A),U(Z),Ke.success("Post published");u(""),m(""),w(""),d(""),f(""),T()},He=E=>{const A=JSON.parse(localStorage.getItem("blog_posts")||"[]").find(Z=>Z.id===E);if(!A)return Ke.error("Post not found");j(E),u(A.title||""),w(A.excerpt||""),m(A.content||""),d(A.featured_image||""),v(A.category||"Marketing"),f((A.tags||[]).join(",")),N(!!A.published)},tt=E=>{const A=JSON.parse(localStorage.getItem("blog_posts")||"[]").filter(Z=>Z.id!==E);U(A);try{const Z=JSON.parse(localStorage.getItem("blog_comments")||"{}");delete Z[E],localStorage.setItem("blog_comments",JSON.stringify(Z))}catch{}Ke.success("Post deleted"),T()},At=E=>{try{return JSON.parse(localStorage.getItem("blog_comments")||"{}")[E]||[]}catch{return[]}},Qe=(E,V)=>{try{const A=JSON.parse(localStorage.getItem("blog_comments")||"{}");A[E]=(A[E]||[]).filter(Z=>Z.id!==V),localStorage.setItem("blog_comments",JSON.stringify(A)),Ke.success("Comment removed"),window.dispatchEvent(new Event("blog_posts_updated"))}catch(A){console.error(A),Ke.error("Failed to remove comment")}},[Ge,_]=S.useState(null),[R,L]=S.useState(null),I=E=>{_(Ge===E?null:E)},ee=()=>{if(R)try{const E=JSON.parse(localStorage.getItem("blog_comments")||"{}");E[R.postId]=(E[R.postId]||[]).map(V=>V.id===R.id?{...V,author:R.author,text:R.text}:V),localStorage.setItem("blog_comments",JSON.stringify(E)),Ke.success("Comment updated"),L(null),window.dispatchEvent(new Event("blog_posts_updated"))}catch(E){console.error(E),Ke.error("Failed to update comment")}};return e?g.jsx("div",{className:"min-h-screen premium-gradient-bg",children:g.jsxs("div",{className:"p-6 max-w-6xl mx-auto",children:[g.jsxs("div",{className:"flex items-center justify-between mb-8 fade-in-up",children:[g.jsxs("div",{children:[g.jsx("h1",{className:"text-4xl font-bold text-white mb-2",children:"Blog Admin Dashboard"}),g.jsx("p",{className:"text-muted-foreground",children:"Create and manage your blog posts"})]}),g.jsx("div",{className:"flex gap-2",children:g.jsxs(Te,{variant:"outline",onClick:ie,className:"border-accent text-accent hover:bg-accent hover:text-accent-foreground",children:[g.jsx(Da,{className:"w-4 h-4 mr-2"}),"Logout"]})})]}),g.jsxs(Vt,{className:"mb-8 glass-effect fade-in-up",style:{animationDelay:"0.1s"},children:[g.jsx(no,{children:g.jsxs(ro,{className:"text-2xl text-white flex items-center gap-2",children:[g.jsx(gh,{className:"w-6 h-6"}),P?"Edit Post":"Create New Post"]})}),g.jsxs(rn,{className:"space-y-6",children:[g.jsxs("div",{className:"grid md:grid-cols-2 gap-4",children:[g.jsxs("div",{className:"space-y-2",children:[g.jsx(rt,{htmlFor:"title",className:"text-white font-medium",children:"Title *"}),g.jsx(wt,{id:"title",placeholder:"Enter post title",value:a,onChange:E=>u(E.target.value),className:"bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-accent"})]}),g.jsxs("div",{className:"space-y-2",children:[g.jsx(rt,{htmlFor:"category",className:"text-white font-medium",children:"Category"}),g.jsx(wt,{id:"category",placeholder:"e.g., Marketing, Technology",value:x,onChange:E=>v(E.target.value),className:"bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-accent"})]})]}),g.jsxs("div",{className:"space-y-2",children:[g.jsx(rt,{htmlFor:"excerpt",className:"text-white font-medium",children:"Excerpt"}),g.jsx(wt,{id:"excerpt",placeholder:"Brief description of the post",value:p,onChange:E=>w(E.target.value),className:"bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-accent"})]}),g.jsxs("div",{className:"space-y-2",children:[g.jsx(rt,{htmlFor:"content",className:"text-white font-medium",children:"Content *"}),g.jsx(gi,{id:"content",placeholder:"Write your post content here (supports markdown and HTML)",value:h,onChange:E=>m(E.target.value),rows:10,className:"bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-accent"})]}),g.jsxs("div",{className:"grid md:grid-cols-2 gap-4",children:[g.jsxs("div",{className:"space-y-2",children:[g.jsx(rt,{htmlFor:"featuredImage",className:"text-white font-medium",children:"Featured Image URL"}),g.jsx(wt,{id:"featuredImage",placeholder:"https://example.com/image.jpg",value:C,onChange:E=>d(E.target.value),className:"bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-accent"})]}),g.jsxs("div",{className:"space-y-2",children:[g.jsx(rt,{htmlFor:"tags",className:"text-white font-medium",children:"Tags"}),g.jsx(wt,{id:"tags",placeholder:"marketing, strategy, growth (comma separated)",value:c,onChange:E=>f(E.target.value),className:"bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-accent"})]})]}),g.jsxs("div",{className:"flex items-center gap-3",children:[g.jsx(gd,{checked:y,onCheckedChange:E=>N(!!E),className:"data-[state=checked]:bg-accent"}),g.jsx(rt,{className:"text-white font-medium",children:"Publish immediately"})]}),g.jsxs("div",{className:"flex gap-3 pt-4",children:[g.jsxs(Te,{onClick:D,className:"bg-accent text-accent-foreground hover:bg-accent/90 hover-lift",children:[g.jsx(vh,{className:"w-4 h-4 mr-2"}),P?"Update Post":"Create Post"]}),P&&g.jsxs(Te,{variant:"outline",onClick:()=>{j(null),u(""),m(""),w(""),d(""),f("")},className:"border-white/20 text-white hover:bg-white/10",children:[g.jsx(xh,{className:"w-4 h-4 mr-2"}),"Cancel"]})]})]})]}),g.jsxs(Vt,{className:"glass-effect fade-in-up",style:{animationDelay:"0.2s"},children:[g.jsx(no,{children:g.jsxs(ro,{className:"text-2xl text-white flex items-center gap-2",children:[g.jsx(Fa,{className:"w-6 h-6"}),"Published Posts (",o.length,")"]})}),g.jsx(rn,{children:g.jsx("div",{className:"space-y-4",children:i?g.jsx("div",{className:"text-center py-8",children:g.jsx("div",{className:"text-white",children:"Loading posts..."})}):o.length===0?g.jsx("div",{className:"text-center py-8",children:g.jsx("div",{className:"text-muted-foreground",children:"No posts yet. Create your first post above!"})}):o.map((E,V)=>g.jsx(Vt,{className:"glass-effect hover-lift",style:{animationDelay:`${.3+V*.1}s`},children:g.jsxs(rn,{className:"p-6",children:[g.jsxs("div",{className:"flex items-start justify-between",children:[g.jsxs("div",{className:"flex-1",children:[g.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[g.jsx("h3",{className:"text-lg font-semibold text-white",children:E.title}),g.jsx(Ia,{className:"bg-accent/20 text-accent border-accent/30",children:E.category}),E.is_featured&&g.jsx(Ia,{className:"bg-yellow-500/20 text-yellow-400 border-yellow-500/30",children:"Featured"})]}),g.jsxs("p",{className:"text-muted-foreground text-sm mb-3 line-clamp-2",children:[E.excerpt||E.content.substring(0,140),"..."]}),g.jsxs("div",{className:"flex items-center gap-4 text-xs text-muted-foreground",children:[g.jsxs("span",{className:"flex items-center gap-1",children:[g.jsx(mh,{className:"w-3 h-3"}),new Date(E.created_at).toLocaleDateString()]}),g.jsxs("span",{className:"flex items-center gap-1",children:[g.jsx(Fa,{className:"w-3 h-3"}),E.view_count||0," views"]}),E.tags&&g.jsxs("span",{children:["Tags: ",E.tags]})]})]}),g.jsxs("div",{className:"flex gap-2 ml-4",children:[g.jsxs(Te,{size:"sm",variant:"outline",onClick:()=>He(E.id),className:"border-accent/30 text-accent hover:bg-accent hover:text-accent-foreground",children:[g.jsx(yh,{className:"w-4 h-4 mr-1"}),"Edit"]}),g.jsxs(Te,{size:"sm",variant:"destructive",onClick:()=>tt(E.id),className:"hover-lift",children:[g.jsx(wh,{className:"w-4 h-4 mr-1"}),"Delete"]}),g.jsx(Te,{size:"sm",onClick:()=>I(E.id),className:"bg-accent/20 text-accent hover:bg-accent hover:text-accent-foreground",children:Ge===E.id?"Hide Comments":"Comments"})]})]}),Ge===E.id&&g.jsx("div",{className:"mt-3 space-y-2",children:At(E.id).length===0?g.jsx("div",{className:"text-sm text-muted-foreground",children:"No comments yet."}):At(E.id).map(A=>g.jsx(Vt,{className:"p-3",children:g.jsxs("div",{className:"flex items-start justify-between",children:[g.jsxs("div",{children:[g.jsx("div",{className:"font-medium",children:A.author}),g.jsx("div",{className:"text-sm text-muted-foreground",children:A.text}),g.jsx("div",{className:"text-xs text-muted-foreground mt-1",children:new Date(A.created_at).toLocaleString()})]}),g.jsx("div",{className:"flex flex-col items-end gap-2",children:g.jsxs("div",{className:"flex gap-2",children:[g.jsx(Te,{size:"sm",variant:"outline",onClick:()=>L({postId:E.id,id:A.id,author:A.author,text:A.text}),children:"Edit"}),g.jsx(Te,{size:"sm",variant:"destructive",onClick:()=>Qe(E.id,A.id),children:"Delete"})]})})]})},A.id))})]})},E.id))})})]}),R&&g.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black/60 p-4",children:g.jsx(Vt,{className:"w-full max-w-2xl",children:g.jsxs(rn,{children:[g.jsx("h3",{className:"text-xl font-semibold mb-2",children:"Edit Comment"}),g.jsx(wt,{value:R.author,onChange:E=>L({...R,author:E.target.value}),placeholder:"Author"}),g.jsx(gi,{value:R.text,onChange:E=>L({...R,text:E.target.value}),rows:4,className:"mt-2"}),g.jsxs("div",{className:"flex gap-2 mt-4",children:[g.jsx(Te,{onClick:ee,children:"Save"}),g.jsx(Te,{variant:"ghost",onClick:()=>L(null),children:"Cancel"})]})]})})})]})}):g.jsx("div",{className:"min-h-screen premium-gradient-bg flex items-center justify-center p-4",children:g.jsxs(Vt,{className:"w-full max-w-md glass-effect",children:[g.jsxs(no,{className:"text-center",children:[g.jsx("div",{className:"flex items-center justify-center mb-4",children:g.jsx(Da,{className:"w-8 h-8 text-accent"})}),g.jsx(ro,{className:"text-3xl font-bold text-white",children:"Blog Admin"}),g.jsx("p",{className:"text-muted-foreground",children:"Enter admin password to manage blog posts"})]}),g.jsxs(rn,{className:"space-y-4",children:[g.jsxs("div",{className:"space-y-2",children:[g.jsx(rt,{htmlFor:"password",className:"text-white font-medium",children:"Password"}),g.jsx(wt,{id:"password",value:n,onChange:E=>r(E.target.value),placeholder:"Enter admin password",type:"password",className:"bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-accent",onKeyPress:E=>E.key==="Enter"&&M()})]}),g.jsxs(Te,{onClick:M,className:"w-full bg-accent text-accent-foreground hover:bg-accent/90 hover-lift",children:[g.jsx(hh,{className:"w-4 h-4 mr-2"}),"Login to Admin"]})]})]})})};wl.createRoot(document.getElementById("root")).render(g.jsx(et.StrictMode,{children:g.jsx(im,{children:g.jsx(Ph,{})})}));
