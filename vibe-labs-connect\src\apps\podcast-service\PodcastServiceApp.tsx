import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Mic, 
  Headphones, 
  Video, 
  Radio, 
  Upload, 
  Users,
  TrendingUp,
  Globe,
  Play,
  Pause,
  Volume2,
  Settings,
  Mail,
  Phone,
  ArrowRight,
  Check,
  Star,
  BookOpen,
  Podcast,
  MonitorPlay,
  AudioWaveform,
  Sparkles
} from 'lucide-react';
import { BlogList } from '@/components/blog/BlogList';
import { BlogPost } from '@/components/blog/BlogPost';
import { BlogAdmin } from '@/components/blog/BlogAdmin';

const PodcastServiceApp: React.FC = () => {
  const [currentView, setCurrentView] = useState<'home' | 'blog' | 'blog-post' | 'admin'>('home');
  const [currentBlogSlug, setCurrentBlogSlug] = useState<string>('');
  const [playingAudio, setPlayingAudio] = useState<string | null>(null);

  const services = [
    {
      icon: <Settings className="w-8 h-8" />,
      title: "Setup & Launch",
      description: "Complete podcast setup from concept to first episode release.",
      features: ["Podcast hosting setup", "RSS feed creation", "Platform distribution", "Cover art design", "Intro/outro production"],
      price: "From $199"
    },
    {
      icon: <AudioWaveform className="w-8 h-8" />,
      title: "Audio Production",
      description: "Professional editing, mixing, and mastering for crystal-clear sound.",
      features: ["Noise reduction & cleanup", "Professional mixing", "Mastering for streaming", "Sound design & music", "Multi-track editing"],
      price: "From $75/episode"
    },
    {
      icon: <Video className="w-8 h-8" />,
      title: "Video Podcasting",
      description: "Full video production for YouTube, social media, and streaming platforms.",
      features: ["Multi-camera editing", "Color correction", "Graphics & titles", "Social media clips", "YouTube optimization"],
      price: "From $149/episode"
    },
    {
      icon: <TrendingUp className="w-8 h-8" />,
      title: "Growth & Marketing",
      description: "Strategic marketing to grow your audience and monetize your podcast.",
      features: ["Podcast SEO", "Social media strategy", "Sponsorship placement", "Analytics & insights", "Cross-platform promotion"],
      price: "From $299/month"
    }
  ];

  const packages = [
    {
      name: "Podcast Starter",
      price: "$299",
      description: "Perfect for launching your first podcast",
      features: [
        "Complete podcast setup",
        "Cover art design",
        "First 3 episodes edited",
        "Platform distribution",
        "Basic analytics setup"
      ],
      popular: false
    },
    {
      name: "Content Creator",
      price: "$799",
      description: "Everything you need for consistent publishing",
      features: [
        "Monthly episode editing (4 episodes)",
        "Video podcast setup",
        "Social media content creation",
        "Audiogram creation",
        "Performance analytics",
        "Growth strategy consultation"
      ],
      popular: true
    },
    {
      name: "Podcast Pro",
      price: "$1,499",
      description: "Full-service podcast production & marketing",
      features: [
        "Unlimited episode editing",
        "Full video production",
        "Advanced sound design",
        "Dedicated producer",
        "Marketing campaign management",
        "Sponsorship facilitation",
        "Monthly strategy sessions"
      ],
      popular: false
    }
  ];

  const sampleEpisodes = [
    {
      id: "1",
      title: "The Future of Digital Marketing",
      duration: "32:15",
      category: "Business",
      description: "Exploring emerging trends in digital marketing and how they'll shape the future of business.",
      audioUrl: "/sample-audio/episode-1.mp3"
    },
    {
      id: "2", 
      title: "Building a Personal Brand",
      duration: "28:42",
      category: "Career",
      description: "A deep dive into personal branding strategies that actually work in today's digital landscape.",
      audioUrl: "/sample-audio/episode-2.mp3"
    },
    {
      id: "3",
      title: "Entrepreneurship in Nigeria",
      duration: "45:18",
      category: "Startup",
      description: "Success stories and challenges of starting a business in Nigeria's dynamic economy.",
      audioUrl: "/sample-audio/episode-3.mp3"
    }
  ];

  const faqs = [
    {
      question: "Do I need to buy equipment?",
      answer: "We can work with whatever setup you have! We'll help you optimize your current equipment and recommend upgrades only when necessary."
    },
    {
      question: "Can you edit video podcasts too?",
      answer: "Absolutely! We offer full video podcast production including multi-camera editing, graphics, and optimization for YouTube and social platforms."
    },
    {
      question: "How long does setup take?",
      answer: "Most podcast setups are completed within 5-7 business days. This includes hosting setup, cover art, and distribution to all major platforms."
    },
    {
      question: "Do you help with content strategy?",
      answer: "Yes! Our packages include content planning, guest outreach assistance, and strategic guidance to help your podcast grow."
    },
    {
      question: "What's included in audio editing?",
      answer: "Full audio cleanup, noise reduction, level balancing, intro/outro integration, and professional mastering for streaming platforms."
    },
    {
      question: "Can you help with monetization?",
      answer: "Definitely! We assist with sponsorship placement, listener growth strategies, and various monetization opportunities for your podcast."
    }
  ];

  const handleAudioPlay = (episodeId: string) => {
    if (playingAudio === episodeId) {
      setPlayingAudio(null);
    } else {
      setPlayingAudio(episodeId);
      // In a real app, you'd implement actual audio playback
      setTimeout(() => setPlayingAudio(null), 30000); // Auto-stop after 30s
    }
  };

  const handleBlogNavigation = (slug: string) => {
    setCurrentBlogSlug(slug);
    setCurrentView('blog-post');
  };

  const renderNavigation = () => (
    <nav className="bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-0 z-50 border-b">
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center gap-2">
            <Podcast className="w-8 h-8 text-primary" />
            <span className="text-xl font-bold">EasyLabs Podcast</span>
          </div>
          <div className="hidden md:flex items-center space-x-8">
            <button 
              onClick={() => setCurrentView('home')}
              className={`hover:text-primary transition-colors ${currentView === 'home' ? 'text-primary font-medium' : ''}`}
            >
              Home
            </button>
            <a href="#about" className="hover:text-primary transition-colors">About</a>
            <a href="#services" className="hover:text-primary transition-colors">Services</a>
            <a href="#portfolio" className="hover:text-primary transition-colors">Portfolio</a>
            <a href="#testimonials" className="hover:text-primary transition-colors">Testimonials</a>
            <a href="#contact" className="hover:text-primary transition-colors">Contact</a>
            <button 
              onClick={() => setCurrentView('blog')}
              className={`hover:text-primary transition-colors flex items-center gap-1 ${currentView === 'blog' ? 'text-primary font-medium' : ''}`}
            >
              <BookOpen className="w-4 h-4" />
              Blog
            </button>
          </div>
          <Button onClick={() => setCurrentView('admin')} variant="outline">
            Admin
          </Button>
        </div>
      </div>
    </nav>
  );

  if (currentView === 'admin') {
    return (
      <div className="min-h-screen bg-background">
        {renderNavigation()}
        <BlogAdmin />
      </div>
    );
  }

  if (currentView === 'blog-post') {
    return (
      <div className="min-h-screen bg-background">
        {renderNavigation()}
        <BlogPost 
          slug={currentBlogSlug} 
          onBack={() => setCurrentView('blog')} 
        />
      </div>
    );
  }

  if (currentView === 'blog') {
    return (
      <div className="min-h-screen bg-background">
        {renderNavigation()}
        <div className="max-w-7xl mx-auto px-4 py-8">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold mb-4">Podcasting Insights & Tips</h1>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Master the art of podcasting with expert insights, industry trends, and growth strategies
            </p>
          </div>
          <BlogList 
            onReadMore={handleBlogNavigation}
            category="Podcast"
          />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {renderNavigation()}
      
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-primary/10 via-background to-secondary/10 py-20">
        <div className="max-w-7xl mx-auto px-4 text-center">
          <h1 className="text-5xl font-bold mb-6">
            Launch, Grow & Monetize{' '}
            <span className="text-primary">Your Podcast</span>
          </h1>
          <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto">
            Professional podcast production services for creators who want to sound amazing, 
            look professional, and grow their audience faster.
          </p>
          <div className="flex gap-4 justify-center mb-8">
            <Button size="lg" className="text-lg px-8">
              Start Your Podcast Journey
              <ArrowRight className="w-5 h-5 ml-2" />
            </Button>
            <Button size="lg" variant="outline" className="text-lg px-8">
              <Play className="w-5 h-5 mr-2" />
              Listen to Samples
            </Button>
          </div>
          <div className="flex justify-center gap-8 text-sm text-muted-foreground">
            <div className="flex items-center gap-2">
              <Check className="w-4 h-4 text-primary" />
              Professional Quality
            </div>
            <div className="flex items-center gap-2">
              <Check className="w-4 h-4 text-primary" />
              Fast Turnaround
            </div>
            <div className="flex items-center gap-2">
              <Check className="w-4 h-4 text-primary" />
              Full Support
            </div>
          </div>
        </div>
      </section>

      {/* Sample Episodes */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Our Work In Action</h2>
            <p className="text-muted-foreground">
              Listen to professionally produced episodes and hear the EasyLabs difference
            </p>
          </div>
          
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {sampleEpisodes.map((episode) => (
              <Card key={episode.id} className="group hover:shadow-lg transition-all duration-300">
                <CardHeader>
                  <div className="flex items-center justify-between mb-2">
                    <Badge variant="secondary">{episode.category}</Badge>
                    <span className="text-sm text-muted-foreground">{episode.duration}</span>
                  </div>
                  <CardTitle className="text-lg group-hover:text-primary transition-colors">
                    {episode.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground text-sm mb-4 line-clamp-2">
                    {episode.description}
                  </p>
                  <div className="flex items-center justify-between">
                    <Button
                      size="sm"
                      variant={playingAudio === episode.id ? "secondary" : "outline"}
                      onClick={() => handleAudioPlay(episode.id)}
                      className="gap-2"
                    >
                      {playingAudio === episode.id ? (
                        <>
                          <Pause className="w-4 h-4" />
                          Playing
                        </>
                      ) : (
                        <>
                          <Play className="w-4 h-4" />
                          Preview
                        </>
                      )}
                    </Button>
                    <div className="flex items-center gap-1 text-sm text-muted-foreground">
                      <Volume2 className="w-4 h-4" />
                      <span>High Quality</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section id="services" className="py-16 bg-muted/50">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Complete Podcast Production Services</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              From concept to distribution, we handle every aspect of your podcast production
            </p>
          </div>
          
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
            {services.map((service, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center text-primary mb-4">
                    {service.icon}
                  </div>
                  <CardTitle>{service.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground mb-4">{service.description}</p>
                  <ul className="text-sm space-y-2 mb-4">
                    {service.features.map((feature, i) => (
                      <li key={i} className="flex items-start gap-2">
                        <Check className="w-3 h-3 text-primary mt-0.5 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <div className="font-semibold text-primary">{service.price}</div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Packages Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Choose Your Podcast Package</h2>
            <p className="text-muted-foreground">
              Tailored solutions for every stage of your podcasting journey
            </p>
          </div>
          
          <div className="grid gap-8 md:grid-cols-3">
            {packages.map((pkg, index) => (
              <Card 
                key={index} 
                className={`relative ${pkg.popular ? 'border-primary shadow-lg scale-105' : ''}`}
              >
                {pkg.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-primary text-primary-foreground">
                      <Sparkles className="w-3 h-3 mr-1" />
                      Most Popular
                    </Badge>
                  </div>
                )}
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl">{pkg.name}</CardTitle>
                  <div className="text-3xl font-bold text-primary">{pkg.price}</div>
                  <p className="text-muted-foreground">{pkg.description}</p>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3 mb-6">
                    {pkg.features.map((feature, i) => (
                      <li key={i} className="flex items-center gap-2">
                        <Check className="w-4 h-4 text-primary" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <Button className="w-full" variant={pkg.popular ? "default" : "outline"}>
                    Choose Package
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-muted/50">
        <div className="max-w-4xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Frequently Asked Questions</h2>
            <p className="text-muted-foreground">
              Everything you need to know about our podcast production services
            </p>
          </div>
          
          <div className="grid gap-6 md:grid-cols-2">
            {faqs.map((faq, index) => (
              <Card key={index}>
                <CardContent className="p-6">
                  <h3 className="font-semibold mb-3 text-primary">{faq.question}</h3>
                  <p className="text-muted-foreground text-sm">{faq.answer}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
            <Card className="text-center">
              <CardContent className="p-6">
                <div className="text-4xl font-bold text-primary mb-2">500+</div>
                <div className="text-muted-foreground">Episodes Produced</div>
              </CardContent>
            </Card>
            <Card className="text-center">
              <CardContent className="p-6">
                <div className="text-4xl font-bold text-primary mb-2">150+</div>
                <div className="text-muted-foreground">Happy Podcasters</div>
              </CardContent>
            </Card>
            <Card className="text-center">
              <CardContent className="p-6">
                <div className="text-4xl font-bold text-primary mb-2">48hr</div>
                <div className="text-muted-foreground">Average Turnaround</div>
              </CardContent>
            </Card>
            <Card className="text-center">
              <CardContent className="p-6">
                <div className="text-4xl font-bold text-primary mb-2">99%</div>
                <div className="text-muted-foreground">Client Satisfaction</div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-16 bg-muted/50">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid gap-12 lg:grid-cols-2 items-center">
            <div>
              <h2 className="text-3xl font-bold mb-6">Why Choose EasyLabs Podcast?</h2>
              <div className="space-y-4">
                <div className="flex items-start gap-4">
                  <Headphones className="w-6 h-6 text-primary mt-1" />
                  <div>
                    <h3 className="font-semibold mb-2">Professional Audio Quality</h3>
                    <p className="text-muted-foreground">Industry-standard editing, mixing, and mastering that makes your podcast sound radio-ready.</p>
                  </div>
                </div>
                <div className="flex items-start gap-4">
                  <MonitorPlay className="w-6 h-6 text-primary mt-1" />
                  <div>
                    <h3 className="font-semibold mb-2">Video Production Expertise</h3>
                    <p className="text-muted-foreground">Full video podcast production for YouTube and social media platforms.</p>
                  </div>
                </div>
                <div className="flex items-start gap-4">
                  <TrendingUp className="w-6 h-6 text-primary mt-1" />
                  <div>
                    <h3 className="font-semibold mb-2">Growth-Focused Strategy</h3>
                    <p className="text-muted-foreground">We don't just produce - we help you grow your audience and monetize your content.</p>
                  </div>
                </div>
              </div>
            </div>
            <div className="grid gap-4 md:grid-cols-2">
              <Card className="p-6 bg-gradient-to-br from-primary/10 to-secondary/10">
                <div className="flex items-center gap-3 mb-3">
                  <Mic className="w-6 h-6 text-primary" />
                  <h3 className="font-semibold">Audio Excellence</h3>
                </div>
                <p className="text-sm text-muted-foreground">Crystal clear sound that keeps listeners engaged</p>
              </Card>
              <Card className="p-6">
                <div className="flex items-center gap-3 mb-3">
                  <Upload className="w-6 h-6 text-primary" />
                  <h3 className="font-semibold">Easy Distribution</h3>
                </div>
                <p className="text-sm text-muted-foreground">Automatic publishing to all major platforms</p>
              </Card>
              <Card className="p-6">
                <div className="flex items-center gap-3 mb-3">
                  <Users className="w-6 h-6 text-primary" />
                  <h3 className="font-semibold">Audience Growth</h3>
                </div>
                <p className="text-sm text-muted-foreground">Strategic marketing to expand your reach</p>
              </Card>
              <Card className="p-6 bg-gradient-to-br from-secondary/10 to-primary/10">
                <div className="flex items-center gap-3 mb-3">
                  <Globe className="w-6 h-6 text-primary" />
                  <h3 className="font-semibold">Global Reach</h3>
                </div>
                <p className="text-sm text-muted-foreground">Distribution to worldwide podcast directories</p>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-16">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">Ready to Start Your Podcast Journey?</h2>
          <p className="text-xl text-muted-foreground mb-8">
            From concept to success, we'll help you create a podcast that stands out and grows your audience.
          </p>
          <div className="flex gap-4 justify-center mb-8">
            <Button size="lg" className="text-lg px-8">
              <Mail className="w-5 h-5 mr-2" />
              Let's Launch Your Podcast
            </Button>
            <Button size="lg" variant="outline" className="text-lg px-8">
              <Phone className="w-5 h-5 mr-2" />
              Book Free Consultation
            </Button>
          </div>
          <p className="text-muted-foreground">
            Join 150+ successful podcasters who trust EasyLabs with their audio journey
          </p>
        </div>
      </section>
    </div>
  );
};

export default PodcastServiceApp;