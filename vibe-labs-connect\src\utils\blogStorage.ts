import { supabase } from '@/integrations/supabase/client';

export interface BlogPost {
  id: string;
  title: string;
  content: string;
  excerpt?: string;
  category: string;
  tags?: string[];
  featured_image?: string;
  is_featured?: boolean;
  published?: boolean;
  created_at: string;
  updated_at: string;
  slug: string;
  view_count?: number;
  seo_title?: string;
  seo_description?: string;
}

export interface BlogComment {
  id: string;
  post_id: string;
  author: string;
  text: string;
  created_at: string;
}

// Check if we're running on localhost
const isLocalhost = () => {
  const hostname = window.location.hostname;
  const isLocal = hostname === 'localhost' ||
                  hostname === '127.0.0.1' ||
                  hostname === '';
  console.log('🔍 Environment check:', { hostname, isLocal });
  return isLocal;
};

// Helper function to get post by slug
const getPostBySlugFromArray = (posts: BlogPost[], slug: string): BlogPost | null => {
  return posts.find(post => post.slug === slug) || null;
};

// Sample blog posts for initialization
const getSampleBlogPosts = (): BlogPost[] => {
  const now = new Date().toISOString();
  return [
    {
      id: 'sample-1',
      title: 'Welcome to Our Marketing Blog',
      content: '<h2>Welcome to the Future of Marketing</h2><p>We\'re excited to share insights, strategies, and trends that will help your business grow. Our team of marketing experts brings you the latest in digital marketing, content strategy, and business growth.</p><h3>What You\'ll Find Here</h3><ul><li>Digital marketing strategies</li><li>Content creation tips</li><li>SEO best practices</li><li>Social media insights</li><li>Business growth tactics</li></ul><p>Stay tuned for regular updates and actionable advice that you can implement immediately.</p>',
      excerpt: 'Welcome to our marketing blog where we share expert insights, strategies, and trends to help your business grow.',
      category: 'Marketing',
      tags: ['welcome', 'marketing', 'blog', 'strategy'],
      featured_image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=400&fit=crop',
      is_featured: true,
      published: true,
      created_at: now,
      updated_at: now,
      slug: 'welcome-to-our-marketing-blog',
      view_count: 0,
      seo_title: 'Welcome to Our Marketing Blog - Expert Insights & Strategies',
      seo_description: 'Discover expert marketing insights, strategies, and trends to grow your business. Get actionable advice from our team of marketing professionals.'
    },
    {
      id: 'sample-2',
      title: 'Digital Marketing Trends for 2024',
      content: '<h2>The Top Digital Marketing Trends Shaping 2024</h2><p>As we navigate through 2024, the digital marketing landscape continues to evolve at breakneck speed. Here are the key trends every marketer should know:</p><h3>1. AI-Powered Personalization</h3><p>Artificial intelligence is revolutionizing how we deliver personalized experiences to customers. From chatbots to recommendation engines, AI is making marketing more efficient and effective.</p><h3>2. Video-First Content Strategy</h3><p>Video content continues to dominate social media platforms. Short-form videos, live streaming, and interactive video content are driving engagement like never before.</p><h3>3. Voice Search Optimization</h3><p>With the rise of smart speakers and voice assistants, optimizing for voice search is becoming crucial for businesses.</p><p>Stay ahead of the curve by implementing these trends in your marketing strategy.</p>',
      excerpt: 'Discover the top digital marketing trends for 2024, including AI-powered personalization, video-first content, and voice search optimization.',
      category: 'Marketing',
      tags: ['digital marketing', 'trends', '2024', 'AI', 'video content'],
      featured_image: 'https://images.unsplash.com/photo-1432888622747-4eb9a8efeb07?w=800&h=400&fit=crop',
      is_featured: true,
      published: true,
      created_at: now,
      updated_at: now,
      slug: 'digital-marketing-trends-for-2024',
      view_count: 0,
      seo_title: 'Digital Marketing Trends for 2024 - Stay Ahead of the Curve',
      seo_description: 'Explore the top digital marketing trends for 2024 including AI personalization, video content strategies, and voice search optimization.'
    },
    {
      id: 'sample-3',
      title: 'Content Marketing Best Practices',
      content: '<h2>Mastering Content Marketing: Best Practices for Success</h2><p>Content marketing remains one of the most effective ways to attract and engage your target audience. Here\'s how to do it right:</p><h3>Know Your Audience</h3><p>Understanding your audience is the foundation of successful content marketing. Create detailed buyer personas and tailor your content to their needs, pain points, and interests.</p><h3>Create Valuable Content</h3><p>Focus on creating content that provides real value to your audience. This could be educational, entertaining, or inspirational content that helps solve their problems.</p><h3>Consistency is Key</h3><p>Maintain a consistent publishing schedule to keep your audience engaged and coming back for more.</p><h3>Measure and Optimize</h3><p>Use analytics to track your content performance and continuously optimize your strategy based on what works best.</p>',
      excerpt: 'Learn the essential content marketing best practices to create valuable content that engages your audience and drives business results.',
      category: 'Marketing',
      tags: ['content marketing', 'best practices', 'strategy', 'audience'],
      featured_image: 'https://images.unsplash.com/photo-1552664730-d307ca884978?w=800&h=400&fit=crop',
      is_featured: false,
      published: true,
      created_at: now,
      updated_at: now,
      slug: 'content-marketing-best-practices',
      view_count: 0,
      seo_title: 'Content Marketing Best Practices - Create Engaging Content',
      seo_description: 'Master content marketing with proven best practices for creating valuable, engaging content that drives business results.'
    },
    {
      id: 'sample-4',
      title: 'Real Estate Marketing Strategies That Work',
      content: '<h2>Effective Real Estate Marketing in the Digital Age</h2><p>The real estate industry has transformed dramatically with digital marketing. Here are proven strategies that generate leads and close deals:</p><h3>Virtual Tours and 3D Walkthroughs</h3><p>Give potential buyers an immersive experience with virtual property tours. This technology saves time and attracts serious buyers.</p><h3>Social Media Marketing</h3><p>Use Instagram and Facebook to showcase properties with high-quality photos and videos. Stories and reels perform exceptionally well.</p><h3>Local SEO Optimization</h3><p>Optimize your website for local searches. Most buyers search for "homes for sale near me" or specific neighborhood names.</p><p>Implement these strategies to stay competitive in today\'s real estate market.</p>',
      excerpt: 'Discover proven real estate marketing strategies including virtual tours, social media marketing, and local SEO optimization.',
      category: 'Real Estate',
      tags: ['real estate', 'marketing', 'virtual tours', 'social media', 'SEO'],
      featured_image: 'https://images.unsplash.com/photo-1560518883-ce09059eeffa?w=800&h=400&fit=crop',
      is_featured: true,
      published: true,
      created_at: now,
      updated_at: now,
      slug: 'real-estate-marketing-strategies-that-work',
      view_count: 0,
      seo_title: 'Real Estate Marketing Strategies That Work - Digital Success',
      seo_description: 'Learn effective real estate marketing strategies including virtual tours, social media, and local SEO to generate more leads.'
    },
    {
      id: 'sample-5',
      title: 'Fashion Brand Building in 2024',
      content: '<h2>Building a Successful Fashion Brand in the Digital Era</h2><p>The fashion industry is more competitive than ever. Here\'s how to build a brand that stands out:</p><h3>Authentic Storytelling</h3><p>Share your brand\'s story, values, and mission. Consumers connect with brands that have authentic narratives and clear purposes.</p><h3>Influencer Partnerships</h3><p>Collaborate with micro-influencers who align with your brand values. They often have higher engagement rates than mega-influencers.</p><h3>Sustainable Practices</h3><p>Highlight your commitment to sustainability. Modern consumers increasingly choose brands that prioritize environmental responsibility.</p><p>Focus on building genuine connections with your audience to create lasting brand loyalty.</p>',
      excerpt: 'Learn how to build a successful fashion brand through authentic storytelling, influencer partnerships, and sustainable practices.',
      category: 'Fashion',
      tags: ['fashion', 'branding', 'storytelling', 'influencers', 'sustainability'],
      featured_image: 'https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=800&h=400&fit=crop',
      is_featured: false,
      published: true,
      created_at: now,
      updated_at: now,
      slug: 'fashion-brand-building-in-2024',
      view_count: 0,
      seo_title: 'Fashion Brand Building in 2024 - Digital Success Guide',
      seo_description: 'Discover how to build a successful fashion brand with authentic storytelling, influencer partnerships, and sustainable practices.'
    },
    {
      id: 'sample-6',
      title: 'Tech Startup Growth Hacking Techniques',
      content: '<h2>Growth Hacking for Tech Startups: Proven Techniques</h2><p>Growing a tech startup requires creative, data-driven approaches. Here are growth hacking techniques that work:</p><h3>Product-Led Growth</h3><p>Build growth into your product itself. Features like referral systems, sharing capabilities, and viral loops can drive organic growth.</p><h3>Content Marketing at Scale</h3><p>Create valuable content that solves real problems for your target audience. This builds authority and drives organic traffic.</p><h3>Community Building</h3><p>Build a community around your product. Engaged communities become your best advocates and provide valuable feedback.</p><p>Focus on sustainable growth strategies that align with your product and audience.</p>',
      excerpt: 'Explore proven growth hacking techniques for tech startups including product-led growth, content marketing, and community building.',
      category: 'Technology',
      tags: ['tech startup', 'growth hacking', 'product-led growth', 'content marketing', 'community'],
      featured_image: 'https://images.unsplash.com/photo-1522071820081-009f0129c71c?w=800&h=400&fit=crop',
      is_featured: true,
      published: true,
      created_at: now,
      updated_at: now,
      slug: 'tech-startup-growth-hacking-techniques',
      view_count: 0,
      seo_title: 'Tech Startup Growth Hacking Techniques - Scale Your Business',
      seo_description: 'Learn proven growth hacking techniques for tech startups including product-led growth and community building strategies.'
    }
  ];
};

// Blog Posts Storage
export const blogStorage = {
  // Initialize sample posts if none exist
  async initializeSamplePosts(): Promise<void> {
    try {
      const existingPosts = await this.getPosts();
      if (existingPosts.length === 0) {
        console.log('🔄 Initializing sample blog posts...');
        const samplePosts = getSampleBlogPosts();

        if (isLocalhost()) {
          localStorage.setItem('blog_posts', JSON.stringify(samplePosts));
          window.dispatchEvent(new Event('blog_posts_updated'));
        } else {
          // For production, you might want to seed via admin interface
          console.log('⚠️ No posts found in production. Use admin interface to create posts.');
        }
        console.log('✅ Sample posts initialized');
      }
    } catch (error) {
      console.error('❌ Error initializing sample posts:', error);
    }
  },

  // Get all blog posts
  async getPosts(): Promise<BlogPost[]> {
    if (isLocalhost()) {
      // Use localStorage for localhost
      console.log('📱 Using localStorage for posts');
      const stored = localStorage.getItem('blog_posts');
      let posts = stored ? JSON.parse(stored) : [];

      // If no posts exist, initialize with sample posts
      if (posts.length === 0) {
        console.log('🔄 No posts found, initializing sample posts...');
        posts = getSampleBlogPosts();
        localStorage.setItem('blog_posts', JSON.stringify(posts));
        window.dispatchEvent(new Event('blog_posts_updated'));
      }

      console.log('📱 Found posts in localStorage:', posts.length);
      return posts;
    } else {
      // Use Supabase for deployed versions
      console.log('☁️ Using Supabase for posts');
      try {
        const { data, error } = await supabase
          .from('blog_posts')
          .select('*')
          .order('created_at', { ascending: false });

        if (error) {
          console.error('❌ Supabase error:', error);
          console.error('❌ Error details:', error.message, error.code);
          return [];
        }

        console.log('☁️ Found posts in Supabase:', data?.length || 0);
        return data || [];
      } catch (error) {
        console.error('❌ Error fetching posts from Supabase:', error);
        return [];
      }
    }
  },

  // Save a new post
  async savePost(post: Omit<BlogPost, 'id' | 'created_at' | 'updated_at' | 'slug'>): Promise<BlogPost> {
    const now = new Date().toISOString();
    const slug = post.title.toLowerCase().replace(/[^a-z0-9\s]/g, '').replace(/\s+/g, '-');
    const newPost: BlogPost = {
      ...post,
      id: Date.now().toString(),
      slug,
      created_at: now,
      updated_at: now
    };

    if (isLocalhost()) {
      // Use localStorage for localhost
      const existingPosts = await this.getPosts();
      const updatedPosts = [newPost, ...existingPosts];
      localStorage.setItem('blog_posts', JSON.stringify(updatedPosts));
      window.dispatchEvent(new Event('blog_posts_updated'));
      return newPost;
    } else {
      // Use Supabase for deployed versions
      try {
        const { data, error } = await supabase
          .from('blog_posts')
          .insert([newPost])
          .select()
          .single();
        
        if (error) {
          console.error('Supabase error:', error);
          throw error;
        }
        
        // Ensure tags is always a string array
        return data;
      } catch (error) {
        console.error('Error saving post to Supabase:', error);
        throw error;
      }
    }
  },

  // Update an existing post
  async updatePost(id: string, updates: Partial<BlogPost>): Promise<BlogPost> {
    const now = new Date().toISOString();
    const updatedData = { ...updates, updated_at: now };

    if (isLocalhost()) {
      // Use localStorage for localhost
      const existingPosts = await this.getPosts();
      const updatedPosts = existingPosts.map(post =>
        post.id === id ? { ...post, ...updatedData } : post
      );
      localStorage.setItem('blog_posts', JSON.stringify(updatedPosts));
      window.dispatchEvent(new Event('blog_posts_updated'));
      
      const updatedPost = updatedPosts.find(p => p.id === id);
      if (!updatedPost) throw new Error('Post not found');
      return updatedPost;
    } else {
      // Use Supabase for deployed versions
      try {
        const { data, error } = await supabase
          .from('blog_posts')
          .update(updatedData)
          .eq('id', id)
          .select()
          .single();
        
        if (error) {
          console.error('Supabase error:', error);
          throw error;
        }
        
        return data;
      } catch (error) {
        console.error('Error updating post in Supabase:', error);
        throw error;
      }
    }
  },

  // Delete a post
  async deletePost(id: string): Promise<void> {
    if (isLocalhost()) {
      // Use localStorage for localhost
      const existingPosts = await this.getPosts();
      const updatedPosts = existingPosts.filter(post => post.id !== id);
      localStorage.setItem('blog_posts', JSON.stringify(updatedPosts));
      window.dispatchEvent(new Event('blog_posts_updated'));
    } else {
      // Use Supabase for deployed versions
      try {
        const { error } = await supabase
          .from('blog_posts')
          .delete()
          .eq('id', id);
        
        if (error) {
          console.error('Supabase error:', error);
          throw error;
        }
      } catch (error) {
        console.error('Error deleting post from Supabase:', error);
        throw error;
      }
    }
  },

  // Get a single post by slug
  async getPostBySlug(slug: string): Promise<BlogPost | null> {
    const posts = await this.getPosts();
    return posts.find(post => post.slug === slug) || null;
  }
};

// Blog Comments Storage (Disabled for now - focus on posts first)
export const commentStorage = {
  // Get comments for a post
  async getComments(postId: string): Promise<BlogComment[]> {
    if (isLocalhost()) {
      // Use localStorage for localhost
      const stored = localStorage.getItem('blog_comments');
      const allComments = stored ? JSON.parse(stored) : {};
      return allComments[postId] || [];
    } else {
      // Use Supabase for deployed versions
      try {
        const { data, error } = await (supabase as any)
          .from('blog_comments')
          .select('*')
          .eq('post_id', postId)
          .order('created_at', { ascending: true });
        
        if (error) {
          console.error('Supabase error:', error);
          return [];
        }
        
        return data || [];
      } catch (error) {
        console.error('Error fetching comments from Supabase:', error);
        return [];
      }
    }
  },

  // Save a new comment
  async saveComment(comment: Omit<BlogComment, 'id' | 'created_at'>): Promise<BlogComment> {
    const now = new Date().toISOString();
    const newComment: BlogComment = {
      ...comment,
      id: Date.now().toString(),
      created_at: now
    };

    if (isLocalhost()) {
      // Use localStorage for localhost
      const stored = localStorage.getItem('blog_comments');
      const allComments = stored ? JSON.parse(stored) : {};
      
      if (!allComments[comment.post_id]) {
        allComments[comment.post_id] = [];
      }
      
      allComments[comment.post_id].push(newComment);
      localStorage.setItem('blog_comments', JSON.stringify(allComments));
      
      return newComment;
    } else {
      // Use Supabase for deployed versions
      try {
        const { data, error } = await (supabase as any)
          .from('blog_comments')
          .insert([newComment])
          .select()
          .single();
        
        if (error) {
          console.error('Supabase error:', error);
          throw error;
        }
        
        return data;
      } catch (error) {
        console.error('Error saving comment to Supabase:', error);
        throw error;
      }
    }
  },

  // Update a comment
  async updateComment(id: string, postId: string, updates: Partial<BlogComment>): Promise<BlogComment> {
    if (isLocalhost()) {
      // Use localStorage for localhost
      const stored = localStorage.getItem('blog_comments');
      const allComments = stored ? JSON.parse(stored) : {};
      
      if (allComments[postId]) {
        const commentIndex = allComments[postId].findIndex((c: BlogComment) => c.id === id);
        if (commentIndex !== -1) {
          allComments[postId][commentIndex] = { ...allComments[postId][commentIndex], ...updates };
          localStorage.setItem('blog_comments', JSON.stringify(allComments));
          return allComments[postId][commentIndex];
        }
      }
      
      throw new Error('Comment not found');
    } else {
      // Use Supabase for deployed versions
      try {
        const { data, error } = await (supabase as any)
          .from('blog_comments')
          .update(updates)
          .eq('id', id)
          .select()
          .single();
        
        if (error) {
          console.error('Supabase error:', error);
          throw error;
        }
        
        return data;
      } catch (error) {
        console.error('Error updating comment in Supabase:', error);
        throw error;
      }
    }
  },

  // Delete a comment
  async deleteComment(id: string, postId: string): Promise<void> {
    if (isLocalhost()) {
      // Use localStorage for localhost
      const stored = localStorage.getItem('blog_comments');
      const allComments = stored ? JSON.parse(stored) : {};
      
      if (allComments[postId]) {
        allComments[postId] = allComments[postId].filter((c: BlogComment) => c.id !== id);
        localStorage.setItem('blog_comments', JSON.stringify(allComments));
      }
    } else {
      // Use Supabase for deployed versions
      try {
        const { error } = await (supabase as any)
          .from('blog_comments')
          .delete()
          .eq('id', id);
        
        if (error) {
          console.error('Supabase error:', error);
          throw error;
        }
      } catch (error) {
        console.error('Error deleting comment from Supabase:', error);
        throw error;
      }
    }
  }
};
