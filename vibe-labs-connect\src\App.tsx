
import Travel from "./pages/Travel";
import Tech from "./pages/Tech";
import Finance from "./pages/Finance";
import Restaurant from "./pages/Restaurant";

import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { useEffect } from "react";
import { analytics } from "@/services/analytics";
import Index from "./pages/Index";
import Fashion from "./pages/Fashion";
import RealEstate from "./pages/RealEstate";
import Music from "./pages/Music";
import Coaching from "./pages/Coaching";
import Video from "./pages/Video";
import Automation from "./pages/Automation";
import Gym from "./pages/Gym";
import Portfolio from "./pages/Portfolio";
import Contact from "./pages/Contact";
import Partner from "./pages/Partner";
import NotFound from "./pages/NotFound";
import Healthcare from "./pages/Healthcare";
import Legal from "./pages/Legal";
import Innovation from "./pages/Innovation";
import Beauty from "./pages/Beauty";
import Farming from "./pages/Farming";
import Hospitality from "./pages/Hospitality";
import NGO from "./pages/NGO";
import Education from "./pages/Education";
import Logistics from "./pages/Logistics";
import SaaS from "./pages/SaaS";
import Events from "./pages/Events";
import TechProductApp from "./apps/tech-product/TechProductApp";
import PrintfulTestCheckoutPage from "./pages/PrintfulTestCheckoutPage";
import TermsOfService from "./pages/TermsOfService";
import PrivacyPolicy from "./pages/PrivacyPolicy";
import RefundPolicy from "./pages/RefundPolicy";
import MarketingServiceApp from "./apps/marketing-service/MarketingServiceApp";
import BlogAdmin from "./apps/marketing-service/components/blog/BlogAdmin";
// Legacy in-repo admin route (kept for backward compatibility)
import BlogAdminTest from "./apps/blog-admin-test/BlogAdmin";
//import BlogAdmin from "./components/blog/BlogAdmin";
import ElectronicServiceApp from "./apps/electronic-service/ElectronicServiceApp";
import MusicServiceApp from "./apps/music-service/MusicServiceApp";
import FashionServiceApp from "./apps/fashion-service/FashionServiceApp";
import PodcastServiceApp from "./apps/podcast-service/PodcastServiceApp";
import VoiceServiceApp from "./apps/marketing-service copy/VoiceServiceApp";
const queryClient = new QueryClient();

const App = () => {
  // Initialize analytics on app start
  useEffect(() => {
    analytics.initialize();
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner
          position="top-right"
          expand={true}
          richColors={true}
          closeButton={true}
        />
        <BrowserRouter>
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/fashion" element={<Fashion />} />
          <Route path="/real-estate" element={<RealEstate />} />
          <Route path="/music" element={<Music />} />
          <Route path="/coaching" element={<Coaching />} />
          <Route path="/video" element={<Video />} />
          <Route path="/travel" element={<Travel />} />
          <Route path="/tech" element={<Tech />} />
          <Route path="/tech-product" element={<TechProductApp />} />
          <Route path="/marketing-service" element={<MarketingServiceApp />} />
          {/* Backward-compatible in-app admin route (legacy) */}
          <Route path="/marketing-admin-blog" element={<BlogAdmin />} />
          {/* New standalone blog admin app route */}
          <Route path="/blog-admin" element={<BlogAdminTest />} />
          <Route path="/voice-service" element={<VoiceServiceApp />} />
          {/* <Route path="/voice-admin" element={<BlogAdmin />} /> */}
          <Route path="/electronic-service" element={<ElectronicServiceApp />} />
          <Route path="/tech-product/:productId" element={<TechProductApp />} />
          <Route path="/tech-product/category/:categoryId" element={<TechProductApp />} />
          <Route path="/finance" element={<Finance />} />
          <Route path="/restaurant" element={<Restaurant />} />
          <Route path="/automation" element={<Automation />} />
          <Route path="/gym" element={<Gym />} />
          <Route path="/portfolio" element={<Portfolio />} />
          <Route path="/contact" element={<Contact />} />
          <Route path="/partner" element={<Partner />} />
          <Route path="/healthcare" element={<Healthcare />} />
          <Route path="/legal" element={<Legal />} />
          <Route path="/innovation" element={<Innovation />} />
          <Route path="/beauty" element={<Beauty />} />
          <Route path="/farming" element={<Farming />} />
          <Route path="/hospitality" element={<Hospitality />} />
          <Route path="/ngo" element={<NGO />} />
          <Route path="/education" element={<Education />} />
          <Route path="/logistics" element={<Logistics />} />
          <Route path="/saas" element={<SaaS />} />
          <Route path="/events" element={<Events />} />
          <Route path="/printful-test-checkout" element={<PrintfulTestCheckoutPage />} />
          <Route path="/terms" element={<TermsOfService />} />
          <Route path="/terms-of-service" element={<TermsOfService />} />
          <Route path="/privacy" element={<PrivacyPolicy />} />
          <Route path="/privacy-policy" element={<PrivacyPolicy />} />
          <Route path="/music-service" element={<MusicServiceApp />} />
          <Route path="/podcast-service" element={<PodcastServiceApp />} />
          <Route path="/refund" element={<RefundPolicy />} />
          <Route path="/refund-policy" element={<RefundPolicy />} />
          <Route path="/fashion-service" element={<FashionServiceApp />} />

          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
  );
};

export default App;
