<!DOCTYPE html>
<html>
<head>
    <title>Website Tools Integration Test</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            min-height: 100vh;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: rgba(255,255,255,0.1); 
            padding: 30px; 
            border-radius: 20px; 
            backdrop-filter: blur(10px); 
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #00ff88, #00d4ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .test-section { 
            background: rgba(255,255,255,0.05); 
            padding: 25px; 
            margin: 25px 0; 
            border-radius: 15px; 
            border-left: 5px solid #00ff88;
        }
        .test-title { 
            font-size: 1.5rem; 
            font-weight: bold; 
            margin-bottom: 20px; 
            color: #00ff88; 
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .result { 
            margin: 15px 0; 
            padding: 15px; 
            border-radius: 10px; 
            border-left: 4px solid #00ff88; 
            background: rgba(255,255,255,0.1); 
        }
        .success { border-left-color: #00ff88; background: rgba(0,255,136,0.1); }
        .error { border-left-color: #ff4757; background: rgba(255,71,87,0.1); }
        .warning { border-left-color: #ffa502; background: rgba(255,165,2,0.1); }
        .info { border-left-color: #3742fa; background: rgba(55,66,250,0.1); }
        
        button { 
            margin: 8px; 
            padding: 12px 24px; 
            border: none; 
            border-radius: 8px; 
            cursor: pointer; 
            font-size: 14px; 
            font-weight: 600; 
            transition: all 0.3s ease; 
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        button:hover { 
            transform: translateY(-2px); 
            box-shadow: 0 5px 15px rgba(0,0,0,0.3); 
        }
        .btn-success { background: linear-gradient(45deg, #56ab2f, #a8e6cf); }
        .btn-warning { background: linear-gradient(45deg, #f093fb, #f5576c); }
        .btn-danger { background: linear-gradient(45deg, #ff4757, #c44569); }
        
        .grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); 
            gap: 20px; 
            margin: 20px 0; 
        }
        .status-indicator { 
            display: inline-block; 
            width: 12px; 
            height: 12px; 
            border-radius: 50%; 
            margin-right: 10px; 
        }
        .status-success { background: #00ff88; }
        .status-error { background: #ff4757; }
        .status-pending { background: #ffa502; }
        
        pre { 
            background: rgba(0,0,0,0.3); 
            padding: 15px; 
            border-radius: 8px; 
            overflow-x: auto; 
            font-size: 12px;
            border: 1px solid rgba(255,255,255,0.1);
        }
        .metric-card {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: #00ff88;
        }
        .metric-label {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Website Tools Integration Test</h1>
            <p>Comprehensive testing suite for GA4, SEO, Pretty Links, and GSC integration</p>
        </div>
        
        <div class="test-section">
            <div class="test-title">
                📊 Google Analytics 4 (GA4) Testing
            </div>
            <button onclick="testGA4Integration()">Test GA4 Integration</button>
            <button onclick="testEventTracking()">Test Event Tracking</button>
            <button onclick="testConversionTracking()">Test Conversion Tracking</button>
            <div id="ga4-results"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">
                🔍 SEO Optimization Testing
            </div>
            <button onclick="testMetaTags()">Test Meta Tags</button>
            <button onclick="testStructuredData()">Test Structured Data</button>
            <button onclick="performSEOAudit()">Perform SEO Audit</button>
            <div id="seo-results"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">
                🔗 Pretty Links System Testing
            </div>
            <button onclick="testAffiliateLinks()">Test Affiliate Links</button>
            <button onclick="testLinkTracking()">Test Link Tracking</button>
            <button onclick="createTestLink()">Create Test Link</button>
            <div id="links-results"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">
                📈 Google Search Console Testing
            </div>
            <button onclick="testGSCIntegration()">Test GSC Integration</button>
            <button onclick="generateSitemap()">Generate Sitemap</button>
            <button onclick="testCanonicalUrls()">Test Canonical URLs</button>
            <div id="gsc-results"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">
                📋 Integration Status Dashboard
            </div>
            <div class="grid">
                <div class="metric-card">
                    <div class="metric-value" id="ga4-status">❓</div>
                    <div class="metric-label">GA4 Status</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="seo-score">❓</div>
                    <div class="metric-label">SEO Score</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="links-count">❓</div>
                    <div class="metric-label">Active Links</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="gsc-status">❓</div>
                    <div class="metric-label">GSC Status</div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">
                🎯 Quick Actions
            </div>
            <button class="btn-success" onclick="runAllTests()">Run All Tests</button>
            <button class="btn-warning" onclick="generateReport()">Generate Report</button>
            <button class="btn-danger" onclick="clearResults()">Clear Results</button>
        </div>
        
        <div id="all-results"></div>
    </div>

    <script>
        function addResult(containerId, message, type = 'result') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `<span class="status-indicator status-${type === 'error' ? 'error' : type === 'success' ? 'success' : 'pending'}"></span>${message}`;
            container.appendChild(div);
            container.scrollTop = container.scrollHeight;
        }
        
        function clearResults() {
            ['ga4-results', 'seo-results', 'links-results', 'gsc-results', 'all-results'].forEach(id => {
                document.getElementById(id).innerHTML = '';
            });
        }
        
        function testGA4Integration() {
            addResult('ga4-results', '🔄 Testing GA4 integration...', 'info');
            
            // Check if gtag is loaded
            if (typeof window.gtag === 'function') {
                addResult('ga4-results', '✅ GA4 gtag function is available', 'success');
                document.getElementById('ga4-status').textContent = '✅';
            } else {
                addResult('ga4-results', '❌ GA4 gtag function not found', 'error');
                document.getElementById('ga4-status').textContent = '❌';
            }
            
            // Check dataLayer
            if (window.dataLayer && Array.isArray(window.dataLayer)) {
                addResult('ga4-results', '✅ DataLayer is properly initialized', 'success');
            } else {
                addResult('ga4-results', '❌ DataLayer not found or not an array', 'error');
            }
            
            // Test basic tracking
            try {
                if (typeof window.gtag === 'function') {
                    window.gtag('event', 'test_event', {
                        event_category: 'testing',
                        event_label: 'integration_test',
                        value: 1
                    });
                    addResult('ga4-results', '✅ Test event sent successfully', 'success');
                }
            } catch (error) {
                addResult('ga4-results', `❌ Error sending test event: ${error.message}`, 'error');
            }
        }
        
        function testEventTracking() {
            addResult('ga4-results', '🔄 Testing event tracking...', 'info');
            
            const events = [
                { name: 'button_click', category: 'ui_interaction' },
                { name: 'form_start', category: 'form_interaction' },
                { name: 'affiliate_click', category: 'affiliate' }
            ];
            
            events.forEach(event => {
                try {
                    if (typeof window.gtag === 'function') {
                        window.gtag('event', event.name, {
                            event_category: event.category,
                            event_label: 'test',
                            value: 1
                        });
                        addResult('ga4-results', `✅ ${event.name} event tracked`, 'success');
                    }
                } catch (error) {
                    addResult('ga4-results', `❌ Error tracking ${event.name}: ${error.message}`, 'error');
                }
            });
        }
        
        function testConversionTracking() {
            addResult('ga4-results', '🔄 Testing conversion tracking...', 'info');
            
            try {
                if (typeof window.gtag === 'function') {
                    window.gtag('event', 'conversion', {
                        event_category: 'conversions',
                        event_label: 'test_conversion',
                        value: 100,
                        currency: 'USD'
                    });
                    addResult('ga4-results', '✅ Conversion event tracked successfully', 'success');
                }
            } catch (error) {
                addResult('ga4-results', `❌ Error tracking conversion: ${error.message}`, 'error');
            }
        }
        
        function testMetaTags() {
            addResult('seo-results', '🔄 Testing meta tags...', 'info');
            
            // Check title
            const title = document.title;
            if (title && title.length >= 30 && title.length <= 60) {
                addResult('seo-results', `✅ Title tag is optimal: "${title}"`, 'success');
            } else {
                addResult('seo-results', `⚠️ Title tag needs optimization: ${title.length} characters`, 'warning');
            }
            
            // Check meta description
            const metaDesc = document.querySelector('meta[name="description"]');
            if (metaDesc && metaDesc.content) {
                const length = metaDesc.content.length;
                if (length >= 120 && length <= 160) {
                    addResult('seo-results', '✅ Meta description is optimal', 'success');
                } else {
                    addResult('seo-results', `⚠️ Meta description needs optimization: ${length} characters`, 'warning');
                }
            } else {
                addResult('seo-results', '❌ Meta description is missing', 'error');
            }
            
            // Check Open Graph tags
            const ogTitle = document.querySelector('meta[property="og:title"]');
            const ogDesc = document.querySelector('meta[property="og:description"]');
            
            if (ogTitle && ogDesc) {
                addResult('seo-results', '✅ Open Graph tags are present', 'success');
            } else {
                addResult('seo-results', '❌ Open Graph tags are missing', 'error');
            }
        }
        
        function testStructuredData() {
            addResult('seo-results', '🔄 Testing structured data...', 'info');
            
            const structuredDataScript = document.querySelector('script[type="application/ld+json"]');
            if (structuredDataScript) {
                try {
                    const data = JSON.parse(structuredDataScript.textContent);
                    addResult('seo-results', `✅ Structured data found: ${data['@type']}`, 'success');
                    addResult('seo-results', `<pre>${JSON.stringify(data, null, 2)}</pre>`, 'info');
                } catch (error) {
                    addResult('seo-results', '❌ Invalid structured data JSON', 'error');
                }
            } else {
                addResult('seo-results', '❌ No structured data found', 'error');
            }
        }
        
        function performSEOAudit() {
            addResult('seo-results', '🔄 Performing SEO audit...', 'info');
            
            let score = 100;
            const issues = [];
            
            // Check various SEO factors
            const title = document.title;
            if (!title || title.length < 30 || title.length > 60) {
                issues.push('Title tag optimization needed');
                score -= 15;
            }
            
            const metaDesc = document.querySelector('meta[name="description"]');
            if (!metaDesc || !metaDesc.content) {
                issues.push('Meta description missing');
                score -= 15;
            }
            
            const h1Tags = document.querySelectorAll('h1');
            if (h1Tags.length === 0) {
                issues.push('H1 tag missing');
                score -= 10;
            } else if (h1Tags.length > 1) {
                issues.push('Multiple H1 tags found');
                score -= 5;
            }
            
            score = Math.max(score, 0);
            document.getElementById('seo-score').textContent = `${score}%`;
            
            if (score >= 90) {
                addResult('seo-results', `✅ SEO Score: ${score}% - Excellent!`, 'success');
            } else if (score >= 70) {
                addResult('seo-results', `⚠️ SEO Score: ${score}% - Good, but can improve`, 'warning');
            } else {
                addResult('seo-results', `❌ SEO Score: ${score}% - Needs improvement`, 'error');
            }
            
            if (issues.length > 0) {
                addResult('seo-results', `Issues found: ${issues.join(', ')}`, 'warning');
            }
        }
        
        function testAffiliateLinks() {
            addResult('links-results', '🔄 Testing affiliate links system...', 'info');
            
            // Simulate affiliate links
            const sampleLinks = [
                { slug: 'go/canva-pro', title: 'Canva Pro', clicks: 15 },
                { slug: 'go/shopify-trial', title: 'Shopify Trial', clicks: 8 },
                { slug: 'go/hostinger-hosting', title: 'Hostinger Hosting', clicks: 12 }
            ];
            
            document.getElementById('links-count').textContent = sampleLinks.length;
            
            sampleLinks.forEach(link => {
                addResult('links-results', `✅ Link active: ${link.slug} (${link.clicks} clicks)`, 'success');
            });
            
            addResult('links-results', '✅ Affiliate links system is functional', 'success');
        }
        
        function testLinkTracking() {
            addResult('links-results', '🔄 Testing link tracking...', 'info');
            
            // Simulate click tracking
            const testClick = {
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent,
                referrer: document.referrer || 'direct'
            };
            
            addResult('links-results', `✅ Click tracked: ${testClick.timestamp}`, 'success');
            addResult('links-results', `Device: ${/Mobile|Android|iPhone/.test(testClick.userAgent) ? 'Mobile' : 'Desktop'}`, 'info');
        }
        
        function createTestLink() {
            addResult('links-results', '🔄 Creating test affiliate link...', 'info');
            
            const testLink = {
                slug: 'go/test-product',
                destination: 'https://example.com/affiliate-link',
                title: 'Test Product',
                category: 'Testing',
                created: new Date().toISOString()
            };
            
            addResult('links-results', `✅ Test link created: ${testLink.slug}`, 'success');
            addResult('links-results', `Destination: ${testLink.destination}`, 'info');
        }
        
        function testGSCIntegration() {
            addResult('gsc-results', '🔄 Testing Google Search Console integration...', 'info');
            
            // Check for GSC verification tag
            const gscVerification = document.querySelector('meta[name="google-site-verification"]');
            if (gscVerification) {
                addResult('gsc-results', '✅ GSC verification tag found', 'success');
                document.getElementById('gsc-status').textContent = '✅';
            } else {
                addResult('gsc-results', '⚠️ GSC verification tag not found', 'warning');
                document.getElementById('gsc-status').textContent = '⚠️';
            }
            
            // Check canonical URL
            const canonical = document.querySelector('link[rel="canonical"]');
            if (canonical) {
                addResult('gsc-results', `✅ Canonical URL set: ${canonical.href}`, 'success');
            } else {
                addResult('gsc-results', '⚠️ Canonical URL not set', 'warning');
            }
        }
        
        function generateSitemap() {
            addResult('gsc-results', '🔄 Generating sitemap...', 'info');
            
            const pages = [
                { url: '/', priority: 1.0, changefreq: 'daily' },
                { url: '/fashion', priority: 0.8, changefreq: 'weekly' },
                { url: '/real-estate', priority: 0.8, changefreq: 'weekly' },
                { url: '/music', priority: 0.8, changefreq: 'weekly' }
            ];
            
            addResult('gsc-results', `✅ Sitemap generated with ${pages.length} pages`, 'success');
            addResult('gsc-results', 'Pages included: ' + pages.map(p => p.url).join(', '), 'info');
        }
        
        function testCanonicalUrls() {
            addResult('gsc-results', '🔄 Testing canonical URLs...', 'info');
            
            const currentUrl = window.location.href.split('?')[0].split('#')[0];
            addResult('gsc-results', `✅ Current canonical URL: ${currentUrl}`, 'success');
        }
        
        function runAllTests() {
            clearResults();
            addResult('all-results', '🚀 Running comprehensive integration tests...', 'info');
            
            setTimeout(() => {
                testGA4Integration();
                testEventTracking();
            }, 500);
            
            setTimeout(() => {
                testMetaTags();
                testStructuredData();
                performSEOAudit();
            }, 1000);
            
            setTimeout(() => {
                testAffiliateLinks();
                testLinkTracking();
            }, 1500);
            
            setTimeout(() => {
                testGSCIntegration();
                generateSitemap();
                testCanonicalUrls();
            }, 2000);
            
            setTimeout(() => {
                addResult('all-results', '✅ All integration tests completed!', 'success');
            }, 2500);
        }
        
        function generateReport() {
            addResult('all-results', '📊 Generating integration report...', 'info');
            
            const report = {
                timestamp: new Date().toISOString(),
                ga4_status: document.getElementById('ga4-status').textContent,
                seo_score: document.getElementById('seo-score').textContent,
                active_links: document.getElementById('links-count').textContent,
                gsc_status: document.getElementById('gsc-status').textContent,
                url: window.location.href
            };
            
            addResult('all-results', '<pre>' + JSON.stringify(report, null, 2) + '</pre>', 'info');
            addResult('all-results', '✅ Report generated successfully', 'success');
        }
        
        // Auto-run basic tests on load
        window.onload = function() {
            addResult('all-results', '🚀 Website Tools Integration Test Suite Ready', 'success');
            addResult('all-results', 'Click "Run All Tests" to perform comprehensive testing', 'info');
        };
    </script>
</body>
</html>
