import { useNavigate, useParams } from 'react-router-dom';
import { BlogPost } from '@/components/blog/BlogPost';

export default function BlogPostPage() {
  const navigate = useNavigate();
  const { slug } = useParams();

  if (!slug) {
    return <div>Post not found</div>;
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-4xl mx-auto px-4 py-8">
        <BlogPost 
          slug={slug} 
          onBack={() => navigate('/blog')}
        />
      </div>
    </div>
  );
}