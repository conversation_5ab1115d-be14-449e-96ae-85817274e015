import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Shirt, 
  TrendingUp, 
  Users, 
  ShoppingBag, 
  Palette, 
  Camera,
  BarChart3,
  Target,
  Zap,
  Mail,
  Phone,
  ArrowRight,
  Check,
  X,
  Star,
  BookOpen,
  Crown,
  Sparkles,
  Gift
} from 'lucide-react';
import { BlogList } from '@/components/blog/BlogList';
import { BlogPost } from '@/components/blog/BlogPost';
import { BlogAdmin } from '@/components/blog/BlogAdmin';

const FashionServiceApp: React.FC = () => {
  const [currentView, setCurrentView] = useState<'home' | 'blog' | 'blog-post' | 'admin'>('home');
  const [currentBlogSlug, setCurrentBlogSlug] = useState<string>('');

  const painPoints = [
    "You have a website, but sales are slow",
    "You run ads, but waste money on poor targeting",
    "Influencers promote your brand, but don't bring buyers",
    "Customers buy once but don't return",
    "Your social media looks great but doesn't convert",
    "You're competing on price instead of value"
  ];

  const packages = [
    {
      name: "Launch Brand",
      price: "$299",
      subtitle: "For new dropshippers or small fashion brands",
      description: "Get your store live & looking legitimate",
      features: [
        "Shopify setup + payment integration",
        "Professional logo & brand kit",
        "3 product mockups (hoodie, tee, cap)",
        "3 branded social media graphics",
        "Basic email automation setup"
      ],
      outcome: "Store live & looking legit",
      popular: false,
      cta: "Launch Now"
    },
    {
      name: "Get Sales",
      price: "$799",
      subtitle: "For brands with a site but no consistent sales",
      description: "Transform your store into a selling machine",
      features: [
        "TikTok + Instagram Ads setup (with 1 creative)",
        "Influencer outreach list (5–10 creators)",
        "Abandoned cart recovery email sequence",
        "30-day social media content calendar",
        "10 Canva post templates",
        "Product page optimization"
      ],
      outcome: "More customers & consistent sales",
      popular: true,
      cta: "Start Selling"
    },
    {
      name: "Scale Up",
      price: "$1,499",
      subtitle: "For growing brands who want serious revenue",
      description: "Build a loyal customer base & long-term growth",
      features: [
        "Advanced analytics dashboard (Google + Pixels)",
        "Conversion Rate Optimization (A/B test product pages)",
        "Upsell bundles (Hoodie + Cap, Save 15%)",
        "Customer loyalty program (points & referrals)",
        "Seasonal email campaigns (new drops, Black Friday, Xmas)",
        "Dedicated account manager",
        "Weekly performance reports"
      ],
      outcome: "Repeat buyers & long-term revenue growth",
      popular: false,
      cta: "Scale Revenue"
    }
  ];

  const addOns = [
    {
      name: "Website Revamp",
      description: "Custom fashion store look with lookbook page",
      price: "+$399"
    },
    {
      name: "Video Ads",
      description: "TikTok try-on haul, UGC promo, Instagram reel",
      price: "+$299"
    },
    {
      name: "Advanced SEO",
      description: "Rank for 'streetwear hoodie Nigeria' or 'minimalist fashion UK'",
      price: "+$199/mo"
    },
    {
      name: "Influencer Campaign",
      description: "Managed micro-influencer campaign with 10+ creators",
      price: "+$599"
    },
    {
      name: "Brand Photography",
      description: "Professional product photos & lifestyle shots",
      price: "+$499"
    }
  ];

  const handleBlogNavigation = (slug: string) => {
    setCurrentBlogSlug(slug);
    setCurrentView('blog-post');
  };

  const renderNavigation = () => (
    <nav className="bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-0 z-50 border-b">
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center gap-2">
            <Shirt className="w-8 h-8 text-primary" />
            <span className="text-xl font-bold">EasyLabs Fashion</span>
          </div>
          <div className="hidden md:flex items-center space-x-8">
            <button 
              onClick={() => setCurrentView('home')}
              className={`hover:text-primary transition-colors ${currentView === 'home' ? 'text-primary font-medium' : ''}`}
            >
              Home
            </button>
            <a href="#about" className="hover:text-primary transition-colors">About</a>
            <a href="#services" className="hover:text-primary transition-colors">Services</a>
            <a href="#portfolio" className="hover:text-primary transition-colors">Portfolio</a>
            <a href="#testimonials" className="hover:text-primary transition-colors">Testimonials</a>
            <a href="#contact" className="hover:text-primary transition-colors">Contact</a>
            <button 
              onClick={() => setCurrentView('blog')}
              className={`hover:text-primary transition-colors flex items-center gap-1 ${currentView === 'blog' ? 'text-primary font-medium' : ''}`}
            >
              <BookOpen className="w-4 h-4" />
              Blog
            </button>
          </div>
          <Button onClick={() => setCurrentView('admin')} variant="outline">
            Admin
          </Button>
        </div>
      </div>
    </nav>
  );

  if (currentView === 'admin') {
    return (
      <div className="min-h-screen bg-background">
        {renderNavigation()}
        <BlogAdmin />
      </div>
    );
  }

  if (currentView === 'blog-post') {
    return (
      <div className="min-h-screen bg-background">
        {renderNavigation()}
        <BlogPost 
          slug={currentBlogSlug} 
          onBack={() => setCurrentView('blog')} 
        />
      </div>
    );
  }

  if (currentView === 'blog') {
    return (
      <div className="min-h-screen bg-background">
        {renderNavigation()}
        <div className="max-w-7xl mx-auto px-4 py-8">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold mb-4">Fashion Industry Insights</h1>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Latest trends, marketing strategies, and business insights for fashion entrepreneurs
            </p>
          </div>
          <BlogList 
            onReadMore={handleBlogNavigation}
            category="Fashion"
          />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {renderNavigation()}
      
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-primary/10 via-background to-secondary/10 py-20">
        <div className="max-w-7xl mx-auto px-4 text-center">
          <h1 className="text-5xl font-bold mb-6">
            Turn Your Fashion Brand Into a{' '}
            <span className="text-primary">Sales Machine</span>{' '}
            👗👟
          </h1>
          <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto">
            Most clothing brands look good, but struggle to sell consistently. 
            We help fashion brands get more customers, boost sales, and turn one-time buyers into loyal fans.
          </p>
          <div className="flex gap-4 justify-center mb-8">
            <Button size="lg" className="text-lg px-8">
              Start Growing Your Brand Today
              <ArrowRight className="w-5 h-5 ml-2" />
            </Button>
            <Button size="lg" variant="outline" className="text-lg px-8">
              <Camera className="w-5 h-5 mr-2" />
              View Success Stories
            </Button>
          </div>
          <div className="flex justify-center gap-8 text-sm text-muted-foreground">
            <div className="flex items-center gap-2">
              <Check className="w-4 h-4 text-primary" />
              Complete Store Setup
            </div>
            <div className="flex items-center gap-2">
              <Check className="w-4 h-4 text-primary" />
              Sales Optimization
            </div>
            <div className="flex items-center gap-2">
              <Check className="w-4 h-4 text-primary" />
              Customer Retention
            </div>
          </div>
        </div>
      </section>

      {/* Pain Points Section */}
      <section className="py-16 bg-muted/50">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-8">You don't need just a website or video.<br />You need a system that sells.</h2>
          <div className="grid gap-4 md:grid-cols-2">
            {painPoints.map((point, index) => (
              <Card key={index} className="text-left">
                <CardContent className="p-4 flex items-start gap-3">
                  <X className="w-5 h-5 text-destructive mt-0.5 flex-shrink-0" />
                  <span className="text-muted-foreground">{point}</span>
                </CardContent>
              </Card>
            ))}
          </div>
          <div className="mt-8 p-6 bg-primary/10 rounded-lg">
            <h3 className="text-xl font-semibold mb-2">The Solution?</h3>
            <p className="text-muted-foreground">
              A complete system that not only looks professional but actually converts visitors into buyers 
              and turns one-time customers into repeat purchasers.
            </p>
          </div>
        </div>
      </section>

      {/* Packages Section */}
      <section id="services" className="py-16">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Choose Your Growth Path</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Whether you're launching, growing, or scaling - we have the perfect package for your fashion brand
            </p>
          </div>
          
          <div className="grid gap-8 lg:grid-cols-3">
            {packages.map((pkg, index) => (
              <Card 
                key={index} 
                className={`relative ${pkg.popular ? 'border-primary shadow-xl scale-105 bg-primary/5' : ''}`}
              >
                {pkg.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-primary text-primary-foreground px-4">
                      <Crown className="w-3 h-3 mr-1" />
                      Most Popular
                    </Badge>
                  </div>
                )}
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl">{pkg.name}</CardTitle>
                  <div className="text-4xl font-bold text-primary mb-2">{pkg.price}</div>
                  <p className="text-sm font-medium text-muted-foreground">{pkg.subtitle}</p>
                  <p className="text-muted-foreground">{pkg.description}</p>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3 mb-6">
                    {pkg.features.map((feature, i) => (
                      <li key={i} className="flex items-start gap-2">
                        <Check className="w-4 h-4 text-primary flex-shrink-0 mt-0.5" />
                        <span className="text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  
                  <div className="bg-secondary/50 p-4 rounded-lg mb-6">
                    <div className="flex items-center gap-2 mb-2">
                      <Target className="w-4 h-4 text-primary" />
                      <span className="font-medium text-sm">Outcome:</span>
                    </div>
                    <p className="text-sm text-muted-foreground">{pkg.outcome}</p>
                  </div>
                  
                  <Button className="w-full" variant={pkg.popular ? "default" : "outline"}>
                    {pkg.popular && <Sparkles className="w-4 h-4 mr-2" />}
                    {pkg.cta}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Add-ons Section */}
      <section className="py-16 bg-muted/50">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Need More? Add Power Boosters Anytime</h2>
            <p className="text-muted-foreground">
              Enhance your package with these specialized services
            </p>
          </div>
          
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {addOns.map((addon, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6 text-center">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center text-primary mx-auto mb-4">
                    <Gift className="w-6 h-6" />
                  </div>
                  <h3 className="font-semibold mb-2">{addon.name}</h3>
                  <p className="text-sm text-muted-foreground mb-4">{addon.description}</p>
                  <Badge variant="secondary" className="text-primary font-semibold">
                    {addon.price}
                  </Badge>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Proof Section */}
      <section id="portfolio" className="py-16">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Imagine Your Store Looking Like This</h2>
            <p className="text-muted-foreground">
              Real transformations from fashion brands we've helped scale
            </p>
          </div>
          
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {[1, 2, 3, 4, 5, 6].map((index) => (
              <Card key={index} className="overflow-hidden hover:shadow-lg transition-shadow">
                <div className="aspect-video bg-gradient-to-br from-primary/20 to-secondary/20 flex items-center justify-center">
                  <div className="text-center">
                    <Shirt className="w-16 h-16 text-primary mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">Store Mockup {index}</p>
                  </div>
                </div>
                <CardContent className="p-6">
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Before</span>
                      <span className="text-sm font-medium text-destructive">$500/month</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">After</span>
                      <span className="text-sm font-medium text-primary">$3,500/month</span>
                    </div>
                    <div className="flex items-center gap-1 justify-center pt-2">
                      {Array.from({ length: 5 }).map((_, i) => (
                        <Star key={i} className="w-4 h-4 fill-primary text-primary" />
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-muted/50">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
            <Card className="text-center">
              <CardContent className="p-6">
                <div className="text-4xl font-bold text-primary mb-2">200+</div>
                <div className="text-muted-foreground">Fashion Brands Helped</div>
              </CardContent>
            </Card>
            <Card className="text-center">
              <CardContent className="p-6">
                <div className="text-4xl font-bold text-primary mb-2">85%</div>
                <div className="text-muted-foreground">Increase in Sales</div>
              </CardContent>
            </Card>
            <Card className="text-center">
              <CardContent className="p-6">
                <div className="text-4xl font-bold text-primary mb-2">60%</div>
                <div className="text-muted-foreground">Return Customer Rate</div>
              </CardContent>
            </Card>
            <Card className="text-center">
              <CardContent className="p-6">
                <div className="text-4xl font-bold text-primary mb-2">30</div>
                <div className="text-muted-foreground">Days Average Setup</div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-16">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid gap-12 lg:grid-cols-2 items-center">
            <div>
              <h2 className="text-3xl font-bold mb-6">Why Fashion Brands Choose Us</h2>
              <div className="space-y-6">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center text-primary">
                    <TrendingUp className="w-6 h-6" />
                  </div>
                  <div>
                    <h3 className="font-semibold mb-2">Proven Sales Systems</h3>
                    <p className="text-muted-foreground">We don't just make your store look pretty - we build systems that actually sell and convert visitors into loyal customers.</p>
                  </div>
                </div>
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center text-primary">
                    <Users className="w-6 h-6" />
                  </div>
                  <div>
                    <h3 className="font-semibold mb-2">Customer Retention Focus</h3>
                    <p className="text-muted-foreground">We help you build a community of repeat customers who keep coming back for more, not just one-time buyers.</p>
                  </div>
                </div>
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center text-primary">
                    <BarChart3 className="w-6 h-6" />
                  </div>
                  <div>
                    <h3 className="font-semibold mb-2">Data-Driven Growth</h3>
                    <p className="text-muted-foreground">Every decision is backed by data and analytics to ensure your fashion brand grows sustainably and profitably.</p>
                  </div>
                </div>
              </div>
            </div>
            <div className="space-y-4">
              <Card className="p-6 bg-gradient-to-r from-primary/10 to-secondary/10">
                <div className="flex items-center gap-4">
                  <ShoppingBag className="w-8 h-8 text-primary" />
                  <div>
                    <h3 className="font-semibold">Complete E-commerce Setup</h3>
                    <p className="text-sm text-muted-foreground">From store design to payment processing</p>
                  </div>
                </div>
              </Card>
              <Card className="p-6">
                <div className="flex items-center gap-4">
                  <Palette className="w-8 h-8 text-primary" />
                  <div>
                    <h3 className="font-semibold">Brand Identity & Design</h3>
                    <p className="text-sm text-muted-foreground">Professional branding that stands out</p>
                  </div>
                </div>
              </Card>
              <Card className="p-6">
                <div className="flex items-center gap-4">
                  <Zap className="w-8 h-8 text-primary" />
                  <div>
                    <h3 className="font-semibold">Marketing Automation</h3>
                    <p className="text-sm text-muted-foreground">Systems that sell while you sleep</p>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section id="contact" className="py-16 bg-gradient-to-r from-primary/10 to-secondary/10">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">Ready to Grow Your Fashion Brand?</h2>
          <p className="text-xl text-muted-foreground mb-8">
            Whether you're just launching or ready to scale, we'll set up the system that makes your brand sell consistently.
          </p>
          <div className="flex gap-4 justify-center mb-8">
            <Button size="lg" className="text-lg px-8">
              <Mail className="w-5 h-5 mr-2" />
              Book Your Free Consultation
            </Button>
            <Button size="lg" variant="outline" className="text-lg px-8">
              <Phone className="w-5 h-5 mr-2" />
              Start Growing Today
            </Button>
          </div>
          <div className="flex justify-center gap-8 text-sm text-muted-foreground">
            <div className="flex items-center gap-2">
              <Check className="w-4 h-4 text-primary" />
              Free Strategy Session
            </div>
            <div className="flex items-center gap-2">
              <Check className="w-4 h-4 text-primary" />
              Custom Growth Plan
            </div>
            <div className="flex items-center gap-2">
              <Check className="w-4 h-4 text-primary" />
              85% Average Sales Increase
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default FashionServiceApp;