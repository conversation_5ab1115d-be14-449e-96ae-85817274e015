import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Link } from 'react-router-dom';
import {
  TrendingUp,
  Globe,
  Video,
  FileText,
  Share2,
  BarChart3,
  Target,
  Zap,
  Users,
  Mail,
  Phone,
  ArrowRight,
  Check,
  BookOpen,
  Megaphone,
  Camera,
  PenTool,
  Database,
  MonitorPlay,
  Star,
  Quote,
  ExternalLink,
  Play,
  Award,
  Rocket,
  TrendingDown,
  Eye,
  MousePointer,
  DollarSign,
  Edit,
  ArrowLeft,
  Home
} from 'lucide-react';
import { BlogList } from '@/components/blog/BlogList';
import { BlogPost } from '@/components/blog/BlogPost';
//import BlogAdmin from '@/components/blog/BlogAdmin';
import  BlogAdmin  from '../blog-admin-test/BlogAdmin';
import { toast } from 'sonner';
import { blogStorage } from '@/utils/blogStorage';
import { useAnalytics } from '@/hooks/useAnalytics';
import { useServiceSEO } from '@/hooks/useSEO';

const MarketingServiceApp: React.FC = () => {
  const [currentView, setCurrentView] = useState<'home' | 'blog' | 'blog-post' | 'admin'>('home');
  const [currentBlogSlug, setCurrentBlogSlug] = useState<string>('');
  const [activeSection, setActiveSection] = useState<string>('home');

  // Analytics and SEO hooks
  const analytics = useAnalytics();
  useServiceSEO(
    'Professional Marketing Services - Digital Growth Solutions',
    'Transform your business with our comprehensive marketing services including web development, content creation, SEO, and digital strategy.',
    'Marketing Services',
    ['marketing', 'digital marketing', 'web development', 'SEO', 'content creation', 'social media']
  );

  // Initialize sample data for localhost
  React.useEffect(() => {
    const initializeSampleData = async () => {
      try {
        const existingPosts = await blogStorage.getPosts();

        // Only add sample data if no posts exist and we're on localhost
        if (existingPosts.length === 0 && window.location.hostname === 'localhost') {
          const samplePosts = [
            {
              title: 'Welcome to Our Marketing Blog',
              content: '<h2>Welcome to the Future of Marketing</h2><p>We\'re excited to share insights, strategies, and trends that will help your business grow. Our team of marketing experts brings you the latest in digital marketing, content strategy, and business growth.</p><h3>What You\'ll Find Here</h3><ul><li>Digital marketing strategies</li><li>Content creation tips</li><li>SEO best practices</li><li>Social media insights</li><li>Business growth tactics</li></ul><p>Stay tuned for regular updates and actionable advice that you can implement immediately.</p>',
              excerpt: 'Welcome to our marketing blog where we share expert insights, strategies, and trends to help your business grow.',
              category: 'Marketing',
              tags: ['welcome', 'marketing', 'blog', 'strategy'],
              featured_image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=400&fit=crop',
              is_featured: true,
              published: true,
              view_count: 0,
              seo_title: 'Welcome to Our Marketing Blog - Expert Insights & Strategies',
              seo_description: 'Discover expert marketing insights, strategies, and trends to grow your business. Get actionable advice from our team of marketing professionals.'
            },
            {
              title: 'Digital Marketing Trends for 2024',
              content: '<h2>The Top Digital Marketing Trends Shaping 2024</h2><p>As we navigate through 2024, the digital marketing landscape continues to evolve at breakneck speed. Here are the key trends every marketer should know:</p><h3>1. AI-Powered Personalization</h3><p>Artificial intelligence is revolutionizing how we deliver personalized experiences to customers. From chatbots to recommendation engines, AI is making marketing more efficient and effective.</p><h3>2. Video-First Content Strategy</h3><p>Video content continues to dominate social media platforms. Short-form videos, live streaming, and interactive video content are driving engagement like never before.</p><h3>3. Voice Search Optimization</h3><p>With the rise of smart speakers and voice assistants, optimizing for voice search is becoming crucial for businesses.</p><p>Stay ahead of the curve by implementing these trends in your marketing strategy.</p>',
              excerpt: 'Discover the top digital marketing trends for 2024, including AI-powered personalization, video-first content, and voice search optimization.',
              category: 'Marketing',
              tags: ['digital marketing', 'trends', '2024', 'AI', 'video content'],
              featured_image: 'https://images.unsplash.com/photo-1432888622747-4eb9a8efeb07?w=800&h=400&fit=crop',
              is_featured: true,
              published: true,
              view_count: 0,
              seo_title: 'Digital Marketing Trends for 2024 - Stay Ahead of the Curve',
              seo_description: 'Explore the top digital marketing trends for 2024 including AI personalization, video content strategies, and voice search optimization.'
            },
            {
              title: 'Content Marketing Best Practices',
              content: '<h2>Mastering Content Marketing: Best Practices for Success</h2><p>Content marketing remains one of the most effective ways to attract and engage your target audience. Here\'s how to do it right:</p><h3>Know Your Audience</h3><p>Understanding your audience is the foundation of successful content marketing. Create detailed buyer personas and tailor your content to their needs, pain points, and interests.</p><h3>Create Valuable Content</h3><p>Focus on creating content that provides real value to your audience. This could be educational, entertaining, or inspirational content that helps solve their problems.</p><h3>Consistency is Key</h3><p>Maintain a consistent publishing schedule to keep your audience engaged and coming back for more.</p><h3>Measure and Optimize</h3><p>Use analytics to track your content performance and continuously optimize your strategy based on what works best.</p>',
              excerpt: 'Learn the essential content marketing best practices to create valuable content that engages your audience and drives business results.',
              category: 'Marketing',
              tags: ['content marketing', 'best practices', 'strategy', 'audience'],
              featured_image: 'https://images.unsplash.com/photo-1552664730-d307ca884978?w=800&h=400&fit=crop',
              is_featured: false,
              published: true,
              view_count: 0,
              seo_title: 'Content Marketing Best Practices - Create Engaging Content',
              seo_description: 'Master content marketing with proven best practices for creating valuable, engaging content that drives business results.'
            }
          ];

          for (const post of samplePosts) {
            await blogStorage.savePost(post);
          }

          console.log('Sample blog posts initialized for localhost');
        }
      } catch (error) {
        console.error('Error initializing sample data:', error);
      }
    };

    initializeSampleData();
  }, []);

  const [contactForm, setContactForm] = useState({
    name: '',
    email: '',
    company: '',
    phone: '',
    service: '',
    budget: '',
    message: ''
  });

  // Blog posts are initialized by the centralized blogStorage system

  // Testimonials data
  const testimonials = [
    {
      id: 1,
      name: "Sarah Johnson",
      company: "TechStart Inc.",
      role: "CEO",
      image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
      rating: 5,
      text: "IssyLabs transformed our marketing strategy completely. Our lead generation increased by 300% in just 3 months!"
    },
    {
      id: 2,
      name: "Michael Chen",
      company: "GrowthCorp",
      role: "Marketing Director",
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
      rating: 5,
      text: "The multi-domain strategy and content distribution helped us reach global markets we never thought possible."
    },
    {
      id: 3,
      name: "Emily Rodriguez",
      company: "Fashion Forward",
      role: "Founder",
      image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
      rating: 5,
      text: "Professional, results-driven, and incredibly creative. Our brand visibility skyrocketed!"
    }
  ];

  // Portfolio data
  const portfolioItems = [
    {
      id: 1,
      title: "E-commerce Growth Campaign",
      category: "Digital Marketing",
      image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=500&h=300&fit=crop",
      results: "450% ROI increase",
      description: "Complete digital transformation for online retail brand",
      link: "#"
    },
    {
      id: 2,
      title: "SaaS Lead Generation",
      category: "Content Marketing",
      image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=500&h=300&fit=crop",
      results: "300% lead increase",
      description: "Strategic content marketing and automation setup",
      link: "#"
    },
    {
      id: 3,
      title: "Brand Awareness Campaign",
      category: "Social Media",
      image: "https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=500&h=300&fit=crop",
      results: "2M+ impressions",
      description: "Multi-platform brand awareness and engagement strategy",
      link: "#"
    }
  ];

  const marketingServices = [
    {
      icon: <Globe className="w-8 h-8" />,
      title: "Domain & Web Presence",
      description: "70+ domain distribution, website optimization, and online presence management.",
      features: ["Multi-domain strategy", "SEO optimization", "Brand consistency", "Global reach"],
      domains: "70+ Domains",
      price: "From $2,999/month"
    },
    {
      icon: <FileText className="w-8 h-8" />,
      title: "Content Publishing",
      description: "Strategic content creation and distribution across all major platforms.",
      features: ["Blog content", "Press releases", "Article distribution", "Content calendar"],
      domains: "Content Strategy",
      price: "From $1,999/month"
    },
    {
      icon: <Share2 className="w-8 h-8" />,
      title: "Social Media Management",
      description: "Complete social media strategy, content creation, and community management.",
      features: ["Platform management", "Content creation", "Community engagement", "Analytics tracking"],
      domains: "All Platforms"
    },
    {
      icon: <Video className="w-8 h-8" />,
      title: "Video Production",
      description: "Professional video content for marketing, tutorials, and brand storytelling.",
      features: ["Promotional videos", "Tutorial content", "Brand storytelling", "Platform optimization"],
      domains: "Multi-format"
    },
    {
      icon: <Target className="w-8 h-8" />,
      title: "Digital Advertising",
      description: "Targeted ad campaigns across Google, Facebook, LinkedIn, and emerging platforms.",
      features: ["PPC campaigns", "Social ads", "Retargeting", "Performance optimization"],
      domains: "Cross-platform"
    },
    {
      icon: <BarChart3 className="w-8 h-8" />,
      title: "Analytics & Reporting",
      description: "Comprehensive data analysis and performance reporting across all channels.",
      features: ["Performance tracking", "ROI analysis", "Custom dashboards", "Strategic insights"],
      domains: "Data-driven"
    }
  ];

  const packages = [
    {
      name: "Growth Starter",
      price: "$499/mo",
      description: "Perfect for growing businesses",
      features: [
        "10 Domain Distribution",
        "Social Media Management (3 platforms)",
        "Monthly Content Calendar",
        "Basic Analytics Dashboard",
        "Email Marketing Setup"
      ],
      popular: false,
      cta: "Start Growing"
    },
    {
      name: "Scale Master",
      price: "$1,299/mo",
      description: "Comprehensive marketing automation",
      features: [
        "35 Domain Distribution",
        "Full Social Media Suite (All platforms)",
        "Video Content Production (2/month)",
        "Advanced Analytics & Reporting",
        "PPC Campaign Management",
        "Content Publishing Network"
      ],
      popular: true,
      cta: "Scale Now"
    },
    {
      name: "Enterprise Domination",
      price: "$2,999/mo",
      description: "Complete market domination strategy",
      features: [
        "70+ Domain Distribution",
        "Omnichannel Marketing",
        "Weekly Video Production",
        "Custom Analytics Platform",
        "Dedicated Account Manager",
        "24/7 Campaign Optimization",
        "Global Market Expansion",
        "Competitive Intelligence"
      ],
      popular: false,
      cta: "Dominate Market"
    }
  ];

  const industries = [
    "E-commerce", "SaaS", "Healthcare", "Finance", "Real Estate", "Education", 
    "Technology", "Manufacturing", "Retail", "Professional Services", "Entertainment",
    "Non-profit", "Hospitality", "Automotive", "Fashion", "Food & Beverage"
  ];

  const handleBlogNavigation = (slug: string) => {
    setCurrentBlogSlug(slug);
    setCurrentView('blog-post');
    // Update URL hash so refresh works
    window.location.hash = `blog/${slug}`;
  };

  const scrollToSectionId = (id: string) => {
    // If not on home, go to home then scroll after a tick so DOM is present
    const doScroll = () => {
      const el = document.getElementById(id);
      if (el) el.scrollIntoView({ behavior: 'smooth' });
    };

    // set the active section immediately so nav highlights right away
    setActiveSection(id);

    if (currentView !== 'home') {
      setCurrentView('home');
      // wait for render then scroll
      setTimeout(doScroll, 220);
    } else {
      doScroll();
    }
  };

  // Observe sections on the home view and update activeSection on scroll
  React.useEffect(() => {
    if (currentView !== 'home') return;

    const ids = ['home', 'about', 'services', 'portfolio', 'testimonials', 'contact'];
    const elements = ids.map(id => document.getElementById(id)).filter(Boolean) as HTMLElement[];
    if (elements.length === 0) return;

    let observer: IntersectionObserver | null = null;
    observer = new IntersectionObserver(
      (entries) => {
        // pick the entry with largest intersectionRatio
        const visible = entries
          .filter(e => e.isIntersecting)
          .sort((a, b) => b.intersectionRatio - a.intersectionRatio);
        if (visible.length > 0) {
          const id = visible[0].target.id;
          setActiveSection(id);
        }
      },
      { threshold: [0.25, 0.5, 0.75] }
    );

    elements.forEach(el => observer?.observe(el));
    return () => observer?.disconnect();
  }, [currentView]);

  // Keep a passive scroll listener for potential future use, but DO NOT
  // redirect when near the top. Redirects caused surprising navigation when
  // users were viewing blog pages or deep sections.
  React.useEffect(() => {
    let lastY = window.scrollY;
    const onScroll = () => {
      try {
        const y = window.scrollY || window.pageYOffset || 0;
        lastY = y;
      } catch (e) {}
    };

    window.addEventListener('scroll', onScroll, { passive: true });
    return () => window.removeEventListener('scroll', onScroll);
  }, [currentView]);

  // support opening via hash (fallback for other pages)
  React.useEffect(() => {
    const checkHash = () => {
      const hash = window.location.hash.replace(/^#/, '');
      if (hash.startsWith('blog/')) {
        const slug = hash.replace('blog/', '');
        setCurrentBlogSlug(slug);
        setCurrentView('blog-post');
      } else if (hash === 'blog') {
        setCurrentView('blog');
      }

      if (hash === 'blog-admin') {
        setCurrentView('admin');
      }
    };

    const checkPath = () => {
      // if URL path ends with /admin, open admin
      try {
        const p = window.location.pathname || '';
        if (p.endsWith('/blog-admin')) setCurrentView('admin');
      } catch (e) {}
    };

    checkHash();
    checkPath();
    window.addEventListener('hashchange', checkHash);
    window.addEventListener('popstate', checkPath);
    return () => {
      window.removeEventListener('hashchange', checkHash);
      window.removeEventListener('popstate', checkPath);
    };
  }, []);

  const handleContactSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      // Track form submission with analytics
      analytics.trackFormComplete('marketing_service_contact_form');
      analytics.trackContactSubmission('marketing_service', contactForm.service);
      analytics.trackConversion('marketing_service_lead', 1);

      // Simulate email sending (replace with actual email service)
      const emailData = {
        to: '<EMAIL>',
        subject: `New Marketing Service Inquiry from ${contactForm.name}`,
        html: `
          <h2>New Marketing Service Inquiry</h2>
          <p><strong>Name:</strong> ${contactForm.name}</p>
          <p><strong>Email:</strong> ${contactForm.email}</p>
          <p><strong>Company:</strong> ${contactForm.company}</p>
          <p><strong>Phone:</strong> ${contactForm.phone}</p>
          <p><strong>Service:</strong> ${contactForm.service}</p>
          <p><strong>Budget:</strong> ${contactForm.budget}</p>
          <p><strong>Message:</strong> ${contactForm.message}</p>
        `
      };

      // Store in localStorage for now (replace with actual email service)
      const existingInquiries = JSON.parse(localStorage.getItem('marketing_inquiries') || '[]');
      existingInquiries.push({
        ...contactForm,
        timestamp: new Date().toISOString(),
        id: Date.now().toString()
      });
      localStorage.setItem('marketing_inquiries', JSON.stringify(existingInquiries));

      toast.success('Thank you! Your inquiry has been sent. We\'ll get back to you within 24 hours.');

      // Reset form
      setContactForm({
        name: '',
        email: '',
        company: '',
        phone: '',
        service: '',
        budget: '',
        message: ''
      });
    } catch (error) {
      toast.error('Something went wrong. Please try again or contact us directly.');
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setContactForm(prev => ({ ...prev, [field]: value }));
  };

  const renderNavigation = () => (
    <nav className="bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-0 z-50 border-b">
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center gap-2">
            <TrendingUp className="w-8 h-8 text-primary" />
            <span className="text-xl font-bold">EasyLabs Marketing</span>
          </div>
          <div className="hidden md:flex items-center space-x-8">
            <button
              onClick={() => { scrollToSectionId('home'); window.location.hash = ''; setCurrentView('home'); }}
              className={`hover:text-primary transition-colors ${(currentView === 'home' && activeSection === 'home') ? 'text-primary font-medium' : ''}`}
            >
              Home
            </button>
            <button onClick={() => { scrollToSectionId('about'); }} className={`hover:text-primary transition-colors ${activeSection === 'about' ? 'text-primary font-medium' : ''}`}>About</button>
            <button onClick={() => { scrollToSectionId('services'); }} className={`hover:text-primary transition-colors ${activeSection === 'services' ? 'text-primary font-medium' : ''}`}>Services</button>
            <button onClick={() => { scrollToSectionId('portfolio'); }} className={`hover:text-primary transition-colors ${activeSection === 'portfolio' ? 'text-primary font-medium' : ''}`}>Portfolio</button>
            <button onClick={() => { scrollToSectionId('testimonials'); }} className={`hover:text-primary transition-colors ${activeSection === 'testimonials' ? 'text-primary font-medium' : ''}`}>Testimonials</button>
            <button onClick={() => { scrollToSectionId('contact'); }} className={`hover:text-primary transition-colors ${activeSection === 'contact' ? 'text-primary font-medium' : ''}`}>Contact</button>
            <button
              onClick={() => { setCurrentView('blog'); window.location.hash = 'blog'; }}
              className={`hover:text-primary transition-colors flex items-center gap-1 ${currentView === 'blog' ? 'text-primary font-medium' : ''}`}
            >
              <BookOpen className="w-4 h-4" />
              Blog
            </button>
          </div>
        </div>
      </div>
    </nav>
  );

  // Handle different views
  if (currentView === 'blog-post') {
    return (
      <div className="min-h-screen premium-gradient-bg">
        {renderNavigation()}
        <div className="pt-32">
          <BlogPost
            slug={currentBlogSlug}
            onBack={() => setCurrentView('blog')}
          />
        </div>

        {/* Footer - Match Real Estate Page Style */}
        <footer className="bg-background/90 py-12 px-4 sm:px-6 lg:px-8 border-t border-border backdrop-blur-sm">
          <div className="max-w-7xl mx-auto">
            <div className="grid md:grid-cols-4 gap-8">
              <div className="md:col-span-2">
                <div className="text-2xl font-bold text-foreground mb-4 flex items-center">
                  <Megaphone className="h-6 w-6 text-accent mr-2" />
                  IssyLabs
                </div>
                <p className="text-muted-foreground mb-4">
                  Your All-in-One Marketing Tech & Media Brand System
                </p>
                <p className="text-muted-foreground/80 mb-4">
                  Creative services are powered by our in-house partner IssyVibe Production.
                </p>
                <br />
                 <div className="space-y-4 text-3xl font-bold">
                                 {/* Email */}
                                 <a
                                   href="mailto:<EMAIL>"
                                   className="flex items-center gap-3 text-muted-foreground/80 hover:underline"
                                 >
                                   <Mail className="text-gray-500" />
                                   <span><EMAIL></span>
                                 </a>
                           <br/>
                                 {/* WhatsApp */}
                                 <a
                                   href="https://wa.me/12494336588"
                                   target="_blank"
                                   rel="noopener noreferrer"
                                   className="flex items-center gap-3 text-green-600 hover:underline"
                                 >
                                   <Phone className="text-green-500" />
                                   <span>+1 (249) 433‑6588‬</span>
                                 </a>
                               </div>

              </div>
              <div>
                <h4 className="text-foreground font-semibold mb-4">Quick Links</h4>
                <ul className="space-y-2">
                  <li><Link to="/" className="text-muted-foreground hover:text-accent transition-colors">Home</Link></li>
                  <li><a href="#about" className="text-muted-foreground hover:text-accent transition-colors">About</a></li>
                  <li><a href="#services" className="text-muted-foreground hover:text-accent transition-colors">Services</a></li>
                  <li><a href="#portfolio" className="text-muted-foreground hover:text-accent transition-colors">Portfolio</a></li>
                  <li><a href="#testimonials" className="text-muted-foreground hover:text-accent transition-colors">Testimonials</a></li>
                  <li><a href="#contact" className="text-muted-foreground hover:text-accent transition-colors">Contact</a></li>
                </ul>
              </div>
              <div>
                <h4 className="text-foreground font-semibold mb-4">Industries</h4>
                <ul className="space-y-2">
                  <li><Link to="/fashion" className="text-muted-foreground hover:text-accent transition-colors">Fashion</Link></li>
                  <li><Link to="/realestate" className="text-muted-foreground hover:text-accent transition-colors">Real Estate</Link></li>
                  <li><Link to="/music" className="text-muted-foreground hover:text-accent transition-colors">Music</Link></li>
                  <li><Link to="/coaching" className="text-muted-foreground hover:text-accent transition-colors">Coaching</Link></li>
                  <li><Link to="/video" className="text-muted-foreground hover:text-accent transition-colors">Video Creation</Link></li>
                  <li><Link to="/automation" className="text-muted-foreground hover:text-accent transition-colors">Automation</Link></li>
                  <li><Link to="/gym" className="text-muted-foreground hover:text-accent transition-colors">Gym & Fitness</Link></li>
                  <li><Link to="/partner" className="text-muted-foreground hover:text-accent transition-colors">Partner Services</Link></li>
                </ul>
              </div>
            </div>
            <div className="border-t border-border/50 mt-8 pt-8 text-center text-muted-foreground/80">
              <p>&copy; 2025 IssyLabs. All rights reserved.</p>
            </div>
          </div>
        </footer>
      </div>
    );
  }

  if (currentView === 'blog') {
    return (
      <div className="min-h-screen premium-gradient-bg">
        {renderNavigation()}
        <div className="pt-32 pb-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16 fade-in-up">
              <h1 className="text-5xl md:text-6xl font-bold mb-6 text-white">
                Marketing Insights & Trends
              </h1>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                Stay ahead with the latest marketing strategies, industry trends, and growth tactics from our experts
              </p>
            </div>
            <div className="fade-in-up" style={{animationDelay: '0.2s'}}>
              <BlogList
                onReadMore={handleBlogNavigation}
                category="Marketing"
                showSearch={false}
                showFilters={false}
                // full blog page: show all posts (no limit)
              />
            </div>
          </div>
        </div>

        {/* Footer - Match Real Estate Page Style */}
        <footer className="bg-background/90 py-12 px-4 sm:px-6 lg:px-8 border-t border-border backdrop-blur-sm">
          <div className="max-w-7xl mx-auto">
            <div className="grid md:grid-cols-4 gap-8">
              <div className="md:col-span-2">
                <div className="text-2xl font-bold text-foreground mb-4 flex items-center">
                  <Megaphone className="h-6 w-6 text-accent mr-2" />
                  IssyLabs
                </div>
                <p className="text-muted-foreground mb-4">
                  Your All-in-One Marketing Tech & Media Brand System
                </p>
                <p className="text-muted-foreground/80 mb-4">
                  Creative services are powered by our in-house partner IssyVibe Production.
                </p>
                <br />
                 <div className="space-y-4 text-3xl font-bold">
                                 {/* Email */}
                                 <a
                                   href="mailto:<EMAIL>"
                                   className="flex items-center gap-3 text-muted-foreground/80 hover:underline"
                                 >
                                   <Mail className="text-gray-500" />
                                   <span><EMAIL></span>
                                 </a>
                           <br/>
                                 {/* WhatsApp */}
                                 <a
                                   href="https://wa.me/12494336588"
                                   target="_blank"
                                   rel="noopener noreferrer"
                                   className="flex items-center gap-3 text-green-600 hover:underline"
                                 >
                                   <Phone className="text-green-500" />
                                   <span>+1 (249) 433‑6588‬</span>
                                 </a>
                               </div>

              </div>
              <div>
                <h4 className="text-foreground font-semibold mb-4">Quick Links</h4>
                <ul className="space-y-2">
                  <li><Link to="/" className="text-muted-foreground hover:text-accent transition-colors">Home</Link></li>
                  <li><a href="#about" className="text-muted-foreground hover:text-accent transition-colors">About</a></li>
                  <li><a href="#services" className="text-muted-foreground hover:text-accent transition-colors">Services</a></li>
                  <li><a href="#portfolio" className="text-muted-foreground hover:text-accent transition-colors">Portfolio</a></li>
                  <li><a href="#testimonials" className="text-muted-foreground hover:text-accent transition-colors">Testimonials</a></li>
                  <li><a href="#contact" className="text-muted-foreground hover:text-accent transition-colors">Contact</a></li>
                </ul>
              </div>
              <div>
                <h4 className="text-foreground font-semibold mb-4">Industries</h4>
                <ul className="space-y-2">
                  <li><Link to="/fashion" className="text-muted-foreground hover:text-accent transition-colors">Fashion</Link></li>
                  <li><Link to="/realestate" className="text-muted-foreground hover:text-accent transition-colors">Real Estate</Link></li>
                  <li><Link to="/music" className="text-muted-foreground hover:text-accent transition-colors">Music</Link></li>
                  <li><Link to="/coaching" className="text-muted-foreground hover:text-accent transition-colors">Coaching</Link></li>
                  <li><Link to="/video" className="text-muted-foreground hover:text-accent transition-colors">Video Creation</Link></li>
                  <li><Link to="/automation" className="text-muted-foreground hover:text-accent transition-colors">Automation</Link></li>
                  <li><Link to="/gym" className="text-muted-foreground hover:text-accent transition-colors">Gym & Fitness</Link></li>
                  <li><Link to="/partner" className="text-muted-foreground hover:text-accent transition-colors">Partner Services</Link></li>
                </ul>
              </div>
            </div>
            <div className="border-t border-border/50 mt-8 pt-8 text-center text-muted-foreground/80">
              <p>&copy; 2025 IssyLabs. All rights reserved.</p>
            </div>
          </div>
        </footer>
      </div>
    );
  }

  if (currentView === 'admin') {
    return (
      <div className="min-h-screen premium-gradient-bg">
        {renderNavigation()}
        <div className="pt-32">
          <BlogAdmin />
        </div>
      </div>
    );
  }

  // Main marketing service page
  return (
    <div className="min-h-screen premium-gradient-bg">
      {renderNavigation()}

      {/* Hero Section with Professional Background */}
      <section id="home" className="pt-32 pb-20 px-4 sm:px-6 lg:px-8 hero-gradient relative overflow-hidden">
        <div
          className="absolute inset-0 opacity-10"
          style={{
            backgroundImage: `url(https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80)`,
            backgroundSize: "cover",
            backgroundPosition: "center",
          }}
        ></div>
        <div className="max-w-7xl mx-auto text-center relative z-10">
          <div className="fade-in-up">
            <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight text-white">
              Scale Your Business with{' '}
              <span className="text-accent">
                70+ Domain Distribution
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 max-w-4xl mx-auto leading-relaxed">
              Complete marketing automation across publishing, social media, video content,
              and digital advertising. Dominate every channel, every platform, every market.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button
                size="lg"
                onClick={() => document.getElementById("portfolio")?.scrollIntoView({ behavior: "smooth" })}
                className="bg-accent text-accent-foreground hover:bg-accent/90 font-semibold px-8 py-4 text-lg shadow-lg hover-lift pulse-glow"
              >
                <Eye className="mr-2 h-5 w-5" />
                See Marketing Work
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button
                size="lg"
                variant="outline"
                onClick={() => document.getElementById("contact")?.scrollIntoView({ behavior: "smooth" })}
                className="border-white/30 bg-white/10 text-white hover:bg-white hover:text-background px-8 py-4 text-lg shadow-sm hover-lift backdrop-blur-sm"
              >
                <Rocket className="mr-2 h-5 w-5" />
                Start My Marketing Project
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Services Grid */}
      <section id="services" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 fade-in-up">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">
              Complete Marketing Ecosystem
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Integrated marketing services designed to maximize your reach, engagement, and conversions across all channels
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {marketingServices.map((service, index) => (
              <Card key={index} className="glass-effect hover-lift group">
                <CardHeader>
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-16 h-16 bg-accent/20 rounded-full flex items-center justify-center text-accent group-hover:bg-accent group-hover:text-accent-foreground transition-all">
                      {service.icon}
                    </div>
                    <Badge variant="secondary">
                      {service.domains}
                    </Badge>
                  </div>
                  <CardTitle className="text-xl text-white">{service.title}</CardTitle>
                  <div className="text-accent font-semibold">{service.price}</div>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground mb-6">{service.description}</p>
                  <ul className="space-y-3 mb-6">
                    {service.features.map((feature, i) => (
                      <li key={i} className="flex items-center gap-3 text-sm text-muted-foreground">
                        <Check className="w-4 h-4 text-accent flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <Button
                    className="w-full bg-accent text-accent-foreground hover:bg-accent/90 hover-lift"
                    onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
                  >
                    Get Started
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Choose Your Growth Plan Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 fade-in-up">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">
              Choose Your Growth Plan
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Scalable marketing solutions that grow with your business
            </p>
          </div>

          <div className="grid gap-8 lg:grid-cols-3">
            {packages.map((pkg, index) => (
              <Card
                key={index}
                className={`glass-effect hover-lift relative ${pkg.popular ? 'border-accent shadow-xl scale-105' : ''}`}
              >
                {pkg.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-accent text-accent-foreground px-4">Most Popular</Badge>
                  </div>
                )}
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl text-white">{pkg.name}</CardTitle>
                  <div className="text-4xl font-bold text-accent">{pkg.price}</div>
                  <p className="text-muted-foreground">{pkg.description}</p>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3 mb-6">
                    {pkg.features.map((feature, i) => (
                      <li key={i} className="flex items-center gap-2">
                        <Check className="w-4 h-4 text-accent flex-shrink-0" />
                        <span className="text-sm text-muted-foreground">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Button
                    className={`w-full hover-lift ${pkg.popular ? 'bg-accent text-accent-foreground hover:bg-accent/90' : 'border-accent text-accent hover:bg-accent hover:text-accent-foreground'}`}
                    variant={pkg.popular ? "default" : "outline"}
                    onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
                  >
                    {pkg.cta}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Portfolio Section */}
      <section id="portfolio" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 fade-in-up">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">
              Proven Results That Speak Volumes
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Real campaigns, real results. See how we've transformed businesses across industries.
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {portfolioItems.map((item) => (
              <Card key={item.id} className="glass-effect hover-lift group overflow-hidden">
                <div className="relative">
                  <img
                    src={item.image}
                    alt={item.title}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute top-4 left-4">
                    <Badge className="bg-accent text-accent-foreground">
                      {item.results}
                    </Badge>
                  </div>
                  <div className="absolute top-4 right-4">
                    <Button size="sm" variant="secondary" className="opacity-0 group-hover:opacity-100 transition-opacity hover-lift">
                      <ExternalLink className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
                <CardContent className="p-6">
                  <Badge variant="secondary" className="mb-3">
                    {item.category}
                  </Badge>
                  <h3 className="text-xl font-bold text-white mb-2">{item.title}</h3>
                  <p className="text-muted-foreground mb-4">{item.description}</p>
                  <Button
                    variant="outline"
                    className="w-full border-accent text-accent hover:bg-accent hover:text-accent-foreground hover-lift"
                    onClick={() => window.open(item.link, '_blank')}
                  >
                    View Case Study
                    <ExternalLink className="w-4 h-4 ml-2" />
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 fade-in-up">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">
              What Our Clients Say
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Don't just take our word for it. Here's what industry leaders say about our results.
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {testimonials.map((testimonial) => (
              <Card key={testimonial.id} className="glass-effect hover-lift">
                <CardContent className="p-6">
                  <div className="flex items-center mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="w-5 h-5 text-accent fill-current" />
                    ))}
                  </div>
                  <Quote className="w-8 h-8 text-accent mb-4" />
                  <p className="text-muted-foreground mb-6 italic">"{testimonial.text}"</p>
                  <div className="flex items-center gap-4">
                    <img
                      src={testimonial.image}
                      alt={testimonial.name}
                      className="w-12 h-12 rounded-full object-cover"
                    />
                    <div>
                      <div className="font-semibold text-white">{testimonial.name}</div>
                      <div className="text-accent text-sm">{testimonial.role}</div>
                      <div className="text-muted-foreground text-sm">{testimonial.company}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Blog Preview Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 fade-in-up">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">
              Marketing Insights & Strategies
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Stay ahead with the latest marketing trends, strategies, and growth tactics from our experts.
            </p>
          </div>

          <div className="mb-8">
            <BlogList
              onReadMore={handleBlogNavigation}
              category="Marketing"
              limit={6}
              showOnlyFeatured={true}
              showSearch={false}
              showFilters={false}
            />
          </div>

          <div className="text-center">
            <Button
              size="lg"
              variant="outline"
              className="border-accent text-accent hover:bg-accent hover:text-accent-foreground hover-lift"
              onClick={() => { setCurrentView('blog'); window.location.hash = 'blog'; }}
            >
              View All Articles
              <BookOpen className="w-5 h-5 ml-2" />
            </Button>
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section id="contact" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 fade-in-up">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">
              Ready to Scale Your Business?
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Get a free strategy consultation and see how our 70+ domain network can transform your business.
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <Card className="glass-effect">
              <CardHeader className="text-center">
                <CardTitle className="text-3xl text-white mb-4">Get Your Free Strategy Session</CardTitle>
                <p className="text-muted-foreground text-lg">
                  Fill out the form below and we'll get back to you within 24 hours with a custom marketing strategy.
                </p>
              </CardHeader>
              <CardContent className="p-8">
                <form onSubmit={handleContactSubmit} className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="name" className="text-white font-medium">Full Name *</Label>
                      <Input
                        id="name"
                        value={contactForm.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        className="bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-accent"
                        placeholder="Your full name"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email" className="text-white font-medium">Email Address *</Label>
                      <Input
                        id="email"
                        type="email"
                        value={contactForm.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        className="bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-accent"
                        placeholder="<EMAIL>"
                        required
                      />
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="company" className="text-white font-medium">Company</Label>
                      <Input
                        id="company"
                        value={contactForm.company}
                        onChange={(e) => handleInputChange('company', e.target.value)}
                        className="bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-accent"
                        placeholder="Your company name"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="phone" className="text-white font-medium">Phone Number</Label>
                      <Input
                        id="phone"
                        value={contactForm.phone}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                        className="bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-accent"
                        placeholder="+****************"
                      />
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="service" className="text-white font-medium">Service Interest</Label>
                      <select
                        id="service"
                        value={contactForm.service}
                        onChange={(e) => handleInputChange('service', e.target.value)}
                        className="w-full p-3 bg-white/5 border border-white/20 rounded-md text-white focus:border-accent focus:outline-none"
                      >
                        <option value="">Select a service</option>
                        <option value="domain-distribution">Domain Distribution</option>
                        <option value="content-marketing">Content Marketing</option>
                        <option value="social-media">Social Media Marketing</option>
                        <option value="video-marketing">Video Marketing</option>
                        <option value="full-package">Complete Package</option>
                      </select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="budget" className="text-white font-medium">Monthly Budget</Label>
                      <select
                        id="budget"
                        value={contactForm.budget}
                        onChange={(e) => handleInputChange('budget', e.target.value)}
                        className="w-full p-3 bg-white/5 border border-white/20 rounded-md text-white focus:border-accent focus:outline-none"
                      >
                        <option value="">Select budget range</option>
                        <option value="1000-5000">$1,000 - $5,000</option>
                        <option value="5000-10000">$5,000 - $10,000</option>
                        <option value="10000-25000">$10,000 - $25,000</option>
                        <option value="25000+">$25,000+</option>
                      </select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="message" className="text-white font-medium">Message *</Label>
                    <Textarea
                      id="message"
                      value={contactForm.message}
                      onChange={(e) => handleInputChange('message', e.target.value)}
                      className="bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-accent min-h-[120px]"
                      placeholder="Tell us about your business goals and how we can help..."
                      required
                    />
                  </div>

                  <Button
                    type="submit"
                    className="w-full bg-accent text-accent-foreground hover:bg-accent/90 text-lg py-4 font-semibold hover-lift pulse-glow"
                  >
                    Get My Free Strategy Session
                    <Rocket className="w-5 h-5 ml-2" />
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-16">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid gap-12 lg:grid-cols-2 items-center">
            <div>
              <h2 className="text-3xl font-bold mb-6">Why Choose EasyLabs Marketing?</h2>
              <div className="space-y-6">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center text-primary">
                    <Globe className="w-6 h-6" />
                  </div>
                  <div>
                    <h3 className="font-semibold mb-2">Massive Distribution Network</h3>
                    <p className="text-muted-foreground">70+ domain distribution ensures your content reaches every corner of the internet.</p>
                  </div>
                </div>
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center text-primary">
                    <Zap className="w-6 h-6" />
                  </div>
                  <div>
                    <h3 className="font-semibold mb-2">Automated Excellence</h3>
                    <p className="text-muted-foreground">AI-powered campaign optimization running 24/7 to maximize your results.</p>
                  </div>
                </div>
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center text-primary">
                    <BarChart3 className="w-6 h-6" />
                  </div>
                  <div>
                    <h3 className="font-semibold mb-2">Data-Driven Results</h3>
                    <p className="text-muted-foreground">Every decision backed by real-time analytics and performance data.</p>
                  </div>
                </div>
              </div>
            </div>
            <div className="space-y-4">
              <Card className="p-6">
                <div className="flex items-center gap-4">
                  <Megaphone className="w-8 h-8 text-primary" />
                  <div>
                    <h3 className="font-semibold">Omnichannel Presence</h3>
                    <p className="text-sm text-muted-foreground">Be everywhere your customers are</p>
                  </div>
                </div>
              </Card>
              <Card className="p-6">
                <div className="flex items-center gap-4">
                  <MonitorPlay className="w-8 h-8 text-primary" />
                  <div>
                    <h3 className="font-semibold">Video Content Mastery</h3>
                    <p className="text-sm text-muted-foreground">Engaging videos that convert</p>
                  </div>
                </div>
              </Card>
              <Card className="p-6">
                <div className="flex items-center gap-4">
                  <Database className="w-8 h-8 text-primary" />
                  <div>
                    <h3 className="font-semibold">Advanced Analytics</h3>
                    <p className="text-sm text-muted-foreground">Deep insights for better decisions</p>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
  <section id="contact-cta" className="py-16 bg-gradient-to-r from-primary/10 to-secondary/10">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">Ready to Dominate Your Market?</h2>
          <p className="text-xl text-muted-foreground mb-8">
            Join 500+ businesses that have transformed their growth with our 70+ domain distribution network
          </p>
          <div className="flex gap-4 justify-center mb-8">
            <Button size="lg" className="text-lg px-8">
              <Mail className="w-5 h-5 mr-2" />
              Start Dominating Today
            </Button>
            <Button size="lg" variant="outline" className="text-lg px-8">
              <Phone className="w-5 h-5 mr-2" />
              Schedule Strategy Call
            </Button>
          </div>
          <p className="text-muted-foreground">
            Free consultation • Custom strategy • 300% average ROI increase
          </p>
        </div>
      </section>

      {/* Footer - Match Real Estate Page Style */}
      <footer className="bg-background/90 py-12 px-4 sm:px-6 lg:px-8 border-t border-border backdrop-blur-sm">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-4 gap-8">
            <div className="md:col-span-2">
              <div className="text-2xl font-bold text-foreground mb-4 flex items-center">
                <Megaphone className="h-6 w-6 text-accent mr-2" />
                IssyLabs
              </div>
              <p className="text-muted-foreground mb-4">
                Your All-in-One Marketing Tech & Media Brand System
              </p>
              <p className="text-muted-foreground/80 mb-4">
                Creative services are powered by our in-house partner IssyVibe Production.
              </p>
              <br />
               <div className="space-y-4 text-3xl font-bold">
                               {/* Email */}
                               <a
                                 href="mailto:<EMAIL>"
                                 className="flex items-center gap-3 text-muted-foreground/80 hover:underline"
                               >
                                 <Mail className="text-gray-500" />
                                 <span><EMAIL></span>
                               </a>
                         <br/>
                               {/* WhatsApp */}
                               <a
                                 href="https://wa.me/12494336588"
                                 target="_blank"
                                 rel="noopener noreferrer"
                                 className="flex items-center gap-3 text-green-600 hover:underline"
                               >
                                 <Phone className="text-green-500" />
                                 <span>+1 (249) 433‑6588‬</span>
                               </a>
                             </div>

            </div>
            <div>
              <h4 className="text-foreground font-semibold mb-4">Quick Links</h4>
              <ul className="space-y-2">
                <li><Link to="/" className="text-muted-foreground hover:text-accent transition-colors">Home</Link></li>
                <li><a href="#about" className="text-muted-foreground hover:text-accent transition-colors">About</a></li>
                <li><a href="#services" className="text-muted-foreground hover:text-accent transition-colors">Services</a></li>
                <li><a href="#portfolio" className="text-muted-foreground hover:text-accent transition-colors">Portfolio</a></li>
                <li><a href="#testimonials" className="text-muted-foreground hover:text-accent transition-colors">Testimonials</a></li>
                <li><a href="#contact" className="text-muted-foreground hover:text-accent transition-colors">Contact</a></li>
              </ul>
            </div>
            <div>
              <h4 className="text-foreground font-semibold mb-4">Industries</h4>
              <ul className="space-y-2">
                <li><Link to="/fashion" className="text-muted-foreground hover:text-accent transition-colors">Fashion</Link></li>
                <li><Link to="/realestate" className="text-muted-foreground hover:text-accent transition-colors">Real Estate</Link></li>
                <li><Link to="/music" className="text-muted-foreground hover:text-accent transition-colors">Music</Link></li>
                <li><Link to="/coaching" className="text-muted-foreground hover:text-accent transition-colors">Coaching</Link></li>
                <li><Link to="/video" className="text-muted-foreground hover:text-accent transition-colors">Video Creation</Link></li>
                <li><Link to="/automation" className="text-muted-foreground hover:text-accent transition-colors">Automation</Link></li>
                <li><Link to="/gym" className="text-muted-foreground hover:text-accent transition-colors">Gym & Fitness</Link></li>
                <li><Link to="/partner" className="text-muted-foreground hover:text-accent transition-colors">Partner Services</Link></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-border/50 mt-8 pt-8 text-center text-muted-foreground/80">
            <p>&copy; 2025 IssyLabs. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default MarketingServiceApp;