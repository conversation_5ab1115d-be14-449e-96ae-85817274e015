import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ExternalLink, Search, TrendingUp, Eye, Calendar } from 'lucide-react';
import { prettyLinks, PrettyLink } from '@/services/prettyLinks';

interface AffiliateLinksProps {
  category?: string;
  limit?: number;
  showSearch?: boolean;
  showStats?: boolean;
  variant?: 'grid' | 'list' | 'compact';
}

export const AffiliateLinks: React.FC<AffiliateLinksProps> = ({
  category,
  limit,
  showSearch = true,
  showStats = true,
  variant = 'grid'
}) => {
  const [links, setLinks] = useState<PrettyLink[]>([]);
  const [filteredLinks, setFilteredLinks] = useState<PrettyLink[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState(category || 'all');
  const [categories, setCategories] = useState<string[]>([]);

  useEffect(() => {
    loadLinks();
    loadCategories();
  }, []);

  useEffect(() => {
    filterLinks();
  }, [links, searchTerm, selectedCategory]);

  const loadLinks = () => {
    const allLinks = prettyLinks.getAllLinks();
    setLinks(allLinks);
  };

  const loadCategories = () => {
    const allLinks = prettyLinks.getAllLinks();
    const uniqueCategories = [...new Set(allLinks.map(link => link.category))];
    setCategories(uniqueCategories);
  };

  const filterLinks = () => {
    let filtered = links;

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(link => link.category === selectedCategory);
    }

    // Filter by search term
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(link =>
        link.title.toLowerCase().includes(searchLower) ||
        link.description?.toLowerCase().includes(searchLower) ||
        link.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }

    // Apply limit
    if (limit) {
      filtered = filtered.slice(0, limit);
    }

    setFilteredLinks(filtered);
  };

  const handleLinkClick = (link: PrettyLink) => {
    prettyLinks.trackAndRedirect(link.slug);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const LinkCard = ({ link }: { link: PrettyLink }) => (
    <Card className="hover-lift cursor-pointer group h-full glass-effect" onClick={() => handleLinkClick(link)}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg group-hover:text-accent transition-colors">
              {link.title}
            </CardTitle>
            <p className="text-sm text-muted-foreground mt-1">
              {link.description}
            </p>
          </div>
          <ExternalLink className="w-4 h-4 text-muted-foreground group-hover:text-accent transition-colors" />
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="text-xs">
              {link.category}
            </Badge>
            <span className="text-xs text-muted-foreground">
              /{link.slug}
            </span>
          </div>
          
          {showStats && (
            <div className="flex items-center gap-4 text-xs text-muted-foreground">
              <div className="flex items-center gap-1">
                <Eye className="w-3 h-3" />
                {link.clicks} clicks
              </div>
              <div className="flex items-center gap-1">
                <Calendar className="w-3 h-3" />
                {formatDate(link.created_at)}
              </div>
            </div>
          )}
          
          <div className="flex flex-wrap gap-1">
            {link.tags.slice(0, 3).map(tag => (
              <Badge key={tag} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
            {link.tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{link.tags.length - 3}
              </Badge>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const LinkListItem = ({ link }: { link: PrettyLink }) => (
    <Card className="hover-lift cursor-pointer group glass-effect" onClick={() => handleLinkClick(link)}>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <h3 className="font-semibold group-hover:text-accent transition-colors">
                {link.title}
              </h3>
              <Badge variant="secondary" className="text-xs">
                {link.category}
              </Badge>
            </div>
            <p className="text-sm text-muted-foreground mb-2">
              {link.description}
            </p>
            <div className="flex items-center gap-4 text-xs text-muted-foreground">
              <span>/{link.slug}</span>
              {showStats && (
                <>
                  <div className="flex items-center gap-1">
                    <Eye className="w-3 h-3" />
                    {link.clicks} clicks
                  </div>
                  <div className="flex items-center gap-1">
                    <Calendar className="w-3 h-3" />
                    {formatDate(link.created_at)}
                  </div>
                </>
              )}
            </div>
          </div>
          <ExternalLink className="w-4 h-4 text-muted-foreground group-hover:text-accent transition-colors" />
        </div>
      </CardContent>
    </Card>
  );

  if (filteredLinks.length === 0) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-semibold mb-2">No affiliate links found</h3>
        <p className="text-muted-foreground">
          {searchTerm ? 'Try adjusting your search terms' : 'No affiliate links available yet'}
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {showSearch && (
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="Search affiliate links..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-full sm:w-48">
              <SelectValue placeholder="All Categories" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {categories.map(cat => (
                <SelectItem key={cat} value={cat}>{cat}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}

      {variant === 'grid' && (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredLinks.map(link => (
            <LinkCard key={link.id} link={link} />
          ))}
        </div>
      )}

      {variant === 'list' && (
        <div className="space-y-4">
          {filteredLinks.map(link => (
            <LinkListItem key={link.id} link={link} />
          ))}
        </div>
      )}

      {variant === 'compact' && (
        <div className="grid gap-3 sm:grid-cols-2">
          {filteredLinks.map(link => (
            <Button
              key={link.id}
              variant="outline"
              className="justify-between h-auto p-4 hover:bg-accent hover:text-accent-foreground"
              onClick={() => handleLinkClick(link)}
            >
              <div className="text-left">
                <div className="font-medium">{link.title}</div>
                <div className="text-xs text-muted-foreground">/{link.slug}</div>
              </div>
              <ExternalLink className="w-4 h-4" />
            </Button>
          ))}
        </div>
      )}
    </div>
  );
};

export default AffiliateLinks;
