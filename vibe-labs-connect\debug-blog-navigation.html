<!DOCTYPE html>
<html>
<head>
    <title>Debug Blog Navigation</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            min-height: 100vh;
        }
        .container { 
            max-width: 1000px; 
            margin: 0 auto; 
            background: rgba(255,255,255,0.1); 
            padding: 30px; 
            border-radius: 20px; 
            backdrop-filter: blur(10px); 
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .test-section { 
            background: rgba(255,255,255,0.05); 
            padding: 25px; 
            margin: 25px 0; 
            border-radius: 15px; 
            border-left: 5px solid #00ff88;
        }
        .result { 
            margin: 15px 0; 
            padding: 15px; 
            border-radius: 10px; 
            border-left: 4px solid #00ff88; 
            background: rgba(255,255,255,0.1); 
        }
        .success { border-left-color: #00ff88; background: rgba(0,255,136,0.1); }
        .error { border-left-color: #ff4757; background: rgba(255,71,87,0.1); }
        .warning { border-left-color: #ffa502; background: rgba(255,165,2,0.1); }
        .info { border-left-color: #3742fa; background: rgba(55,66,250,0.1); }
        
        button { 
            margin: 8px; 
            padding: 12px 24px; 
            border: none; 
            border-radius: 8px; 
            cursor: pointer; 
            font-size: 14px; 
            font-weight: 600; 
            transition: all 0.3s ease; 
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        button:hover { 
            transform: translateY(-2px); 
            box-shadow: 0 5px 15px rgba(0,0,0,0.3); 
        }
        
        pre { 
            background: rgba(0,0,0,0.3); 
            padding: 15px; 
            border-radius: 8px; 
            overflow-x: auto; 
            font-size: 12px;
            border: 1px solid rgba(255,255,255,0.1);
        }
        .blog-card {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            margin: 10px 0;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .blog-card:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐛 Debug Blog Navigation</h1>
        <p>Testing blog post storage and navigation functionality</p>
        
        <div class="test-section">
            <h3>📊 Blog Storage Test</h3>
            <button onclick="testBlogStorage()">Test Blog Storage</button>
            <button onclick="listAllPosts()">List All Posts</button>
            <button onclick="testSlugGeneration()">Test Slug Generation</button>
            <div id="storage-results"></div>
        </div>
        
        <div class="test-section">
            <h3>🔗 Navigation Test</h3>
            <button onclick="testNavigation()">Test Navigation</button>
            <button onclick="simulateCardClick()">Simulate Card Click</button>
            <div id="navigation-results"></div>
        </div>
        
        <div class="test-section">
            <h3>📝 Blog Posts</h3>
            <div id="blog-posts"></div>
        </div>
    </div>

    <script>
        function addResult(containerId, message, type = 'result') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = message;
            container.appendChild(div);
        }
        
        function testBlogStorage() {
            addResult('storage-results', '🔄 Testing blog storage...', 'info');
            
            // Check localStorage
            const stored = localStorage.getItem('blog_posts');
            if (stored) {
                try {
                    const posts = JSON.parse(stored);
                    addResult('storage-results', `✅ Found ${posts.length} posts in localStorage`, 'success');
                    
                    // Check for marketing posts
                    const marketingPosts = posts.filter(p => p.category === 'Marketing');
                    addResult('storage-results', `📊 Marketing posts: ${marketingPosts.length}`, 'info');
                    
                    // Check slugs
                    const slugs = posts.map(p => p.slug).filter(Boolean);
                    addResult('storage-results', `🔗 Posts with slugs: ${slugs.length}`, 'info');
                    
                    if (slugs.length > 0) {
                        addResult('storage-results', `Sample slugs: ${slugs.slice(0, 3).join(', ')}`, 'info');
                    }
                    
                } catch (error) {
                    addResult('storage-results', `❌ Error parsing stored posts: ${error.message}`, 'error');
                }
            } else {
                addResult('storage-results', '⚠️ No posts found in localStorage', 'warning');
            }
        }
        
        function listAllPosts() {
            addResult('storage-results', '📋 Listing all blog posts...', 'info');
            
            const stored = localStorage.getItem('blog_posts');
            if (stored) {
                try {
                    const posts = JSON.parse(stored);
                    const blogPostsContainer = document.getElementById('blog-posts');
                    blogPostsContainer.innerHTML = '';
                    
                    posts.forEach((post, index) => {
                        const postDiv = document.createElement('div');
                        postDiv.className = 'blog-card';
                        postDiv.onclick = () => simulatePostClick(post.slug);
                        postDiv.innerHTML = `
                            <h4>${post.title}</h4>
                            <p><strong>Slug:</strong> ${post.slug || 'No slug'}</p>
                            <p><strong>Category:</strong> ${post.category}</p>
                            <p><strong>Published:</strong> ${post.published !== false ? 'Yes' : 'No'}</p>
                            <p><strong>ID:</strong> ${post.id}</p>
                        `;
                        blogPostsContainer.appendChild(postDiv);
                    });
                    
                    addResult('storage-results', `✅ Listed ${posts.length} posts`, 'success');
                } catch (error) {
                    addResult('storage-results', `❌ Error listing posts: ${error.message}`, 'error');
                }
            }
        }
        
        function testSlugGeneration() {
            addResult('storage-results', '🔄 Testing slug generation...', 'info');
            
            const testTitles = [
                'Welcome to Our Marketing Blog',
                'Digital Marketing Trends for 2024',
                'Content Marketing Best Practices'
            ];
            
            testTitles.forEach(title => {
                const slug = title.toLowerCase().replace(/[^a-z0-9\s]/g, '').replace(/\s+/g, '-');
                addResult('storage-results', `"${title}" → "${slug}"`, 'info');
            });
        }
        
        function testNavigation() {
            addResult('navigation-results', '🔄 Testing navigation functions...', 'info');
            
            // Test if we can access the marketing service app
            const currentUrl = window.location.href;
            addResult('navigation-results', `Current URL: ${currentUrl}`, 'info');
            
            // Test hash navigation
            const testSlug = 'welcome-to-our-marketing-blog';
            const hashUrl = `${currentUrl.split('#')[0]}#blog/${testSlug}`;
            addResult('navigation-results', `Test hash URL: ${hashUrl}`, 'info');
            
            // Test if we can find the post
            const stored = localStorage.getItem('blog_posts');
            if (stored) {
                const posts = JSON.parse(stored);
                const foundPost = posts.find(p => p.slug === testSlug);
                if (foundPost) {
                    addResult('navigation-results', `✅ Found post: ${foundPost.title}`, 'success');
                } else {
                    addResult('navigation-results', `❌ Post not found with slug: ${testSlug}`, 'error');
                }
            }
        }
        
        function simulateCardClick() {
            addResult('navigation-results', '🔄 Simulating blog card click...', 'info');
            
            const stored = localStorage.getItem('blog_posts');
            if (stored) {
                const posts = JSON.parse(stored);
                const marketingPosts = posts.filter(p => p.category === 'Marketing');
                
                if (marketingPosts.length > 0) {
                    const firstPost = marketingPosts[0];
                    simulatePostClick(firstPost.slug);
                } else {
                    addResult('navigation-results', '❌ No marketing posts found', 'error');
                }
            }
        }
        
        function simulatePostClick(slug) {
            addResult('navigation-results', `🖱️ Simulating click on post with slug: ${slug}`, 'info');
            
            // This simulates what happens when a blog card is clicked
            const marketingServiceUrl = 'http://localhost:8082/#blog/' + slug;
            addResult('navigation-results', `Would navigate to: ${marketingServiceUrl}`, 'info');
            
            // Test if we can find the post
            const stored = localStorage.getItem('blog_posts');
            if (stored) {
                const posts = JSON.parse(stored);
                const foundPost = posts.find(p => p.slug === slug);
                if (foundPost) {
                    addResult('navigation-results', `✅ Post found: ${foundPost.title}`, 'success');
                    addResult('navigation-results', `<pre>${JSON.stringify(foundPost, null, 2)}</pre>`, 'info');
                } else {
                    addResult('navigation-results', `❌ Post not found with slug: ${slug}`, 'error');
                    
                    // Show available slugs
                    const availableSlugs = posts.map(p => p.slug).filter(Boolean);
                    addResult('navigation-results', `Available slugs: ${availableSlugs.join(', ')}`, 'warning');
                }
            }
        }
        
        // Auto-run tests on load
        window.onload = function() {
            setTimeout(() => {
                testBlogStorage();
                listAllPosts();
            }, 500);
        };
    </script>
</body>
</html>
