import { useEffect } from 'react';
import { blogStorage } from '@/utils/blogStorage';

/**
 * Hook to initialize blog posts with sample data if none exist
 * This ensures the blog system always has content to display
 */
export const useBlogInitialization = () => {
  useEffect(() => {
    const initializeBlog = async () => {
      try {
        console.log('🔄 Initializing blog system...');
        await blogStorage.initializeSamplePosts();
        console.log('✅ Blog system initialized');
      } catch (error) {
        console.error('❌ Error initializing blog system:', error);
      }
    };

    initializeBlog();
  }, []);
};

export default useBlogInitialization;
