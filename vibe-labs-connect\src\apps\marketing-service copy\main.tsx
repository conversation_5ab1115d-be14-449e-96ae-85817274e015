import React from "react";
import ReactD<PERSON> from "react-dom/client";
import { <PERSON><PERSON>er<PERSON>out<PERSON> } from "react-router-dom";  // ⬅️ add this
import "../../index.css";
import      VoiceServiceApp from "./VoiceServiceApp.tsx";

ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <BrowserRouter>
      <VoiceServiceApp />
    </BrowserRouter>
  </React.StrictMode>
);
    


