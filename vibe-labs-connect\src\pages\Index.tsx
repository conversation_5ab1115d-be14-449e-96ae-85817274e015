import { Leaf, MessageCircle } from "lucide-react";
import { FaWhatsapp } from "react-icons/fa";
import { HiOutlineMail } from "react-icons/hi";
// Floating button group for WhatsApp and Tawk.to
const FloatingButtonGroup = ({ section }) => {
  // WhatsApp
  const phone = '12494336588';
  const pageName = section ? ` (${section.charAt(0).toUpperCase() + section.slice(1)})` : '';
  const message = encodeURIComponent(`Hello, I am reaching out to you from your website. I would like to know more about your services.`);
  const whatsappUrl = `https://wa.me/${phone}?text=${message}`;

  // Tawk.to (Talk) - open dashboard in new tab
  const tawkUrl = 'https://dashboard.tawk.to/';

  return (
    <div className="fixed bottom-5 right-32 z-50 flex flex-col items-end gap-2">
      {/* <div className="flex gap-4"> */}
        {/* Tawk.to Button */}
       {/* <a
          href={tawkUrl}
          target="_blank"
          rel="noopener noreferrer"
          className="rounded-full bg-blue-600 hover:bg-blue-700 shadow-lg w-14 h-14 flex items-center justify-center transition-all"
          aria-label="Chat with us (Tawk.to)"
        >
          <MessageCircle className="w-7 h-7 text-white" />
        </a>*/}
        {/* WhatsApp Button */}
        <a
          href={whatsappUrl}
          target="_blank"
          rel="noopener noreferrer"
          className="rounded-full bg-green-500 hover:bg-green-600 shadow-lg w-14 h-14 flex items-center justify-center transition-all"
          aria-label="Chat on WhatsApp"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none" viewBox="0 0 24 24"><path fill="currentColor" d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.472-.148-.67.15-.198.297-.767.967-.94 1.166-.173.198-.347.223-.644.075-.297-.149-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.372-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.372-.01-.571-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.1 3.2 5.077 4.363.71.306 1.263.489 1.695.626.712.227 1.36.195 1.872.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.288.173-1.413-.074-.124-.272-.198-.57-.347z"/><path fill="currentColor" fillRule="evenodd" d="M12.004 2.003c-5.514 0-9.997 4.483-9.997 9.997 0 1.762.463 3.484 1.34 4.997L2.003 22l5.137-1.343c1.462.8 3.09 1.222 4.864 1.222 5.514 0 9.997-4.483 9.997-9.997 0-2.668-1.04-5.175-2.927-7.062-1.888-1.888-4.395-2.927-7.062-2.927zm-8.001 9.997c0-4.418 3.583-8.001 8.001-8.001 2.137 0 4.146.832 5.656 2.343 1.511 1.51 2.343 3.519 2.343 5.656 0 4.418-3.583 8.001-8.001 8.001-1.548 0-3.04-.44-4.32-1.272l-.307-.19-3.057.799.82-2.995-.2-.314C3.44 15.04 3.003 13.548 3.003 12z" clipRule="evenodd"/></svg>
        </a>
      {/* </div> */}
    </div>
  );
};
import { useState, useEffect } from "react";
import { ChevronRight, ArrowUpRight, Play, Code, Video, Palette, Users, Building, Music, Settings, Mail, Phone, Calendar, Star, CheckCircle, ExternalLink, Menu, X, Zap, Camera, Monitor, Headphones, Sparkles, Rocket, Eye, Plane, ShoppingBag } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Link } from "react-router-dom";
import { sendContactEmail, ContactFormData } from "@/services/emailService";
import YouTubeEmbed from "@/components/YouTubeEmbed";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { BlogList } from "@/components/blog/BlogList";
import { useBlogInitialization } from "@/hooks/useBlogInitialization";
import { useAnalytics } from "@/hooks/useAnalytics";
import { useHomepageSEO } from "@/hooks/useSEO";
import { title } from "process";

const Index = () => {
  const [activeSection, setActiveSection] = useState("home");
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    business_type: '',
    service_type: '',
    message: ''
  });

  // Initialize blog posts on component mount
  useBlogInitialization();

  // Initialize analytics tracking
  const analytics = useAnalytics();

  // Initialize SEO for homepage
  useHomepageSEO();

  const navItems = [
    { id: "home", label: "Home" },
    { id: "about", label: "About" },
    { id: "services", label: "Services" },
    { id: "portfolio", label: "Portfolio" },
    { id: "blog", label: "Blog" },
    { id: "testimonials", label: "Testimonials" },
    { id: "contact", label: "Contact" }
  ];

  // All industries (mainstream + high-demand)
  const niches = [
    { title: "Fashion", icon: Palette, route: "/fashion", description: "Style meets systems e-commerce, booking flows, and stunning visual content.", color: "from-pink-500 to-purple-600", image: "photo-1469334031218-e382a71b716b" },
    { title: "Real Estate", icon: Building, route: "/real-estate", description: "Property funnels that perform  listing platforms, CRM, and virtual tours.", color: "from-blue-500 to-cyan-600", image: "photo-1560518883-ce09059eeffa" },
    { title: "Music", icon: Music, route: "/music", description: "Sound meets storytelling  artist platforms, streaming, and music visuals.", color: "from-purple-500 to-pink-600", image: "photo-1493225457124-a3eb161ffa5f" },
    { title: "Coaching", icon: Users, route: "/coaching", description: "From expertise to empire  course platforms, booking systems, and promo content.", color: "from-emerald-500 to-teal-600", image: "photo-1507003211169-0a1dd7228f2d" },
    { title: "Video Creation", icon: Video, route: "/video", description: "Content that captures  publishing tools, analytics, and production services.", color: "from-orange-500 to-red-600", image: "photo-1574717024653-61fd2cf4d44d" },
    { title: "Automation", icon: Settings, route: "/automation", description: "Smart tools for smart teams  workflow automation and custom dashboards.", color: "from-indigo-500 to-blue-600", image: "photo-1518186285589-2f7649de83e0" },
    { title: "Gym & Fitness", icon: Headphones, route: "/gym", description: "Fitness tech and content  member management, booking systems, and workout videos.", color: "from-green-500 to-emerald-600", image: "photo-1534438327276-14e5300c3a48" },
    { title: "Farming & Agriculture", icon: Leaf, route: "/farming", description: "Sustainable practices,  agricultural systems and supply chain management.", color: "from-yellow-500 to-orange-600", image: "photo-1684954215462-cad9f3693b41" },
    { title: "Travel Agency", icon: Plane, route: "/travel", description: "Seamless travel bookings, itinerary management, and stunning destination content.", color: "from-teal-500 to-blue-500", image: "photo-1506744038136-46273834b3fb" },
    { title: "Tech Startup", icon: Rocket, route: "/tech", description: "Launch platforms, SaaS dashboards, and pitch-perfect branding for startups.", color: "from-gray-700 to-blue-700", image: "photo-1522071820081-009f0129c71c" },
    { title: "Financial Trader", icon: Monitor, route: "/finance", description: "Trading dashboards, analytics, and investor portals for finance professionals.", color: "from-green-700 to-gray-800", image: "photo-1563986768711-b3bde3dc821e" },
    { title: "Restaurant", icon: ShoppingBag, route: "/restaurant", description: "Online menus, booking systems, and mouthwatering food photography.", color: "from-red-500 to-yellow-500", image: "photo-1504674900247-0877df9cc836" },
    // High-demand/new industries
    { title: "Healthcare", icon: Star, route: "/healthcare", description: "Patient portals, appointment booking, and telehealth solutions.", color: "from-red-400 to-pink-500", image: "photo-1527613426441-4da17471b66d" },
    { title: "SaaS / Startup", icon: Rocket, route: "/saas", description: "SaaS dashboards, onboarding flows, and growth analytics for startups.", color: "from-blue-700 to-indigo-700", image: "photo-**********-2f36c7322d3b" },
    { title: "Innovation & Science Ventures", icon: Sparkles, route: "/innovation", description: "Showcase R&D, science, and innovation with custom platforms.", color: "from-purple-700 to-pink-700", image: "photo-1602052577122-f73b9710adba" },
    { title: "Education", icon: Calendar, route: "/education", description: "E-learning, course platforms, and student management systems.", color: "from-yellow-400 to-orange-500", image: "photo-1523240795612-9a054b0db644" },
    { title: "Legal Services", icon: Users, route: "/legal", description: "Client portals, document automation, and secure communication.", color: "from-gray-800 to-gray-500", image: "photo-1589994284978-c98238e44443" },
    { title: "Events & Entertainment", icon: Play, route: "/events", description: "Event booking, ticketing, and promotional content.", color: "from-pink-600 to-red-600", image: "photo-1495229159533-c7fcb0f1f014" },
    { title: "NGO / Nonprofit", icon: Users, route: "/ngo", description: "Donation platforms, volunteer management, and impact storytelling.", color: "from-green-700 to-green-400", image: "photo-1643321613180-68d62b93cb79" },
    { title: "Hospitality", icon: Building, route: "/hospitality", description: "Hotel booking, guest experience, and property management.", color: "from-yellow-700 to-orange-700", image: "photo-1605492072247-5788e8b81589" },
    // { title: "Beauty & Wellness", icon: Palette, route: "/beauty", description: "Booking, e-commerce, and content for salons and spas.", color: "from-pink-400 to-purple-400", image: "photo-1465101046530-73398c7f28ca" },
     //{ title: "Logistics & Delivery", icon: Plane, route: "/logistics", description: "Order tracking, fleet management, and delivery dashboards.", color: "from-blue-900 to-blue-400", image: "photo-1465101046530-73398c7f28ca" },
  ];

  // Organized portfolio projects
  const technicalProjects = [
    {
      title: "Fashion E-commerce Platform",
      description: "Complete boutique with inventory management and CRM integration",
      tags: ["React", "E-commerce", "CRM", "Inventory Management"],
      niche: "Fashion",
      image: "photo-1441986300917-64674bd600d8",
      liveUrl: "https://minna.framer.website/",
      type: "technical"
    },
    {
      title: "Real Estate CRM Dashboard",
      description: "Property management system with analytics and automation",
      tags: ["Dashboard", "Analytics", "Automation", "Property Management"],
      niche: "Real Estate",
      image: "photo-1560518883-ce09059eeffa",
      liveUrl: "https://homelly.framer.website/",
      type: "technical"
    },
    {
      title: "Music Artist Platform",
      description: "Streaming platform with fan management and analytics",
      tags: ["Streaming", "Artist Platform", "Fan Management", "Analytics"],
      niche: "Music",
      image: "photo-1493225457124-a3eb161ffa5f",
      liveUrl: "https://issyvibesproductio.wixsite.com/beats",
      type: "technical"
    },
    {
      title: "Fitness Studio Booking System",
      description: "Online booking and payments for fitness classes",
      tags: ["Booking System", "Payments", "Fitness Classes", "Online Booking"],
      niche: "Fitness",
      image: "photo-1607962837359-5e7e89f86776",
      liveUrl: "https://fitbricks.framer.website/",
      type: "technical"
    },
    {
      title: "Online Course Platform",
      description: "Online course platform with payment and enrollment management",
      tags: ["Online Courses", "Payment Processing", "Enrollment Management", "Course Management"],
      niche: "Education",
      image: "photo-1557425507-57dd2cafc241",
      liveUrl: "https://digitalise.framer.website/",
      type: "technical"
    },
    {
  
      title: "Defix SaaS Landing Page",
      description: "Decentralized finance for Defix SaaS platform",
      tags: ["Landing Page", "SaaS", "Defix", "Productivity"],
      niche: "SaaS",
      image: "photo-1573495627361-d9b87960b12d",
      liveUrl: "https://defix-saas-landing-page.vercel.app/",
      type: "technical"
    },
    {
      title: "IssyVibes Production(Partner)",
      description: "Visual storytelling powerhouse from cinematic brand films to social-ready reels. IssyVibe Production brings fashion, product, and lifestyle visions to life through world-class photography and videography.",
      tags: ["", "Booking System", "Videography", "Agency Management"],
      niche: "Visual Agency",
      image: "photo-1584224549374-995cbac1ab62?",
      liveUrl: "https://issyvibesproduction.vercel.app/",
      type: "technical"
    },
    {
      title: "Healthcare Patient Portal",
      description: "Patient management system with appointment booking and telehealth",
      tags: ["Patient Portal", "Appointment Booking", "Telehealth", "Healthcare Management"],
      niche: "Healthcare",
      image: "photo-1631217873436-b0fa88e71f0a",
      liveUrl: "https://bettermedical.framer.website/",
      type: "technical"
    }
  ];

  const creativeProjects = [
    {
      title: "Fashion Brand Campaign",
      description: "Complete visual campaign with product photography and social content",
      tags: ["Product Photography", "Brand Videos", "Social Media", "Instagram Reels"],
      niche: "Fashion",
      image: "photo-1509631179647-0177331693ae",
      videoId: "TWoKt_mW6Eo?si=2T1_Qo_qkN8Lx9oY",
      type: "creative"
    },
    {
      title: "Real Estate Virtual Tours",
      description: "Immersive virtual tours and agent promotional videos",
      tags: ["Virtual Tours", "Agent Videos", "Property Marketing", "Drone Footage"],
      niche: "Real Estate",
      image: "photo-1564013799919-ab600027ffc6",
      videoId: "o-BgxaEMn6o",
      type: "creative"
    },
    {
      title: "Fitness Studio Promotional Videos",
      description: "Promotional videos and class previews for fitness studios",
      tags: ["Promotional Videos", "Class Previews", "Fitness Marketing", "Social Media"],
      niche: "Fitness",
      image: "photo-1546483875-ad9014c88eba",
      videoId: "TwaU_NONocg",
      type: "creative"
    },
    {
      title: "Travel Agency Promotional Content",
      description: "Promotional videos and course previews for online courses",
      tags: ["Promotional Videos", "Course Previews", "Online Education", "Social Media"],
      niche: "Travel",
      image: "photo-1619120238346-978e07731e77",
      videoId: "o7poX65O5ng",
      type: "creative"
    },
    {
      title: "Music Video Production",
      description: "Professional music videos and visual content for artists",
      tags: ["Music Videos", "Visual Content", "Artist Branding", "Creative Direction"],
      niche: "Music",
      image: "photo-1470225620780-dba8ba36b745",
      videoId: "hI5HitKCkBg",
      type: "creative"
    },
    {
      title: "Coaching Course podcast",
      description: "Professional course content and promotional materials",
      tags: ["Course Videos", "Promotional Content", "Testimonial Videos", "Educational Content"],
      niche: "Coaching",
      image: "photo-1556761175-129418cb2dfe",
      videoId: "5_Gicbihz6Q",
      type: "creative"
    },
{
      title: "Issylabs Growth Media",
      description: "Visual storytelling powerhouse from cinematic brand films to social-ready reels. IssyLabs Growth Media brings travel, product, and lifestyle visions to life through world-class photography and video content designed to captivate and convert.",
      tags: ["Portfolio", "Visual Storytelling", "Branding", "Creative Direction"],
      niche: "Visual Agency",
      image: "photo-1630323826753-5de6ffa370d5",
      videoId: "hUlpDdM-b4A",
      type: "creative"
    },
    {
      title: "Tech Trading video content",
      description: "Showcasing the latest innovations and trends in technology  and trading.",
      tags: ["Tech", "Innovation", "Trade Shows", "Highlights"],
      niche: "Technology",
      image: "photo-1716279083559-ffc3a863c457",
      videoId: "BnKcmXPAQOI",
      type: "creative"
    },
    {
      title: "fashion studio promotional videos",
      description: "Promotional videos and class previews for fashion studios",
      tags: ["Promotional Videos", "Class Previews", "Fashion Marketing", "Social Media"],    
      niche: "Fashion",
      image: "photo-1445205170230-053b83016050",
      videoId: "sPhRi5bKLTo?si=ZcX1AO4pJmZQDBHu",
      type: "creative"
    },
    {
      title: "gym Promotional Content",
      description: "Promotional videos and class previews for gym studios",
      tags: ["Promotional Videos", "Class Previews", "Gym Marketing", "Social Media"],
      niche: "Gym",
      image: "photo-1607962837359-5e7e89f86776",
      videoId: "PkL17htzkHg",
      type: "creative"
    }

  ];

  const testimonials = [
    {
      name: "Sarah Chen",
      role: "Fashion Boutique Owner",
      content: "IssyLabs built my entire e-commerce platform while IssyVibe handled all my product photography. The integration was seamless and my sales doubled in 3 months.",
      rating: 5,
      niche: "Fashion",
      image: "photo-1494790108755-2616c35d6f17"
    },
    {
      name: "Marcus Rodriguez",
      role: "Real Estate Agent",
      content: "From my property dashboard to virtual tour videos, they handled everything. My clients love the professional presentation and I've closed 40% more deals.",
      rating: 5,
      niche: "Real Estate",
      image: "photo-1472099645785-5658abf4ff4e"
    },
    {
      name: "Elena Vasquez",
      role: "Business Coach",
      content: "The course platform they built is incredible, and the promotional videos they created helped me reach 10x more students. One team, complete solution.",
      rating: 5,
      niche: "Coaching",
      image: "photo-1438761681033-6461ffad8d80"
    }
  ];

  useEffect(() => {
    const handleScroll = () => {
      const sections = navItems.map(item => document.getElementById(item.id));
      const scrollPosition = window.scrollY + 100;

      for (let i = sections.length - 1; i >= 0; i--) {
        const section = sections[i];
        if (section && section.offsetTop <= scrollPosition) {
          setActiveSection(navItems[i].id);
          break;
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
    setIsMenuOpen(false);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Track form submission start
    analytics.trackFormStart('homepage_contact_form');

    const emailData: ContactFormData = {
      ...formData,
      page_source: "Homepage"
    };

    const success = await sendContactEmail(emailData);

    // Track form completion and conversion
    if (success) {
      analytics.trackFormComplete('homepage_contact_form');
      analytics.trackContactSubmission('homepage_contact', formData.service_type);
      analytics.trackConversion('contact_form_submission', 1);
    }
    
    if (success) {
      setShowSuccessMessage(true);
      setFormData({
        name: '',
        email: '',
        business_type: '',
        service_type: '',
        message: ''
      });
      setTimeout(() => setShowSuccessMessage(false), 5000);
    } else {
      alert('Failed to send message. Please try again or contact us <NAME_EMAIL>');
    }
    
    setIsSubmitting(false);
  };

  const VideoModal = ({ videoId, thumbnail, title, description }) => (
    <Dialog>
      <DialogTrigger asChild>
        <div className="relative group cursor-pointer overflow-hidden rounded-lg transition-all duration-300 hover:scale-105">
          <img 
            src={thumbnail} 
            alt={title}
            className="w-full h-48 object-cover"
          />
          <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center">
            <div className="bg-primary/90 rounded-full p-4 glow-primary">
              <Play className="w-8 h-8 text-white fill-white" />
            </div>
          </div>
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
            <h3 className="text-white font-semibold text-sm">{title}</h3>
            {description && (
              <p className="text-white/80 text-xs mt-1">{description}</p>
            )}
          </div>
        </div>
      </DialogTrigger>
      <DialogContent className="max-w-4xl w-full bg-card border-border">
        <div className="aspect-video w-full">
          <iframe
            src={`https://www.youtube.com/embed/${videoId}?autoplay=1`}
            title={title}
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
            className="w-full h-full rounded-lg"
          />
        </div>
      </DialogContent>
    </Dialog>
  );

  return (
    <div className="min-h-screen premium-gradient-bg flex flex-col">
      {/* Floating Button Group: WhatsApp & Tawk.to */}
      <FloatingButtonGroup section={activeSection} />
      {/* Fixed Navigation */}
      <nav className="fixed top-0 w-full z-50 glass-effect backdrop-blur-md shadow-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="text-2xl font-bold text-foreground flex items-center">
              <Sparkles className="h-4 w-4 text-accent mr-2" />
              IssyLabs Tech Media
            </div>
            
            {/* Desktop Navigation */}
            <div className="hidden md:flex space-x-8">
              {navItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => scrollToSection(item.id)}
                  className={`text-sm font-medium transition-all duration-300 hover:text-accent ${
                    activeSection === item.id ? 'text-accent border-b-2 border-accent' : 'text-muted-foreground'
                  }`}
                >
                  {item.label}
                </button>
              ))}
            </div>

            {/* Mobile Menu Button */}
            <button
              className="md:hidden text-foreground"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>

            {/* CTA Button */}
            <Button 
              onClick={() => scrollToSection('contact')}
              className="hidden md:flex bg-accent hover:bg-accent/90 text-accent-foreground font-semibold shadow-lg hover-lift pulse-glow"
            >
              Start Your Project
            </Button>
          </div>

          {/* Mobile Navigation */}
          {isMenuOpen && (
            <div className="md:hidden py-4 border-t border-border glass-effect">
              {navItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => scrollToSection(item.id)}
                  className="block w-full text-left py-2 text-muted-foreground hover:text-accent"
                >
                  {item.label}
                </button>
              ))}
            </div>
          )}
        </div>
      </nav>

      {/* Hero Section */}
      <section
        id="home"
        className="min-h-screen flex items-center justify-center pt-32 pb-20 px-4 sm:px-6 lg:px-8 hero-gradient text-white relative overflow-hidden"
        style={{ minHeight: '100vh' }}
      >
        <div className="absolute inset-0 bg-gradient-to-br from-black/20 to-transparent"></div>
        <div 
          className="absolute inset-0 opacity-10"
          style={{
            backgroundImage: `url(https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80)`,
            backgroundSize: 'cover',
            backgroundPosition: 'center'
          }}
        ></div>
        <div className="max-w-7xl mx-auto text-center relative z-10 w-full">
          <div className="fade-in-up flex flex-col items-center justify-center w-full">
            <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
              Where Technology
              <br />
              <span className="text-accent float-animation inline-block">
                Empowers
              </span>
              <br />
              and Storytelling
              <br />
              <span className="text-primary float-animation inline-block" style={{animationDelay: '1s'}}>
                Converts
              </span>
            </h1>
            <p className="text-xl md:text-2xl mb-12 max-w-3xl mx-auto opacity-90">
              Launch better. Scale smarter. Build something remarkable with our unified technical and creative approach.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button 
                size="lg" 
                onClick={() => scrollToSection('portfolio')}
                className="bg-accent text-accent-foreground hover:bg-accent/90 font-semibold px-8 py-4 text-lg shadow-lg hover-lift pulse-glow"
              >
                <Eye className="mr-2 h-5 w-5" />
                See Our Work
                <ChevronRight className="ml-2 h-5 w-5" />
              </Button>
              <Button 
                size="lg" 
                variant="outline" 
                onClick={() => scrollToSection('contact')}
                className="border-white/30 bg-white/10 text-white hover:bg-white hover:text-background px-8 py-4 text-lg shadow-sm hover-lift backdrop-blur-sm"
              >
                <Rocket className="mr-2 h-5 w-5" />
                Start Your Project
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-20 px-4 sm:px-6 lg:px-8 bg-card/50">
        <div className="max-w-7xl mx-auto text-center">
          <div className="fade-in-up">
            <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-8">
              One Team. Two Forces. Unified Execution.
            </h2>
            <p className="text-xl text-muted-foreground mb-16 max-w-3xl mx-auto">
              IssyLabs combines technical excellence with creative storytelling to deliver complete digital solutions that actually work.
            </p>
          </div>
          
          <div className="grid lg:grid-cols-2 gap-12">
            {/* IssyLabs */}
            <Card className="hover-lift glass-effect premium-shadow scale-in">
              <CardContent className="p-8">
                <div className="flex items-center mb-6">
                  <div className="p-3 rounded-full bg-primary/20 mr-4">
                    <Code className="h-10 w-10 text-primary" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-foreground">IssyLabs</h3>
                    <p className="text-muted-foreground">The Technical Core</p>
                  </div>
                </div>
                <p className="text-muted-foreground mb-6">
                  Your digital backbone for speed, structure, and growth.
                </p>
                <ul className="space-y-3 text-muted-foreground">
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Smart websites & landing pages
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    CRM & automation systems
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Booking flows & funnel tools
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Dashboards & data platforms
                  </li>
                </ul>
              </CardContent>
            </Card>

            {/* IssyVibe Production */}
            <Card className="hover-lift glass-effect premium-shadow scale-in" style={{animationDelay: '0.2s'}}>
              <CardContent className="p-8">
                <div className="flex items-center mb-6">
                  <div className="p-3 rounded-full bg-accent/20 mr-4">
                    <Video className="h-10 w-10 text-accent" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-foreground">IssyVibe Production</h3>
                    <p className="text-muted-foreground">The Creative Partner</p>
                  </div>
                </div>
                <p className="text-muted-foreground mb-6">
                  Creative services powered by our in-house partner  bringing your vision to life through compelling visuals and content.
                </p>
                <ul className="space-y-3 text-muted-foreground">
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-accent mr-2" />
                    Reels, music visuals, product shoots
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-accent mr-2" />
                    Branded content & campaign edits
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-accent mr-2" />
                    Voiceovers, animations & promos
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-accent mr-2" />
                    Scripts, soundtracks & storyboards
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section id="services" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 fade-in-up">
            <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
              Technical Solutions + Creative Media
            </h2>
            <p className="text-xl text-muted-foreground">
              Everything you need to launch, scale, and succeed.
            </p>
          </div>
          
          <div className="grid lg:grid-cols-2 gap-12 mb-20">
            {/* Technical Services */}
            <Card className="hover-lift glass-effect premium-shadow fade-in-left">
              <CardContent className="p-8">
                <div className="flex items-center mb-6">
                  <div className="p-3 rounded-full bg-primary/20 mr-4">
                    <Monitor className="h-10 w-10 text-primary" />
                  </div>
                  <h3 className="text-2xl font-bold text-foreground">Technical Solutions</h3>
                </div>
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <div className="flex items-center text-muted-foreground">
                      <Zap className="h-4 w-4 text-primary mr-2" />
                      Web Applications
                    </div>
                    <div className="flex items-center text-muted-foreground">
                      <Zap className="h-4 w-4 text-primary mr-2" />
                      Landing Pages
                    </div>
                    <div className="flex items-center text-muted-foreground">
                      <Zap className="h-4 w-4 text-primary mr-2" />
                      Booking Systems
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-center text-muted-foreground">
                      <Zap className="h-4 w-4 text-primary mr-2" />
                      Dashboards
                    </div>
                    <div className="flex items-center text-muted-foreground">
                      <Zap className="h-4 w-4 text-primary mr-2" />
                      CRM Systems
                    </div>
                    <div className="flex items-center text-muted-foreground">
                      <Zap className="h-4 w-4 text-primary mr-2" />
                      Automations
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Media Services */}
            <Card className="hover-lift glass-effect premium-shadow fade-in-right">
              <CardContent className="p-8">
                <div className="flex items-center mb-6">
                  <div className="p-3 rounded-full bg-accent/20 mr-4">
                    <Camera className="h-10 w-10 text-accent" />
                  </div>
                  <h3 className="text-2xl font-bold text-foreground">Media & Content</h3>
                </div>
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <div className="flex items-center text-muted-foreground">
                      <Zap className="h-4 w-4 text-accent mr-2" />
                      Reels & Short Content
                    </div>
                    <div className="flex items-center text-muted-foreground">
                      <Zap className="h-4 w-4 text-accent mr-2" />
                      Music Visuals
                    </div>
                    <div className="flex items-center text-muted-foreground">
                      <Zap className="h-4 w-4 text-accent mr-2" />
                      Fashion Shoots
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-center text-muted-foreground">
                      <Zap className="h-4 w-4 text-accent mr-2" />
                      Promo Videos
                    </div>
                    <div className="flex items-center text-muted-foreground">
                      <Zap className="h-4 w-4 text-accent mr-2" />
                      Scriptwriting
                    </div>
                    <div className="flex items-center text-muted-foreground">
                      <Zap className="h-4 w-4 text-accent mr-2" />
                      Voiceovers
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

      {/* Choose Your Niche Section (all industries) */}
      <div className="mt-20">
        <div className="text-center mb-12 fade-in-up">
          <h3 className="text-3xl font-bold text-foreground mb-4">Choose Your Niche</h3>
          <p className="text-lg text-muted-foreground">
            Start from what you do  we'll take it from there.
          </p>
        </div>
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {niches.map((niche, index) => (
            <Link
              key={niche.title}
              to={niche.route}
              onClick={() => analytics.trackServiceVisit(niche.title, 'main_services')}
            >
              <Card className="hover-lift cursor-pointer group h-full glass-effect premium-shadow scale-in" style={{animationDelay: `${index * 0.1}s`}}>
                <CardContent className="p-6">
                  <div 
                    className="h-32 rounded-lg mb-4 flex items-center justify-center relative overflow-hidden"
                    style={{
                      backgroundImage: `url(https://images.unsplash.com/${niche.image}?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80)`,
                      backgroundSize: 'cover',
                      backgroundPosition: 'center'
                    }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-br from-black/50 to-black/20"></div>
                    <niche.icon className="h-12 w-12 text-white relative z-10" />
                  </div>
                  <h4 className="text-xl font-semibold text-foreground mb-2 group-hover:text-primary transition-colors">
                    {niche.title}
                  </h4>
                  <p className="text-muted-foreground text-sm">
                    {niche.description}
                  </p>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
      </div>
        </div>
      </section>

      {/* Portfolio Section - Organized by Technical/Creative */}
      <section id="portfolio" className="py-20 px-4 sm:px-6 lg:px-8 bg-card/30">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 fade-in-up">
            <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
              Our Work Speaks For Itself
            </h2>
            <p className="text-xl text-muted-foreground">
              Technical excellence meets creative brilliance  see how we deliver complete solutions.
            </p>
          </div>

          {/* Technical Projects Section */}
          <div className="mb-20">
            <div className="flex items-center mb-12">
              <div className="p-3 rounded-full bg-primary/20 mr-4">
                <Code className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-3xl font-bold text-foreground">Technical Solutions</h3>
            </div>
            
            <div className="grid md:grid-cols-2 gap-8">
              {technicalProjects.map((project, index) => (
                <Card 
                  key={index} 
                  className="hover-lift cursor-pointer group glass-effect premium-shadow scale-in"
                  style={{animationDelay: `${index * 0.1}s`}}
                  onClick={() => window.open(project.liveUrl, '_blank')}
                >
                  <CardContent className="p-0">
                    <div 
                      className="h-64 relative overflow-hidden rounded-t-lg"
                      style={{
                        backgroundImage: `url(https://images.unsplash.com/${project.image}?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80)`,
                        backgroundSize: 'cover',
                        backgroundPosition: 'center'
                      }}
                    >
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                      <div className="absolute top-4 left-4">
                        <span className="bg-primary/90 backdrop-blur-sm text-primary-foreground px-3 py-1 rounded-full text-sm font-medium">
                          {project.niche}
                        </span>
                      </div>
                      <div className="absolute bottom-4 right-4">
                        <div className="p-2 bg-primary/20 backdrop-blur-sm rounded-full">
                          <ExternalLink className="h-5 w-5 text-white" />
                        </div>
                      </div>
                    </div>
                    <div className="p-6">
                      <h4 className="text-xl font-semibold text-foreground mb-2 group-hover:text-primary transition-colors">
                        {project.title}
                      </h4>
                      <p className="text-muted-foreground mb-4 text-sm">
                        {project.description}
                      </p>
                      
                      <div className="flex flex-wrap gap-2 mb-4">
                        {project.tags.map((tag, i) => (
                          <span 
                            key={i} 
                            className="bg-primary/10 text-primary px-2 py-1 rounded text-xs"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                      
                      <div className="flex items-center text-primary font-medium text-sm group-hover:text-accent transition-colors">
                        View Live Site
                        <ExternalLink className="ml-1 h-4 w-4" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Creative Projects Section */}
          <div>
            <div className="flex items-center mb-12">
              <div className="p-3 rounded-full bg-accent/20 mr-4">
                <Video className="h-8 w-8 text-accent" />
              </div>
              <h3 className="text-3xl font-bold text-foreground">Creative Productions</h3>
            </div>
            
            <div className="grid md:grid-cols-2 gap-8">
              {creativeProjects.map((project, index) => (
                <Card key={index} className="hover-lift cursor-pointer group glass-effect premium-shadow scale-in" style={{animationDelay: `${index * 0.1}s`}}>
                  <CardContent className="p-0">
                    <VideoModal
                      videoId={project.videoId}
                      thumbnail={`https://images.unsplash.com/${project.image}?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80`}
                      title={project.title}
                      description={project.description}
                    />
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Blog Section */}
      <section id="blog" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 fade-in-up">
            <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
              Latest Insights & Updates
            </h2>
            <p className="text-xl text-muted-foreground">
              Stay updated with the latest trends, tips, and insights from our experts across different industries.
            </p>
          </div>

          <div className="mb-8">
            <BlogList
              onReadMore={(slug) => window.open(`/marketing-service#blog-post-${slug}`, '_blank')}
              limit={6}
              showOnlyFeatured={false}
              showSearch={false}
              showFilters={false}
              variant="grid"
            />
          </div>

          <div className="text-center">
            <Button
              className="bg-accent hover:bg-accent/90 text-accent-foreground hover-lift pulse-glow"
              onClick={() => window.open('/marketing-service#blog', '_blank')}
            >
              View All Blog Posts
              <ArrowUpRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 fade-in-up">
            <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
              What Our Clients Say
            </h2>
            <p className="text-xl text-muted-foreground">
              Real results from real businesses we've helped transform.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="hover-lift glass-effect premium-shadow scale-in" style={{animationDelay: `${index * 0.1}s`}}>
                <CardContent className="p-8">
                  <div className="flex items-center mb-4">
                    <div 
                      className="w-12 h-12 rounded-full mr-4 bg-cover bg-center"
                      style={{
                        backgroundImage: `url(https://images.unsplash.com/${testimonial.image}?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80)`
                      }}
                    ></div>
                    <div className="flex">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} className="h-4 w-4 text-accent fill-current" />
                      ))}
                    </div>
                  </div>
                  <p className="text-muted-foreground mb-6 italic">
                    "{testimonial.content}"
                  </p>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-semibold text-foreground">{testimonial.name}</p>
                      <p className="text-sm text-muted-foreground">{testimonial.role}</p>
                    </div>
                    <span className="bg-primary/10 text-primary px-2 py-1 rounded text-xs">
                      {testimonial.niche}
                    </span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section - Improved Form with Email Functionality */}
      <section id="contact" className="py-20 px-4 sm:px-6 lg:px-8 bg-card/50">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16 fade-in-up">
            <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
              Ready to Transform Your Business?
            </h2>
            <p className="text-xl text-muted-foreground">
              Let's discuss how IssyLabs can help you succeed with both technical excellence and creative storytelling.
            </p>
          </div>

          {showSuccessMessage && (
            <div className="mb-8 p-4 bg-primary/10 border border-primary/20 rounded-lg text-primary text-center">
              Thank you! Your message has been sent successfully  we'll get back to you within 24 hours.
            </div>
          )}

          <div className="grid lg:grid-cols-3 gap-12">
            {/* Contact Info */}
            <div className="lg:col-span-1">
              <Card className="hover-lift glass-effect premium-shadow scale-in">
                <CardContent className="p-8">
                  <h3 className="text-2xl font-bold text-foreground mb-6">Get in Touch</h3>
                  
                  <div className="space-y-6">
                    <div className="flex items-start">
                      <div className="p-2 bg-primary/20 rounded-full mr-3 mt-1">
                        <Mail className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <p className="text-foreground font-medium">Email Us</p>
                        <p className="text-muted-foreground"><EMAIL></p>
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <div className="p-2 bg-accent/20 rounded-full mr-3 mt-1">
                        <Calendar className="h-5 w-5 text-accent" />
                      </div>
                      <div>
                        <p className="text-foreground font-medium">Book a Call</p>
                        <p className="text-muted-foreground">Free consultation available</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <div className="p-2 bg-primary/20 rounded-full mr-3 mt-1">
                        <Phone className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <p className="text-foreground font-medium">Response Time</p>
                        <p className="text-muted-foreground">Within 24 hours</p>
                      </div>
                    </div>
                  </div>

                  <Button 
                    className="w-full mt-8 bg-accent hover:bg-accent/90 text-accent-foreground hover-lift pulse-glow"
                    onClick={() => window.open('https://calendly.com/issylabs/30min', '_blank')}
                  >
                    <Calendar className="mr-2 h-4 w-4" />
                    Book a Call
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* Contact Form */}
            <div className="lg:col-span-2">
              <Card className="hover-lift glass-effect premium-shadow scale-in" style={{animationDelay: '0.2s'}}>
                <CardContent className="p-8">
                  <form onSubmit={handleFormSubmit} className="space-y-6">
                    <div className="grid md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-foreground font-medium mb-2">Name *</label>
                        <Input 
                          name="name"
                          value={formData.name}
                          onChange={handleInputChange}
                          className="bg-background border-2 border-border hover:border-primary focus:border-primary text-foreground placeholder:text-muted-foreground" 
                          placeholder="Your full name"
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-foreground font-medium mb-2">Email *</label>
                        <Input 
                          type="email" 
                          name="email"
                          value={formData.email}
                          onChange={handleInputChange}
                          className="bg-background border-2 border-border hover:border-primary focus:border-primary text-foreground placeholder:text-muted-foreground"
                          placeholder="<EMAIL>"
                          required
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-foreground font-medium mb-2">Project Type</label>
                      <select 
                        name="business_type"
                        value={formData.business_type}
                        onChange={handleInputChange}
                        className="w-full p-3 bg-background border-2 border-border hover:border-primary focus:border-primary text-foreground rounded-md"
                      >
                        <option value="">Select your project type</option>
                        <option value="Fashion">Fashion</option>
                        <option value="Real Estate">Real Estate</option>
                        <option value="Music">Music</option>
                        <option value="Coaching">Coaching</option>
                        <option value="Video Creation">Video Creation</option>
                        <option value="Automation">Automation</option>
                        <option value="Gym & Fitness">Gym & Fitness</option>
                        <option value="Partner Services">Partner Services</option>
                        <option value="Other">Other</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-foreground font-medium mb-2">Services Needed</label>
                      <select 
                        name="service_type"
                        value={formData.service_type}
                        onChange={handleInputChange}
                        className="w-full p-3 bg-background border-2 border-border hover:border-primary focus:border-primary text-foreground rounded-md"
                      >
                        <option value="">Select service needed</option>
                        <option value="IssyLabs (Technical)">IssyLabs (Technical)</option>
                        <option value="IssyVibe (Creative)">IssyVibe (Creative)</option>
                        <option value="Both Services">Both Services</option>
                        <option value="Consultation Only">Consultation Only</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-foreground font-medium mb-2">Project Details *</label>
                      <Textarea 
                        name="message"
                        value={formData.message}
                        onChange={handleInputChange}
                        className="bg-background border-2 border-border hover:border-primary focus:border-primary text-foreground placeholder:text-muted-foreground" 
                        rows={6}
                        placeholder="Tell us about your project goals, timeline, budget range, and any specific requirements..."
                        required
                      />
                    </div>

                    <Button 
                      type="submit"
                      disabled={isSubmitting}
                      className="w-full bg-primary hover:bg-primary/90 text-primary-foreground font-semibold py-4 text-lg shadow-lg hover-lift pulse-glow"
                    >
                      {isSubmitting ? 'Sending...' : 'Send Message & Start Project'}
                      <ArrowUpRight className="ml-2 h-5 w-5" />
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-background/90 py-12 px-4 sm:px-6 lg:px-8 border-t border-border backdrop-blur-sm">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-4 gap-8">
            <div className="md:col-span-2">
              <div className="text-2xl font-bold text-foreground mb-4 flex items-center">
                <Sparkles className="h-6 w-6 text-accent mr-2" />
                IssyLabs
              </div>
              <p className="text-muted-foreground mb-4">
                Your All-in-One Digital, Automation & Media Brand System
              </p>
              <p className="text-muted-foreground/80 mb-4">
                Creative services are powered by our in-house partner  IssyVibe Production.
              </p>

               <div className="space-y-4 text-3xl font-bold">
      {/* Email */}
      <a
        href="mailto:<EMAIL>"
        className="flex items-center gap-3 text-muted-foreground/80 hover:underline"
      >
        <HiOutlineMail className="text-gray-500" />
        <span><EMAIL></span>
      </a>

      {/* WhatsApp */}
      <a
        href="https://wa.me/12494336588"
        target="_blank"
        rel="noopener noreferrer"
        className="flex items-center gap-3 text-green-600 hover:underline"
      >
        <FaWhatsapp className="text-green-500" />
        <span>+1 (249) 433‑6588‬</span>
      </a>
    </div>
    

          
            </div>
            <div>
              <h4 className="text-foreground font-semibold mb-4">Quick Links</h4>
              <ul className="space-y-2">
                {navItems.map((item) => (
                  <li key={item.id}>
                    <button 
                      onClick={() => scrollToSection(item.id)}
                      className="text-muted-foreground hover:text-accent transition-colors"
                    >
                      {item.label}
                    </button>
                  </li>
                ))}
              </ul>
            </div>
            <div>
              <h4 className="text-foreground font-semibold mb-4">Industries</h4>
              <ul className="space-y-2">
                {niches.slice(0, 8).map((niche) => (
                  <li key={niche.title}>
                    <Link 
                      to={niche.route}
                      className="text-muted-foreground hover:text-accent transition-colors"
                    >
                      {niche.title}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>
          <div className="border-t border-border/50 mt-8 pt-8 text-center text-muted-foreground/80">
            <p>&copy; 2025 IssyLabs. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Index;
