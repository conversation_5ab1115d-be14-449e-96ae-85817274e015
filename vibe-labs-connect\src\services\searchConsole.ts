/**
 * Google Search Console Integration Service
 * Provides tools for SEO monitoring, keyword tracking, and site indexing
 */

// Site configuration
const SITE_URL = import.meta.env.VITE_GSC_SITE_URL || 'https://vibelabsconnect.com';

/**
 * Generate Google Search Console verification meta tag
 */
export const generateGSCVerificationTag = (verificationCode: string): void => {
  // Remove existing verification tag
  const existingTag = document.querySelector('meta[name="google-site-verification"]');
  if (existingTag) {
    existingTag.remove();
  }

  // Add new verification tag
  const meta = document.createElement('meta');
  meta.setAttribute('name', 'google-site-verification');
  meta.setAttribute('content', verificationCode);
  document.head.appendChild(meta);

  console.log('✅ GSC: Verification tag added');
};

/**
 * Generate sitemap.xml content
 */
export const generateSitemap = (pages: Array<{
  url: string;
  lastModified?: string;
  changeFreq?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority?: number;
}>): string => {
  const baseUrl = SITE_URL.replace(/\/$/, '');
  
  const urlEntries = pages.map(page => {
    const url = page.url.startsWith('http') ? page.url : `${baseUrl}${page.url}`;
    const lastMod = page.lastModified || new Date().toISOString().split('T')[0];
    const changeFreq = page.changeFreq || 'weekly';
    const priority = page.priority || 0.5;

    return `  <url>
    <loc>${url}</loc>
    <lastmod>${lastMod}</lastmod>
    <changefreq>${changeFreq}</changefreq>
    <priority>${priority}</priority>
  </url>`;
  }).join('\n');

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urlEntries}
</urlset>`;
};

/**
 * Generate robots.txt content
 */
export const generateRobotsTxt = (options: {
  allowAll?: boolean;
  disallowPaths?: string[];
  sitemapUrl?: string;
  crawlDelay?: number;
}): string => {
  const {
    allowAll = true,
    disallowPaths = [],
    sitemapUrl = `${SITE_URL}/sitemap.xml`,
    crawlDelay
  } = options;

  let robotsTxt = 'User-agent: *\n';
  
  if (allowAll) {
    robotsTxt += 'Allow: /\n';
  }
  
  disallowPaths.forEach(path => {
    robotsTxt += `Disallow: ${path}\n`;
  });
  
  if (crawlDelay) {
    robotsTxt += `Crawl-delay: ${crawlDelay}\n`;
  }
  
  robotsTxt += `\nSitemap: ${sitemapUrl}\n`;
  
  return robotsTxt;
};

/**
 * Get default site pages for sitemap generation
 */
export const getDefaultSitePages = () => {
  return [
    {
      url: '/',
      changeFreq: 'daily' as const,
      priority: 1.0
    },
    {
      url: '/fashion',
      changeFreq: 'weekly' as const,
      priority: 0.8
    },
    {
      url: '/real-estate',
      changeFreq: 'weekly' as const,
      priority: 0.8
    },
    {
      url: '/music',
      changeFreq: 'weekly' as const,
      priority: 0.8
    },
    {
      url: '/coaching',
      changeFreq: 'weekly' as const,
      priority: 0.8
    },
    {
      url: '/video',
      changeFreq: 'weekly' as const,
      priority: 0.8
    },
    {
      url: '/tech',
      changeFreq: 'weekly' as const,
      priority: 0.8
    },
    {
      url: '/marketing-service',
      changeFreq: 'daily' as const,
      priority: 0.9
    },
    {
      url: '/tech-product',
      changeFreq: 'weekly' as const,
      priority: 0.7
    },
    {
      url: '/contact',
      changeFreq: 'monthly' as const,
      priority: 0.6
    },
    {
      url: '/privacy-policy',
      changeFreq: 'yearly' as const,
      priority: 0.3
    },
    {
      url: '/terms-of-service',
      changeFreq: 'yearly' as const,
      priority: 0.3
    }
  ];
};

/**
 * Submit URL to Google for indexing (requires Search Console API)
 */
export const requestIndexing = async (url: string): Promise<boolean> => {
  // Note: This requires Google Search Console API setup and authentication
  // For now, we'll just log the request
  console.log('📊 GSC: Indexing requested for:', url);
  
  // In a real implementation, you would:
  // 1. Set up Google Search Console API credentials
  // 2. Use the Indexing API to submit URLs
  // 3. Handle authentication and rate limiting
  
  return true;
};

/**
 * Check if URL is indexed in Google
 */
export const checkIndexingStatus = (url: string): void => {
  const searchUrl = `https://www.google.com/search?q=site:${encodeURIComponent(url)}`;
  window.open(searchUrl, '_blank');
  console.log('🔍 GSC: Checking indexing status for:', url);
};

/**
 * Generate structured data for breadcrumbs
 */
export const generateBreadcrumbStructuredData = (breadcrumbs: Array<{
  name: string;
  url: string;
}>): object => {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: breadcrumbs.map((crumb, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: crumb.name,
      item: crumb.url
    }))
  };
};

/**
 * SEO audit checklist
 */
export const performSEOAudit = (): {
  score: number;
  issues: string[];
  recommendations: string[];
} => {
  const issues: string[] = [];
  const recommendations: string[] = [];
  let score = 100;

  // Check title tag
  const title = document.title;
  if (!title) {
    issues.push('Missing title tag');
    score -= 20;
  } else if (title.length < 30 || title.length > 60) {
    issues.push('Title tag length should be 30-60 characters');
    score -= 10;
  }

  // Check meta description
  const metaDesc = document.querySelector('meta[name="description"]') as HTMLMetaElement;
  if (!metaDesc || !metaDesc.content) {
    issues.push('Missing meta description');
    score -= 15;
  } else if (metaDesc.content.length < 120 || metaDesc.content.length > 160) {
    issues.push('Meta description should be 120-160 characters');
    score -= 5;
  }

  // Check heading structure
  const h1Tags = document.querySelectorAll('h1');
  if (h1Tags.length === 0) {
    issues.push('Missing H1 tag');
    score -= 15;
  } else if (h1Tags.length > 1) {
    issues.push('Multiple H1 tags found');
    score -= 10;
  }

  // Check images alt text
  const images = document.querySelectorAll('img');
  let imagesWithoutAlt = 0;
  images.forEach(img => {
    if (!img.alt) imagesWithoutAlt++;
  });
  if (imagesWithoutAlt > 0) {
    issues.push(`${imagesWithoutAlt} images missing alt text`);
    score -= Math.min(imagesWithoutAlt * 2, 10);
  }

  // Check internal links
  const internalLinks = document.querySelectorAll('a[href^="/"], a[href^="#"]');
  if (internalLinks.length < 3) {
    recommendations.push('Add more internal links to improve site navigation');
  }

  // Check page load speed (basic check)
  if (performance.timing) {
    const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
    if (loadTime > 3000) {
      issues.push('Page load time is slow (>3 seconds)');
      score -= 10;
    }
  }

  // Add recommendations
  if (score < 90) {
    recommendations.push('Optimize meta tags and content structure');
  }
  if (issues.length > 0) {
    recommendations.push('Fix identified SEO issues to improve search rankings');
  }
  recommendations.push('Regularly update content and monitor search performance');
  recommendations.push('Build quality backlinks from relevant websites');

  return {
    score: Math.max(score, 0),
    issues,
    recommendations
  };
};

/**
 * Generate canonical URL tag
 */
export const setCanonicalUrl = (url?: string): void => {
  const canonicalUrl = url || window.location.href.split('?')[0].split('#')[0];
  
  // Remove existing canonical tag
  const existingCanonical = document.querySelector('link[rel="canonical"]');
  if (existingCanonical) {
    existingCanonical.remove();
  }

  // Add new canonical tag
  const link = document.createElement('link');
  link.setAttribute('rel', 'canonical');
  link.setAttribute('href', canonicalUrl);
  document.head.appendChild(link);

  console.log('✅ GSC: Canonical URL set:', canonicalUrl);
};

// Export the search console service
export const searchConsole = {
  generateGSCVerificationTag,
  generateSitemap,
  generateRobotsTxt,
  getDefaultSitePages,
  requestIndexing,
  checkIndexingStatus,
  generateBreadcrumbStructuredData,
  performSEOAudit,
  setCanonicalUrl
};

export default searchConsole;
