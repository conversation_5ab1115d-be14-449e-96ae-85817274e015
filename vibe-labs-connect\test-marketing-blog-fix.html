<!DOCTYPE html>
<html>
<head>
    <title>Marketing Service Blog Fix Test</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            min-height: 100vh;
        }
        .container { 
            max-width: 1000px; 
            margin: 0 auto; 
            background: rgba(255,255,255,0.1); 
            padding: 30px; 
            border-radius: 20px; 
            backdrop-filter: blur(10px); 
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .test-section { 
            background: rgba(255,255,255,0.05); 
            padding: 25px; 
            margin: 25px 0; 
            border-radius: 15px; 
            border-left: 5px solid #00ff88;
        }
        .result { 
            margin: 15px 0; 
            padding: 15px; 
            border-radius: 10px; 
            border-left: 4px solid #00ff88; 
            background: rgba(255,255,255,0.1); 
        }
        .success { border-left-color: #00ff88; background: rgba(0,255,136,0.1); }
        .error { border-left-color: #ff4757; background: rgba(255,71,87,0.1); }
        .warning { border-left-color: #ffa502; background: rgba(255,165,2,0.1); }
        .info { border-left-color: #3742fa; background: rgba(55,66,250,0.1); }
        
        button { 
            margin: 8px; 
            padding: 12px 24px; 
            border: none; 
            border-radius: 8px; 
            cursor: pointer; 
            font-size: 14px; 
            font-weight: 600; 
            transition: all 0.3s ease; 
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        button:hover { 
            transform: translateY(-2px); 
            box-shadow: 0 5px 15px rgba(0,0,0,0.3); 
        }
        .btn-success { background: linear-gradient(45deg, #56ab2f, #a8e6cf); }
        
        pre { 
            background: rgba(0,0,0,0.3); 
            padding: 15px; 
            border-radius: 8px; 
            overflow-x: auto; 
            font-size: 12px;
            border: 1px solid rgba(255,255,255,0.1);
        }
        .blog-card {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            margin: 10px 0;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .blog-card:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Marketing Service Blog Fix Test</h1>
        <p>Testing the blog functionality after removing duplicate initialization</p>
        
        <div class="test-section">
            <h3>📊 Blog Storage Status</h3>
            <button onclick="checkBlogStorage()">Check Blog Storage</button>
            <button onclick="testMarketingPosts()">Test Marketing Posts</button>
            <div id="storage-results"></div>
        </div>
        
        <div class="test-section">
            <h3>🔗 Navigation Test</h3>
            <button onclick="testBlogNavigation()">Test Blog Navigation</button>
            <button onclick="openMarketingService()">Open Marketing Service</button>
            <div id="navigation-results"></div>
        </div>
        
        <div class="test-section">
            <h3>📝 Marketing Blog Posts</h3>
            <div id="blog-posts"></div>
        </div>
        
        <div class="test-section">
            <h3>🎯 Quick Actions</h3>
            <button class="btn-success" onclick="runAllTests()">Run All Tests</button>
            <button onclick="clearLocalStorage()">Clear Blog Storage</button>
            <button onclick="reinitializeBlog()">Reinitialize Blog</button>
            <div id="actions-results"></div>
        </div>
    </div>

    <script>
        function addResult(containerId, message, type = 'result') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = message;
            container.appendChild(div);
        }
        
        function checkBlogStorage() {
            addResult('storage-results', '🔄 Checking blog storage...', 'info');
            
            const stored = localStorage.getItem('blog_posts');
            if (stored) {
                try {
                    const posts = JSON.parse(stored);
                    addResult('storage-results', `✅ Found ${posts.length} total posts`, 'success');
                    
                    const marketingPosts = posts.filter(p => p.category === 'Marketing');
                    addResult('storage-results', `📊 Marketing posts: ${marketingPosts.length}`, 'info');
                    
                    const postsWithSlugs = posts.filter(p => p.slug);
                    addResult('storage-results', `🔗 Posts with slugs: ${postsWithSlugs.length}`, 'info');
                    
                    const publishedPosts = posts.filter(p => p.published !== false);
                    addResult('storage-results', `📰 Published posts: ${publishedPosts.length}`, 'info');
                    
                } catch (error) {
                    addResult('storage-results', `❌ Error parsing posts: ${error.message}`, 'error');
                }
            } else {
                addResult('storage-results', '⚠️ No blog posts found in localStorage', 'warning');
            }
        }
        
        function testMarketingPosts() {
            addResult('storage-results', '🔄 Testing marketing posts specifically...', 'info');
            
            const stored = localStorage.getItem('blog_posts');
            if (stored) {
                try {
                    const posts = JSON.parse(stored);
                    const marketingPosts = posts.filter(p => p.category === 'Marketing');
                    
                    const blogPostsContainer = document.getElementById('blog-posts');
                    blogPostsContainer.innerHTML = '';
                    
                    if (marketingPosts.length > 0) {
                        marketingPosts.forEach((post, index) => {
                            const postDiv = document.createElement('div');
                            postDiv.className = 'blog-card';
                            postDiv.onclick = () => testPostNavigation(post.slug);
                            postDiv.innerHTML = `
                                <h4>${post.title}</h4>
                                <p><strong>Slug:</strong> ${post.slug || 'No slug'}</p>
                                <p><strong>Published:</strong> ${post.published !== false ? 'Yes' : 'No'}</p>
                                <p><strong>Featured:</strong> ${post.is_featured ? 'Yes' : 'No'}</p>
                                <p><strong>Content Length:</strong> ${post.content ? post.content.length : 0} chars</p>
                            `;
                            blogPostsContainer.appendChild(postDiv);
                        });
                        
                        addResult('storage-results', `✅ Displayed ${marketingPosts.length} marketing posts`, 'success');
                    } else {
                        addResult('storage-results', '❌ No marketing posts found', 'error');
                    }
                } catch (error) {
                    addResult('storage-results', `❌ Error processing marketing posts: ${error.message}`, 'error');
                }
            }
        }
        
        function testBlogNavigation() {
            addResult('navigation-results', '🔄 Testing blog navigation...', 'info');
            
            const testSlugs = [
                'welcome-to-our-marketing-blog',
                'digital-marketing-trends-for-2024',
                'content-marketing-best-practices'
            ];
            
            const stored = localStorage.getItem('blog_posts');
            if (stored) {
                const posts = JSON.parse(stored);
                
                testSlugs.forEach(slug => {
                    const foundPost = posts.find(p => p.slug === slug);
                    if (foundPost) {
                        addResult('navigation-results', `✅ Found post: ${slug}`, 'success');
                    } else {
                        addResult('navigation-results', `❌ Missing post: ${slug}`, 'error');
                    }
                });
                
                // Test marketing service navigation
                const marketingServiceUrl = 'http://localhost:8082/#marketing-service';
                addResult('navigation-results', `🔗 Marketing Service URL: ${marketingServiceUrl}`, 'info');
                
                const blogUrl = 'http://localhost:8082/#blog/welcome-to-our-marketing-blog';
                addResult('navigation-results', `📝 Sample Blog URL: ${blogUrl}`, 'info');
            }
        }
        
        function testPostNavigation(slug) {
            addResult('navigation-results', `🖱️ Testing navigation to: ${slug}`, 'info');
            
            const marketingServiceUrl = `http://localhost:8082/#blog/${slug}`;
            addResult('navigation-results', `🔗 Would navigate to: ${marketingServiceUrl}`, 'info');
            
            // Open in new tab for testing
            window.open(marketingServiceUrl, '_blank');
        }
        
        function openMarketingService() {
            addResult('navigation-results', '🔄 Opening Marketing Service...', 'info');
            window.open('http://localhost:8082/#marketing-service', '_blank');
        }
        
        function clearLocalStorage() {
            addResult('actions-results', '🗑️ Clearing blog storage...', 'info');
            localStorage.removeItem('blog_posts');
            addResult('actions-results', '✅ Blog storage cleared', 'success');
        }
        
        function reinitializeBlog() {
            addResult('actions-results', '🔄 Reinitializing blog...', 'info');
            
            // Clear existing
            localStorage.removeItem('blog_posts');
            
            // Trigger reinitialization by dispatching event
            window.dispatchEvent(new Event('blog_posts_updated'));
            
            addResult('actions-results', '✅ Blog reinitialization triggered', 'success');
            addResult('actions-results', 'Refresh the marketing service page to see new posts', 'info');
        }
        
        function runAllTests() {
            addResult('actions-results', '🚀 Running all tests...', 'info');
            
            setTimeout(() => {
                checkBlogStorage();
                testMarketingPosts();
            }, 500);
            
            setTimeout(() => {
                testBlogNavigation();
            }, 1000);
            
            setTimeout(() => {
                addResult('actions-results', '✅ All tests completed!', 'success');
            }, 1500);
        }
        
        // Auto-run basic tests on load
        window.onload = function() {
            setTimeout(() => {
                checkBlogStorage();
                testMarketingPosts();
            }, 500);
        };
    </script>
</body>
</html>
