import React, { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  Calendar, 
  Clock, 
  Eye, 
  Share2, 
  Twitter, 
  Facebook, 
  Linkedin,
  Copy,
  ArrowLeft,
  Tag
} from 'lucide-react';
import { toast } from 'sonner';

interface BlogPost {
  id: string;
  title: string;
  slug: string;
  content: string;
  excerpt: string;
  featured_image: string;
  author_name: string;
  author_avatar: string;
  category: string;
  tags: string[];
  reading_time: number;
  view_count: number;
  published_at: string;
  seo_title: string;
  seo_description: string;
}

interface BlogPostProps {
  slug: string;
  onBack: () => void;
}

export const BlogPost: React.FC<BlogPostProps> = ({ slug, onBack }) => {
  const [post, setPost] = useState<BlogPost | null>(null);
  const [loading, setLoading] = useState(true);
  const [relatedPosts, setRelatedPosts] = useState<BlogPost[]>([]);
  const [comments, setComments] = useState<any[]>([]);
  const [commentAuthor, setCommentAuthor] = useState('');
  const [commentText, setCommentText] = useState('');

  useEffect(() => {
    fetchPost();
  }, [slug]);

  const fetchPost = async () => {
    setLoading(true);
    try {
      const storedPosts = localStorage.getItem('blog_posts');
      const posts = storedPosts ? JSON.parse(storedPosts) : [];

      const foundPost = posts.find((p: any) =>
        p.slug === slug && p.published !== false
      );

      if (foundPost) {
        setPost(foundPost);
        incrementViewCount(foundPost.id);
        fetchRelatedPosts(foundPost.category, foundPost.id);
  fetchComments(foundPost.id);
      } else {
        toast.error('Blog post not found');
      }
    } catch (error) {
      console.error('Error fetching post:', error);
      toast.error('Failed to load blog post');
    } finally {
      setLoading(false);
    }
  };

  const fetchComments = (postId: string) => {
    try {
      const stored = JSON.parse(localStorage.getItem('blog_comments') || '{}');
      setComments(stored[postId] || []);
    } catch (e) {
      setComments([]);
    }
  };

  const addComment = () => {
    if (!commentAuthor || !commentText) return toast.error('Name and comment required');
    const newComment = {
      id: Date.now().toString(),
      author: commentAuthor,
      text: commentText,
      created_at: new Date().toISOString()
    };
    try {
      const stored = JSON.parse(localStorage.getItem('blog_comments') || '{}');
      stored[post!.id] = stored[post!.id] || [];
      stored[post!.id].push(newComment);
      localStorage.setItem('blog_comments', JSON.stringify(stored));
      setComments(stored[post!.id]);
      setCommentAuthor(''); setCommentText('');
      toast.success('Comment added');
      // notify admin lists
      window.dispatchEvent(new Event('blog_posts_updated'));
    } catch (e) {
      console.error(e);
      toast.error('Failed to add comment');
    }
  };

  const incrementViewCount = async (postId: string) => {
    try {
      const storedPosts = localStorage.getItem('blog_posts');
      const posts = storedPosts ? JSON.parse(storedPosts) : [];

      const updatedPosts = posts.map((p: any) =>
        p.id === postId
          ? { ...p, view_count: (p.view_count || 0) + 1 }
          : p
      );

      localStorage.setItem('blog_posts', JSON.stringify(updatedPosts));
      // notify lists to refresh
      window.dispatchEvent(new Event('blog_posts_updated'));
    } catch (error) {
      console.error('Error incrementing view count:', error);
    }
  };

  const fetchRelatedPosts = async (category: string, currentPostId: string) => {
    try {
      const storedPosts = localStorage.getItem('blog_posts');
      const posts = storedPosts ? JSON.parse(storedPosts) : [];

      const relatedPosts = posts
        .filter((p: any) =>
          p.category === category &&
          p.published !== false &&
          p.id !== currentPostId
        )
        .slice(0, 3);

      setRelatedPosts(relatedPosts);
    } catch (error) {
      console.error('Error fetching related posts:', error);
    }
  };

  const sharePost = async (platform: 'twitter' | 'facebook' | 'linkedin' | 'copy') => {
    const url = window.location.href;
    const title = post?.title || '';
    
    const shareUrls = {
      twitter: `https://twitter.com/intent/tweet?text=${encodeURIComponent(title)}&url=${encodeURIComponent(url)}`,
      facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`,
      linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`,
      copy: url
    };

    if (platform === 'copy') {
      try {
        await navigator.clipboard.writeText(url);
        toast.success('Link copied to clipboard!');
      } catch (error) {
        toast.error('Failed to copy link');
      }
    } else {
      window.open(shareUrls[platform], '_blank', 'width=600,height=400');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="animate-pulse space-y-6">
          <div className="h-4 bg-white/10 rounded w-32"></div>
          <div className="h-8 bg-white/10 rounded w-3/4"></div>
          <div className="h-64 bg-white/10 rounded"></div>
          <div className="space-y-4">
            {Array.from({ length: 6 }).map((_, i) => (
              <div key={i} className="h-4 bg-white/10 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!post) {
    return (
      <div className="max-w-4xl mx-auto px-4 py-8 text-center">
        <h1 className="text-2xl font-bold mb-4 text-white">Post Not Found</h1>
        <p className="text-muted-foreground mb-6">The blog post you're looking for doesn't exist.</p>
        <Button onClick={onBack} variant="outline" className="border-accent text-accent hover:bg-accent hover:text-accent-foreground">
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Blog
        </Button>
      </div>
    );
  }

  return (
    <article className="max-w-4xl mx-auto px-4 py-8">
      {/* Header */}
      <header className="mb-8">
        <Button
          variant="ghost"
          onClick={onBack}
          className="mb-6 -ml-4 text-accent hover:bg-accent/20 hover-lift"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Blog
        </Button>

        <div className="space-y-4">
          <Badge variant="default" className="mb-2">
            {post.category}
          </Badge>
          
          <h1 className="text-4xl font-bold leading-tight">
            {post.title}
          </h1>
          
          {post.excerpt && (
            <p className="text-xl text-muted-foreground leading-relaxed">
              {post.excerpt}
            </p>
          )}

          <div className="flex items-center justify-between flex-wrap gap-4">
            <div className="flex items-center gap-4">
              <Avatar>
                <AvatarImage src={post.author_avatar} alt={post.author_name} />
                <AvatarFallback>
                  {post.author_name.split(' ').map(n => n[0]).join('')}
                </AvatarFallback>
              </Avatar>
              
              <div>
                <p className="font-medium">{post.author_name}</p>
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <span className="flex items-center gap-1">
                    <Calendar className="w-4 h-4" />
                    {formatDate(post.published_at)}
                  </span>
                  <span className="flex items-center gap-1">
                    <Clock className="w-4 h-4" />
                    {post.reading_time}m read
                  </span>
                  <span className="flex items-center gap-1">
                    <Eye className="w-4 h-4" />
                    {post.view_count} views
                  </span>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => sharePost('twitter')}
              >
                <Twitter className="w-4 h-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => sharePost('facebook')}
              >
                <Facebook className="w-4 h-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => sharePost('linkedin')}
              >
                <Linkedin className="w-4 h-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => sharePost('copy')}
              >
                <Copy className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Featured Image */}
      {post.featured_image && (
        <div className="mb-8">
          <img
            src={post.featured_image}
            alt={post.title}
            className="w-full h-64 md:h-80 object-cover rounded-lg"
          />
        </div>
      )}

      {/* Content */}
      <div className="prose prose-lg max-w-none mb-8">
        <div 
          dangerouslySetInnerHTML={{ 
            __html: post.content.replace(/\n/g, '<br>') 
          }} 
        />
      </div>

      {/* Tags */}
      {post.tags.length > 0 && (
        <div className="mb-8">
          <Separator className="mb-4" />
          <div className="flex items-center gap-2 flex-wrap">
            <Tag className="w-4 h-4 text-muted-foreground" />
            {post.tags.map((tag) => (
              <Badge key={tag} variant="outline">
                {tag}
              </Badge>
            ))}
          </div>
        </div>
      )}

      {/* Share Section */}
      <Card className="mb-8">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-semibold mb-1">Enjoyed this post?</h3>
              <p className="text-sm text-muted-foreground">Share it with your network</p>
            </div>
            <div className="flex items-center gap-2">
              <Button onClick={() => sharePost('twitter')}>
                <Twitter className="w-4 h-4 mr-2" />
                Tweet
              </Button>
              <Button variant="outline" onClick={() => sharePost('linkedin')}>
                <Linkedin className="w-4 h-4 mr-2" />
                Share
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Related Posts */}
      {relatedPosts.length > 0 && (
        <section>
          <Separator className="mb-6" />
          <h2 className="text-2xl font-bold mb-6">Related Posts</h2>
          <div className="grid gap-6 md:grid-cols-3">
            {relatedPosts.map((relatedPost) => (
              <Card 
                key={relatedPost.id}
                className="cursor-pointer hover:shadow-lg transition-shadow"
                onClick={() => window.location.hash = `blog/${relatedPost.slug}`}
              >
                {relatedPost.featured_image && (
                  <img
                    src={relatedPost.featured_image}
                    alt={relatedPost.title}
                    className="w-full h-32 object-cover rounded-t-lg"
                  />
                )}
                <CardContent className="p-4">
                  <h3 className="font-semibold mb-2 line-clamp-2">
                    {relatedPost.title}
                  </h3>
                  <p className="text-sm text-muted-foreground line-clamp-2 mb-2">
                    {relatedPost.excerpt}
                  </p>
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <Calendar className="w-3 h-3" />
                    {formatDate(relatedPost.published_at)}
                    <Clock className="w-3 h-3 ml-2" />
                    {relatedPost.reading_time}m
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>
      )}

      {/* Comments */}
      <section className="mt-8">
        <Separator className="mb-4" />
        <h2 className="text-2xl font-bold mb-4">Comments</h2>
        <div className="space-y-4 mb-6">
          {comments.length === 0 ? (
            <p className="text-muted-foreground">Be the first to comment.</p>
          ) : (
            comments.map(c => (
              <Card key={c.id} className="p-4">
                <div className="flex items-start justify-between">
                  <div>
                    <p className="font-medium">{c.author}</p>
                    <p className="text-sm text-muted-foreground">{c.text}</p>
                    <p className="text-xs text-muted-foreground mt-2">{new Date(c.created_at).toLocaleString()}</p>
                  </div>
                </div>
              </Card>
            ))
          )}
        </div>

        <div className="grid gap-2">
          <Input placeholder="Your name" value={commentAuthor} onChange={e => setCommentAuthor(e.target.value)} />
          <Textarea placeholder="Your comment" value={commentText} onChange={e => setCommentText(e.target.value)} rows={4} />
          <div className="flex gap-2">
            <Button onClick={addComment}>Add Comment</Button>
          </div>
        </div>
      </section>
    </article>
  );
};

export default BlogPost;
