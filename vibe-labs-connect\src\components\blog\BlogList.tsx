import React, { useState, useEffect } from 'react';
import { BlogCard } from './BlogCard';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Search, Filter, Calendar, TrendingUp } from 'lucide-react';
import { blogStorage, type BlogPost as StorageBlogPost } from '@/utils/blogStorage';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  featured_image: string;
  author_name: string;
  category: string;
  tags: string[];
  reading_time: number;
  view_count: number;
  published_at: string;
  is_featured: boolean;
}

interface BlogListProps {
  onReadMore: (slug: string) => void;
  category?: string;
  limit?: number;
  showSearch?: boolean;
  showFilters?: boolean;
  variant?: 'grid' | 'list' | 'mixed';
  showOnlyFeatured?: boolean;
}

export const BlogList: React.FC<BlogListProps> = ({
  onReadMore,
  category,
  limit,
  showSearch = true,
  showFilters = true,
  variant = 'mixed',
  showOnlyFeatured = false
}) => {
  const [posts, setPosts] = useState<StorageBlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState(category || 'all');
  const [sortBy, setSortBy] = useState<'latest' | 'popular' | 'trending'>('latest');
  const [categories, setCategories] = useState<string[]>([]);

  useEffect(() => {
    fetchPosts();
    fetchCategories();
  }, [selectedCategory, sortBy, category, showOnlyFeatured]);

  const fetchPosts = async () => {
    setLoading(true);
    try {
      let posts = await blogStorage.getPosts();

      // Filter published posts
      posts = posts.filter((post: any) => post.published !== false);

      // Apply category filter
      if (selectedCategory !== 'all') {
        posts = posts.filter((post: any) => post.category === selectedCategory);
      }

      // Filter only featured posts if showOnlyFeatured is true
      if (showOnlyFeatured) {
        posts = posts.filter((post: any) => post.is_featured === true);
      }

      // Apply sorting
      switch (sortBy) {
        case 'popular':
          posts.sort((a: any, b: any) => (b.view_count || 0) - (a.view_count || 0));
          break;
        case 'trending':
          const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
          posts = posts
            .filter((post: any) => new Date(post.published_at || post.created_at) >= sevenDaysAgo)
            .sort((a: any, b: any) => (b.view_count || 0) - (a.view_count || 0));
          break;
        default:
          posts.sort((a: any, b: any) =>
            new Date(b.published_at || b.created_at).getTime() - new Date(a.published_at || a.created_at).getTime()
          );
      }

      if (limit) {
        posts = posts.slice(0, limit);
      }

      setPosts(posts);
    } catch (error) {
      console.error('Error fetching posts:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const posts = await blogStorage.getPosts();
      console.log('Fetched posts for categories:', posts);

      const publishedPosts = posts.filter((post: any) => post.published !== false);
      const uniqueCategories = Array.from(new Set(
        publishedPosts
          .map((post: any) => post.category)
          .filter((category: any) => category && category.trim() !== '')
      ));

      console.log('Unique categories found:', uniqueCategories);
      setCategories(uniqueCategories as string[]);
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const filteredPosts = posts.filter(post => {
    const searchLower = searchTerm.toLowerCase();
    const titleMatch = post.title.toLowerCase().includes(searchLower);
    const excerptMatch = post.excerpt?.toLowerCase().includes(searchLower);
    const contentMatch = post.content?.toLowerCase().includes(searchLower);

    // Handle tags - stored as string
    let tagsMatch = false;
    if (post.tags && typeof post.tags === 'string') {
      tagsMatch = post.tags.toLowerCase().includes(searchLower);
    }

    return titleMatch || excerptMatch || contentMatch || tagsMatch;
  });

  const featuredPosts = filteredPosts.filter(post => post.is_featured);
  const regularPosts = filteredPosts.filter(post => !post.is_featured);

  if (loading) {
    return (
      <div className="space-y-6">
        {showFilters && (
          <div className="flex flex-col sm:flex-row gap-4">
            <Skeleton className="h-10 flex-1" />
            <Skeleton className="h-10 w-32" />
            <Skeleton className="h-10 w-32" />
          </div>
        )}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {Array.from({ length: 6 }).map((_, i) => (
            <div key={i} className="space-y-4">
              <Skeleton className="h-48 w-full rounded-lg" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      {(showSearch || showFilters) && (
        <div className="flex flex-col sm:flex-row gap-4 mb-8">
          {showSearch && (
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search posts, tags, or topics..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-accent"
              />
            </div>
          )}

          {showFilters && (
            <>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-40 bg-white/5 border-white/20 text-white focus:border-accent">
                  <Filter className="w-4 h-4 mr-2" />
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent className="bg-background/95 backdrop-blur-sm border-white/20">
                  <SelectItem value="all">All Categories</SelectItem>
                  {categories.map((cat) => (
                    <SelectItem key={cat} value={cat}>
                      {cat}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={sortBy} onValueChange={(value: 'latest' | 'popular' | 'trending') => setSortBy(value)}>
                <SelectTrigger className="w-32 bg-white/5 border-white/20 text-white focus:border-accent">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-background/95 backdrop-blur-sm border-white/20">
                  <SelectItem value="latest">
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4" />
                      Latest
                    </div>
                  </SelectItem>
                  <SelectItem value="popular">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="w-4 h-4" />
                      Popular
                    </div>
                  </SelectItem>
                  <SelectItem value="trending">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="w-4 h-4" />
                      Trending
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </>
          )}
        </div>
      )}

      {filteredPosts.length === 0 ? (
        <div className="text-center py-12">
          <h3 className="text-lg font-semibold mb-2">No posts found</h3>
          <p className="text-muted-foreground mb-4">
            {searchTerm ? 'Try adjusting your search terms' : 'No blog posts available yet'}
          </p>
          {!searchTerm && (
            <Button
              onClick={() => {
                fetchPosts();
                fetchCategories();
              }}
              variant="outline"
              className="hover:bg-accent hover:text-accent-foreground"
            >
              Refresh Posts
            </Button>
          )}
        </div>
      ) : (
        <>
          {/* Featured Posts */}
          {featuredPosts.length > 0 && (
            <section className="mb-12">
              <div className="flex items-center gap-2 mb-6 fade-in-up">
                <Badge className="bg-accent text-accent-foreground pulse-glow">Featured</Badge>
                <h2 className="text-2xl font-bold text-white">Featured Posts</h2>
              </div>
              <div className="grid gap-6 md:grid-cols-2">
                {featuredPosts.slice(0, 2).map((post, index) => (
                  <div key={post.id} className="fade-in-up" style={{animationDelay: `${index * 0.1}s`}}>
                    <BlogCard
                      post={post}
                      onReadMore={onReadMore}
                      variant="featured"
                    />
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Regular Posts */}
          {regularPosts.length > 0 && (
            <section>
              {featuredPosts.length > 0 && (
                <h2 className="text-2xl font-bold mb-6 text-white fade-in-up">Latest Posts</h2>
              )}

              {variant === 'grid' ? (
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                  {regularPosts.map((post, index) => (
                    <div key={post.id} className="fade-in-up" style={{animationDelay: `${index * 0.1}s`}}>
                      <BlogCard
                        post={post}
                        onReadMore={onReadMore}
                      />
                    </div>
                  ))}
                </div>
              ) : variant === 'list' ? (
                <div className="space-y-4">
                  {regularPosts.map((post, index) => (
                    <div key={post.id} className="fade-in-up" style={{animationDelay: `${index * 0.1}s`}}>
                      <BlogCard
                        post={post}
                        onReadMore={onReadMore}
                        variant="compact"
                      />
                    </div>
                  ))}
                </div>
              ) : (
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                  {regularPosts.map((post, index) => (
                    <div key={post.id} className="fade-in-up" style={{animationDelay: `${index * 0.1}s`}}>
                      <BlogCard
                        post={post}
                        onReadMore={onReadMore}
                        variant={index < 3 ? 'default' : 'compact'}
                      />
                    </div>
                  ))}
                </div>
              )}
            </section>
          )}
        </>
      )}
    </div>
  );
};