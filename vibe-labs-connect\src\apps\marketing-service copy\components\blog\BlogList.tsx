import React, { useState, useEffect } from 'react';

declare global {
  interface Window {
    searchTimeout: number;
  }
}
import { BlogCard } from './BlogCard';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Search, Filter, Calendar, TrendingUp } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  featured_image: string;
  author_name: string;
  category: string;
  tags: string[];
  reading_time: number;
  view_count: number;
  published_at: string;
  created_at?: string;
  published?: boolean;
  is_featured?: boolean;
}

interface BlogListProps {
  onReadMore?: (slug: string) => void;
  category?: string;
  limit?: number;
  showSearch?: boolean;
  showFilters?: boolean;
  variant?: 'grid' | 'list' | 'mixed';
  // when true, only display featured posts (useful for voice landing previews)
  showOnlyFeatured?: boolean;
}

export const BlogList: React.FC<BlogListProps> = ({
  onReadMore,
  category,
  limit: initialLimit = 24,
  showSearch = true,
  showFilters = true,
  variant = 'mixed'
  , showOnlyFeatured = false
}) => {
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState(category || 'all');
  const [sortBy, setSortBy] = useState<'trending' | 'popular' | 'latest' | 'all'>('trending');
  const [categories, setCategories] = useState<string[]>([]);
  const [limit, setLimit] = useState(initialLimit);

  useEffect(() => {
    fetchPosts();
    fetchCategories();
    // listen for external updates (admin creates/updates posts)
    const handler = () => fetchPosts();
    window.addEventListener('blog_posts_updated', handler as EventListener);
    return () => window.removeEventListener('blog_posts_updated', handler as EventListener);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedCategory, sortBy, category]);

  const fetchPosts = async () => {
    setLoading(true);
    try {
      const storedPosts = localStorage.getItem('blog_posts');
      let posts = storedPosts ? JSON.parse(storedPosts) : [];

      // Dedupe posts by slug (keep first occurrence)
      const seen = new Set<string>();
      posts = posts.filter((p: any) => {
        if (!p || !p.slug) return true;
        if (seen.has(p.slug)) return false;
        seen.add(p.slug);
        return true;
      });

      // Filter published posts
      posts = posts.filter((post: any) => post.published !== false);

      // Apply category filter
      if (selectedCategory !== 'all') {
        posts = posts.filter((post: any) => post.category === selectedCategory);
      }

      // Apply sorting
  switch (sortBy) {
  case 'popular':
          posts.sort((a: any, b: any) => (b.view_count || 0) - (a.view_count || 0));
          break;
  case 'trending':
          const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
          posts = posts
            .filter((post: any) => new Date(post.published_at || post.created_at) >= sevenDaysAgo)
            .sort((a: any, b: any) => (b.view_count || 0) - (a.view_count || 0));
          break;
  case 'all':
          // no additional sorting beyond default (latest). keep latest order
          posts.sort((a: any, b: any) => new Date(b.published_at || b.created_at).getTime() - new Date(a.published_at || a.created_at).getTime());
          break;
        default:
          posts.sort((a: any, b: any) =>
            new Date(b.published_at || b.created_at).getTime() - new Date(a.published_at || a.created_at).getTime()
          );
      }

      if (limit) {
        posts = posts.slice(0, limit);
      }

      setPosts(posts);
    } catch (error) {
      console.error('Error fetching posts:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const storedPosts = localStorage.getItem('blog_posts');
      const posts = storedPosts ? JSON.parse(storedPosts) : [];

      const publishedPosts = posts.filter((post: any) => post.published !== false);
      const uniqueCategories = Array.from(new Set(
        publishedPosts
          .map((post: any) => post.category)
          .filter((category: any) => category && category.trim() !== '')
      ));

      setCategories(uniqueCategories as string[]);
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  // Memoize filtered and sorted posts
  const { filteredPosts, featuredPosts, trendingPosts, popularPosts, latestPosts } = React.useMemo(() => {
    // First apply category filter and search term
    let filtered = posts.filter(post => post.published !== false);
    
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(post => post.category === selectedCategory);
    }
    
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(post =>
        post.title.toLowerCase().includes(term) ||
        post.excerpt?.toLowerCase().includes(term) ||
        post.tags.some(tag => tag.toLowerCase().includes(term))
      );
    }

    // Get featured posts
    const featured = filtered.filter(post => post.is_featured);
    
    // Create derived sections based on sort criteria
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const trending = [...filtered]
      .filter(p => new Date(p.published_at || p.created_at) >= sevenDaysAgo)
      .sort((a, b) => (b.view_count || 0) - (a.view_count || 0))
      .slice(0, 6);
    
    const popular = [...filtered]
      .sort((a, b) => (b.view_count || 0) - (a.view_count || 0))
      .slice(0, 6);
    
    const latest = [...filtered]
      .sort((a, b) => 
        new Date(b.published_at || b.created_at).getTime() - 
        new Date(a.published_at || a.created_at).getTime()
      );

    return {
      filteredPosts: filtered,
      featuredPosts: featured,
      trendingPosts: trending,
      popularPosts: popular,
      latestPosts: latest
    };
  }, [posts, selectedCategory, searchTerm]);

  if (loading) {
    return (
      <div className="space-y-6">
        {showFilters && (
          <div className="flex flex-col sm:flex-row gap-4">
            <Skeleton className="h-10 flex-1" />
            <Skeleton className="h-10 w-32" />
            <Skeleton className="h-10 w-32" />
          </div>
        )}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {Array.from({ length: 6 }).map((_, i) => (
            <div key={i} className="space-y-4">
              <Skeleton className="h-48 w-full rounded-lg" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
  {(showSearch || showFilters) && (
        <div className="flex flex-col sm:flex-row gap-4 mb-8">
          {showSearch && (
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search posts, tags, or topics..."
                value={searchTerm}
                onChange={(e) => {
                  const value = e.target.value;
                  // Clear any existing timeouts
                  if (window.searchTimeout) {
                    clearTimeout(window.searchTimeout);
                  }
                  // Set a new timeout to update search after 300ms of no typing
                  window.searchTimeout = setTimeout(() => {
                    setSearchTerm(value);
                  }, 300) as unknown as number;
                }}
                className="pl-10 bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-accent"
              />
            </div>
          )}

          {showFilters && (
            <>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-40 bg-white/5 border-white/20 text-white focus:border-accent">
                  <Filter className="w-4 h-4 mr-2" />
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent className="bg-background/95 backdrop-blur-sm border-white/20">
                  <SelectItem value="all">All Categories</SelectItem>
                  {categories.map((cat) => (
                    <SelectItem key={cat} value={cat}>
                      {cat}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={sortBy} onValueChange={(value: 'latest' | 'popular' | 'trending' | 'all') => setSortBy(value as any)}>
                <SelectTrigger className="w-32 bg-white/5 border-white/20 text-white focus:border-accent">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-background/95 backdrop-blur-sm border-white/20">
                  <SelectItem value="latest">
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4" />
                      Latest
                    </div>
                  </SelectItem>
                  <SelectItem value="popular">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="w-4 h-4" />
                      Popular
                    </div>
                  </SelectItem>
                  <SelectItem value="trending">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="w-4 h-4" />
                      Trending
                    </div>
                  </SelectItem>
                  <SelectItem value="all">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="w-4 h-4" />
                      All
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </>
          )}
        </div>
      )}

    {filteredPosts.length === 0 ? (
        <div className="text-center py-12">
          <h3 className="text-lg font-semibold mb-2">No posts found</h3>
          <p className="text-muted-foreground">
            {searchTerm ? 'Try adjusting your search terms' : 'No blog posts available yet'}
          </p>
        </div>
      ) : (
        <>
      {/* Featured Posts */}
          {featuredPosts.length > 0 && (
            <section className="mb-12">
              <div className="flex items-center gap-2 mb-6 fade-in-up">
                <Badge className="bg-accent text-accent-foreground pulse-glow">Featured</Badge>
                <h2 className="text-2xl font-bold text-white">Featured Posts</h2>
              </div>
              <div className="grid gap-6 md:grid-cols-2">
                {featuredPosts.slice(0, 2).map((post, index) => (
                  <div key={post.id} className="fade-in-up w-full" style={{animationDelay: `${index * 0.1}s`}}>
                    <BlogCard
                      post={post}
                      onReadMore={onReadMore}
                      variant="featured"
                    />
                  </div>
                ))}
              </div>
            </section>
          )}

          {showOnlyFeatured ? (
            <section className="mb-12">
              <h2 className="text-2xl font-bold mb-4 text-white">Featured</h2>
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {featuredPosts.slice(0, limit || 6).map((post: any, i: number) => (
                  <div key={post.id} className="fade-in-up w-full" style={{animationDelay: `${i * 0.05}s`}}>
                    <BlogCard post={post} onReadMore={onReadMore} />
                  </div>
                ))}
              </div>
            </section>
          ) : (
            <>
              <section className="mb-12">
                <h2 className="text-2xl font-bold mb-4 text-white">Trending</h2>
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                  {trendingPosts.slice(0, 6).map((post: any, i: number) => (
                    <div key={post.id} className="fade-in-up w-full will-change-transform" style={{
                      animationDelay: `${Math.min(i * 0.05, 0.3)}s`,
                      transform: 'translateZ(0)'  // Force GPU acceleration
                    }}>
                      <BlogCard post={post} onReadMore={onReadMore} />
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-12">
                <h2 className="text-2xl font-bold mb-4 text-white">Popular</h2>
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                  {popularPosts.slice(0, 6).map((post: any, i: number) => (
                    <div key={post.id} className="fade-in-up w-full" style={{animationDelay: `${i * 0.05}s`}}>
                      <BlogCard post={post} onReadMore={onReadMore} />
                    </div>
                  ))}
                </div>
              </section>

              <section className="mb-12">
                <h2 className="text-2xl font-bold mb-4 text-white">Latest</h2>
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                  {latestPosts.slice(0, 6).map((post: any, i: number) => (
                    <div key={post.id} className="fade-in-up w-full" style={{animationDelay: `${i * 0.05}s`}}>
                      <BlogCard post={post} onReadMore={onReadMore} />
                    </div>
                  ))}
                </div>
              </section>

              <section>
                <h2 className="text-2xl font-bold mb-4 text-white">All Posts</h2>
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                  {latestPosts.slice(0, Math.min(limit || 24, latestPosts.length)).map((post: any, i: number) => (
                    <div key={post.id} className="fade-in-up w-full" style={{animationDelay: `${Math.min(i * 0.02, 0.5)}s`}}>
                      <BlogCard post={post} onReadMore={onReadMore} />
                    </div>
                  ))}
                </div>
                {latestPosts.length > (limit || 24) && (
                  <div className="mt-8 text-center">
                    <Button
                      variant="outline"
                      onClick={() => limit && setLimit(prev => prev + 12)}
                      className="hover:bg-accent hover:text-accent-foreground"
                    >
                      Load More Posts
                    </Button>
                  </div>
                )}
              </section>
            </>
          )}
        </>
      )}
    </div>
  );
};
