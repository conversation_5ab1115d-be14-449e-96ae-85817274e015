<!DOCTYPE html>
<html>
<head>
    <title>GA4 Integration Test - G-B63EQLSE44</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            min-height: 100vh;
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: rgba(255,255,255,0.1); 
            padding: 30px; 
            border-radius: 20px; 
            backdrop-filter: blur(10px); 
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #00ff88, #00d4ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .test-section { 
            background: rgba(255,255,255,0.05); 
            padding: 25px; 
            margin: 25px 0; 
            border-radius: 15px; 
            border-left: 5px solid #00ff88;
        }
        .result { 
            margin: 15px 0; 
            padding: 15px; 
            border-radius: 10px; 
            border-left: 4px solid #00ff88; 
            background: rgba(255,255,255,0.1); 
        }
        .success { border-left-color: #00ff88; background: rgba(0,255,136,0.1); }
        .error { border-left-color: #ff4757; background: rgba(255,71,87,0.1); }
        .warning { border-left-color: #ffa502; background: rgba(255,165,2,0.1); }
        .info { border-left-color: #3742fa; background: rgba(55,66,250,0.1); }
        
        button { 
            margin: 8px; 
            padding: 12px 24px; 
            border: none; 
            border-radius: 8px; 
            cursor: pointer; 
            font-size: 14px; 
            font-weight: 600; 
            transition: all 0.3s ease; 
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        button:hover { 
            transform: translateY(-2px); 
            box-shadow: 0 5px 15px rgba(0,0,0,0.3); 
        }
        .btn-success { background: linear-gradient(45deg, #56ab2f, #a8e6cf); }
        .btn-warning { background: linear-gradient(45deg, #f093fb, #f5576c); }
        
        .status-indicator { 
            display: inline-block; 
            width: 12px; 
            height: 12px; 
            border-radius: 50%; 
            margin-right: 10px; 
        }
        .status-success { background: #00ff88; }
        .status-error { background: #ff4757; }
        .status-pending { background: #ffa502; }
        
        pre { 
            background: rgba(0,0,0,0.3); 
            padding: 15px; 
            border-radius: 8px; 
            overflow-x: auto; 
            font-size: 12px;
            border: 1px solid rgba(255,255,255,0.1);
        }
        .metric-card {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.2);
            margin: 10px 0;
        }
        .metric-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #00ff88;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 GA4 Integration Test</h1>
            <p>Testing Google Analytics 4 with ID: <strong>G-B63EQLSE44</strong></p>
        </div>
        
        <div class="test-section">
            <h3>🔧 GA4 Setup Test</h3>
            <button onclick="testGA4Setup()">Test GA4 Setup</button>
            <button onclick="loadGA4Script()">Load GA4 Script</button>
            <div id="setup-results"></div>
        </div>
        
        <div class="test-section">
            <h3>📈 Event Tracking Test</h3>
            <button onclick="testPageView()">Test Page View</button>
            <button onclick="testCustomEvent()">Test Custom Event</button>
            <button onclick="testConversion()">Test Conversion</button>
            <div id="tracking-results"></div>
        </div>
        
        <div class="test-section">
            <h3>🎯 Real-Time Testing</h3>
            <button class="btn-success" onclick="sendTestEvents()">Send Test Events</button>
            <button class="btn-warning" onclick="openGA4RealTime()">Open GA4 Real-Time</button>
            <div id="realtime-results"></div>
        </div>
        
        <div class="metric-card">
            <div class="metric-value" id="ga4-status">❓</div>
            <div>GA4 Status</div>
        </div>
        
        <div id="all-results"></div>
    </div>

    <script>
        const GA4_ID = 'G-B63EQLSE44';
        
        function addResult(containerId, message, type = 'result') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `<span class="status-indicator status-${type === 'error' ? 'error' : type === 'success' ? 'success' : 'pending'}"></span>${message}`;
            container.appendChild(div);
            container.scrollTop = container.scrollHeight;
        }
        
        function loadGA4Script() {
            addResult('setup-results', '🔄 Loading GA4 script...', 'info');
            
            // Create dataLayer if it doesn't exist
            window.dataLayer = window.dataLayer || [];
            
            // Define gtag function
            window.gtag = function() {
                window.dataLayer.push(arguments);
            };

            // Initialize GA4
            window.gtag('js', new Date());
            window.gtag('config', GA4_ID, {
                send_page_view: true,
                allow_google_signals: true,
                allow_ad_personalization_signals: true
            });

            // Load GA4 script
            const script = document.createElement('script');
            script.async = true;
            script.src = `https://www.googletagmanager.com/gtag/js?id=${GA4_ID}`;
            script.onload = function() {
                addResult('setup-results', '✅ GA4 script loaded successfully', 'success');
                document.getElementById('ga4-status').textContent = '✅';
            };
            script.onerror = function() {
                addResult('setup-results', '❌ Failed to load GA4 script', 'error');
                document.getElementById('ga4-status').textContent = '❌';
            };
            document.head.appendChild(script);
        }
        
        function testGA4Setup() {
            addResult('setup-results', '🔄 Testing GA4 setup...', 'info');
            
            // Check if gtag is available
            if (typeof window.gtag === 'function') {
                addResult('setup-results', '✅ gtag function is available', 'success');
            } else {
                addResult('setup-results', '❌ gtag function not found - loading script...', 'warning');
                loadGA4Script();
                return;
            }
            
            // Check dataLayer
            if (window.dataLayer && Array.isArray(window.dataLayer)) {
                addResult('setup-results', `✅ dataLayer initialized with ${window.dataLayer.length} items`, 'success');
            } else {
                addResult('setup-results', '❌ dataLayer not found or not an array', 'error');
            }
            
            // Check if GA4 script is loaded
            const ga4Script = document.querySelector(`script[src*="${GA4_ID}"]`);
            if (ga4Script) {
                addResult('setup-results', '✅ GA4 script tag found in DOM', 'success');
            } else {
                addResult('setup-results', '⚠️ GA4 script tag not found in DOM', 'warning');
            }
        }
        
        function testPageView() {
            addResult('tracking-results', '🔄 Testing page view tracking...', 'info');
            
            if (typeof window.gtag === 'function') {
                window.gtag('config', GA4_ID, {
                    page_path: '/test-page-view',
                    page_title: 'GA4 Test Page View'
                });
                addResult('tracking-results', '✅ Page view event sent', 'success');
            } else {
                addResult('tracking-results', '❌ gtag not available for page view', 'error');
            }
        }
        
        function testCustomEvent() {
            addResult('tracking-results', '🔄 Testing custom event tracking...', 'info');
            
            if (typeof window.gtag === 'function') {
                window.gtag('event', 'test_custom_event', {
                    event_category: 'testing',
                    event_label: 'ga4_integration_test',
                    value: 1,
                    custom_parameter: 'test_value'
                });
                addResult('tracking-results', '✅ Custom event sent: test_custom_event', 'success');
            } else {
                addResult('tracking-results', '❌ gtag not available for custom event', 'error');
            }
        }
        
        function testConversion() {
            addResult('tracking-results', '🔄 Testing conversion tracking...', 'info');
            
            if (typeof window.gtag === 'function') {
                window.gtag('event', 'conversion', {
                    event_category: 'conversions',
                    event_label: 'test_conversion',
                    value: 100,
                    currency: 'USD'
                });
                addResult('tracking-results', '✅ Conversion event sent', 'success');
            } else {
                addResult('tracking-results', '❌ gtag not available for conversion', 'error');
            }
        }
        
        function sendTestEvents() {
            addResult('realtime-results', '🔄 Sending multiple test events...', 'info');
            
            if (typeof window.gtag !== 'function') {
                loadGA4Script();
                setTimeout(sendTestEvents, 2000);
                return;
            }
            
            const events = [
                { name: 'button_click', category: 'ui_interaction', label: 'test_button' },
                { name: 'form_start', category: 'form_interaction', label: 'test_form' },
                { name: 'affiliate_click', category: 'affiliate', label: 'test_affiliate_link' },
                { name: 'service_visit', category: 'services', label: 'test_service' },
                { name: 'blog_interaction', category: 'content', label: 'test_blog_post' }
            ];
            
            events.forEach((event, index) => {
                setTimeout(() => {
                    window.gtag('event', event.name, {
                        event_category: event.category,
                        event_label: event.label,
                        value: 1
                    });
                    addResult('realtime-results', `✅ Event sent: ${event.name}`, 'success');
                }, index * 500);
            });
            
            setTimeout(() => {
                addResult('realtime-results', '🎉 All test events sent! Check GA4 Real-Time reports.', 'success');
            }, events.length * 500);
        }
        
        function openGA4RealTime() {
            const ga4Url = `https://analytics.google.com/analytics/web/#/p/realtime/overview?params=_u..nav%3Dmaui%26_r.reportId%3Drealtime-overview&collectionId=life-cycle`;
            window.open(ga4Url, '_blank');
            addResult('realtime-results', '🔗 GA4 Real-Time dashboard opened in new tab', 'info');
            addResult('realtime-results', 'Look for events in the Real-Time report to verify tracking is working', 'info');
        }
        
        // Auto-load GA4 on page load
        window.onload = function() {
            addResult('all-results', '🚀 GA4 Integration Test Ready', 'success');
            addResult('all-results', `Testing with Measurement ID: ${GA4_ID}`, 'info');
            
            // Auto-load GA4 script
            setTimeout(() => {
                loadGA4Script();
            }, 1000);
        };
    </script>
</body>
</html>
