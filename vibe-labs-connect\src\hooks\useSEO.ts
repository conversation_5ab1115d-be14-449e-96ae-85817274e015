import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { seo, SEOData, StructuredData } from '@/services/seo';

/**
 * Hook for easy SEO management in React components
 */
export const useSEO = (seoData: SEOData, structuredData?: StructuredData) => {
  const location = useLocation();

  useEffect(() => {
    // Update meta tags
    const fullSeoData = {
      ...seoData,
      url: seoData.url || `${window.location.origin}${location.pathname}`
    };
    
    seo.updateMetaTags(fullSeoData);

    // Add structured data if provided
    if (structuredData) {
      seo.addStructuredData(structuredData);
    }

    // Cleanup function to reset title when component unmounts
    return () => {
      // Optional: Reset to default title when component unmounts
      // document.title = 'Vibe Labs Connect - Professional Web Development Services';
    };
  }, [seoData, structuredData, location.pathname]);

  return {
    updateSEO: (newSeoData: SEOData, newStructuredData?: StructuredData) => {
      const fullSeoData = {
        ...newSeoData,
        url: newSeoData.url || `${window.location.origin}${location.pathname}`
      };
      
      seo.updateMetaTags(fullSeoData);
      
      if (newStructuredData) {
        seo.addStructuredData(newStructuredData);
      }
    }
  };
};

/**
 * Hook specifically for service pages
 */
export const useServiceSEO = (serviceName: string, description: string, category: string, keywords?: string[]) => {
  const structuredData = seo.generateServiceStructuredData(serviceName, description, category);
  
  const seoData: SEOData = {
    title: `${serviceName} Services - Professional ${category} Solutions | Vibe Labs Connect`,
    description: description,
    keywords: keywords || [serviceName.toLowerCase(), category.toLowerCase(), 'web development', 'digital marketing'],
    type: 'service'
  };

  return useSEO(seoData, structuredData);
};

/**
 * Hook specifically for blog articles
 */
export const useBlogSEO = (
  title: string,
  description: string,
  author: string = 'Vibe Labs Connect Team',
  publishedDate: string,
  modifiedDate: string,
  tags: string[] = [],
  image?: string
) => {
  const structuredData = seo.generateArticleStructuredData(
    title,
    description,
    author,
    publishedDate,
    modifiedDate,
    image
  );
  
  const seoData: SEOData = {
    title: `${title} | Vibe Labs Connect Blog`,
    description: description,
    keywords: tags,
    type: 'article',
    author: author,
    publishedTime: publishedDate,
    modifiedTime: modifiedDate,
    tags: tags,
    image: image
  };

  return useSEO(seoData, structuredData);
};

/**
 * Hook for homepage SEO
 */
export const useHomepageSEO = () => {
  const organizationData = seo.generateOrganizationStructuredData();
  
  const seoData: SEOData = {
    title: 'Vibe Labs Connect - Professional Web Development & Digital Marketing Services',
    description: 'Transform your business with professional web development, digital marketing, and custom solutions. Serving fashion, real estate, music, coaching, and more industries.',
    keywords: [
      'web development',
      'digital marketing',
      'custom websites',
      'e-commerce solutions',
      'fashion websites',
      'real estate platforms',
      'music industry solutions',
      'coaching platforms',
      'business automation'
    ],
    type: 'website'
  };

  return useSEO(seoData, organizationData);
};

export default useSEO;
