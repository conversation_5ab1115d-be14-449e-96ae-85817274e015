import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import {
  Plus,
  Edit,
  Trash2,
  Save,
  Eye,
  Calendar,
  Search,
  Filter,
  MoreHorizontal,
  Upload,
  X,
  Lock,
  Unlock
} from 'lucide-react';
import { toast } from 'sonner';
import { blogStorage, commentStorage, type BlogPost, type BlogComment } from '@/utils/blogStorage';




const ADMIN_PASSWORD = import.meta.env.VITE_MARKETING_ADMIN_PASSWORD || '';

export const BlogAdmin: React.FC = () => {
  const [authenticated, setAuthenticated] = useState(false);
  const [password, setPassword] = useState('');
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [excerpt, setExcerpt] = useState('');
  const [category, setCategory] = useState('Marketing');
  const [featuredImage, setFeaturedImage] = useState('');
  const [tags, setTags] = useState('');
  const [published, setPublished] = useState(true);
  const [editingId, setEditingId] = useState<string | null>(null);

  // comments not stored here; comments are stored under localStorage key `blog_comments` as { postId: [comments] }

  useEffect(() => {
    if (authenticated) fetchPosts();
  }, [authenticated]);

  const fetchPosts = async () => {
    setLoading(true);
    try {
      const posts = await blogStorage.getPosts();
      setPosts(posts);
    } catch (error) {
      console.error('Error fetching posts:', error);
      toast.error('Failed to fetch posts');
    } finally {
      setLoading(false);
    }
  };

  // Legacy function - no longer needed with new storage system
  const savePosts = (list: any[]) => {
    localStorage.setItem('blog_posts', JSON.stringify(list));
    window.dispatchEvent(new Event('blog_posts_updated'));
  };

  const login = () => {
    if (!ADMIN_PASSWORD) {
      toast.error('Admin password not configured. Set VITE_MARKETING_ADMIN_PASSWORD in .env');
      return;
    }

    if (password === ADMIN_PASSWORD) {
      setAuthenticated(true);
      toast.success('Login successful! Welcome to Blog Admin', {
        duration: 3000,
        style: {
          background: 'rgba(34, 197, 94, 0.1)',
          border: '1px solid rgba(34, 197, 94, 0.3)',
          color: '#22c55e'
        }
      });
      // Sync URL: set hash and push /blog-admin path for this standalone admin
      try {
        // add hash marker
        if (window.location.hash !== '#blog-admin') {
          window.location.hash = 'blog-admin';
        }

        // push /blog-admin at end of current pathname if not already present
        const p = window.location.pathname || '';
        if (!p.endsWith('/blog-admin')) {
          const newPath = p.replace(/\/$/, '') + '/blog-admin';
          window.history.replaceState({}, '', newPath + window.location.search + window.location.hash);
        }
      } catch (e) {
        // ignore
      }
    } else {
      // Show prominent error popup for incorrect password
      toast.error('❌ Incorrect Password! Please check your admin password and try again.', {
        duration: 5000,
        style: {
          background: 'rgba(239, 68, 68, 0.1)',
          border: '1px solid rgba(239, 68, 68, 0.3)',
          color: '#ef4444',
          fontSize: '16px',
          fontWeight: '600'
        }
      });
      // Clear password field
      setPassword('');
    }
  };

  const logout = () => {
    setAuthenticated(false);
    // remove admin markers from url: hash and pathname
    try {
      if (window.location.hash === '#blog-admin') window.history.replaceState({}, '', window.location.pathname + window.location.search + '');
      // if pathname ends with /blog-admin remove it
      const p = window.location.pathname || '';
      if (p.endsWith('/blog-admin')) {
        const newPath = p.replace(/\/blog-admin$/, '') || '/';
        window.history.replaceState({}, '', newPath + window.location.search + window.location.hash);
      }
    } catch (e) {}
  };

  const createPost = async () => {
    if (!title || !content) return toast.error('Title and content required');

    try {
      if (editingId) {
        // Update existing post
        await blogStorage.updatePost(editingId, {
          title,
          content,
          excerpt,
          category,
          tags: tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0),
          featured_image: featuredImage,
          is_featured: false,
          published
        });
        toast.success('Post updated successfully!');
        setEditingId(null);
      } else {
        // Create new post
        await blogStorage.savePost({
          title,
          content,
          excerpt,
          category,
          tags: tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0),
          featured_image: featuredImage,
          is_featured: false,
          published,
          view_count: 0,
          seo_title: title,
          seo_description: excerpt
        });
        toast.success('Post created successfully!');
      }

      // Reset form
      setTitle('');
      setContent('');
      setExcerpt('');
      setFeaturedImage('');
      setTags('');

      // Refresh posts list
      await fetchPosts();
    } catch (error) {
      console.error('Error saving post:', error);
      toast.error('Failed to save post. Please try again.');
    }
  };

  const editPost = (id: string) => {
    const p = posts.find(post => post.id === id);
    if (!p) return toast.error('Post not found');
    setEditingId(id);
    setTitle(p.title || '');
    setExcerpt(p.excerpt || '');
    setContent(p.content || '');
    setFeaturedImage(p.featured_image || '');
    setCategory(p.category || 'Marketing');
    setTags(Array.isArray(p.tags) ? p.tags.join(', ') : (p.tags || ''));
    setPublished(Boolean(p.published));
  };

  const deletePost = async (id: string) => {
    try {
      await blogStorage.deletePost(id);
      toast.success('Post deleted successfully!');
      await fetchPosts();
    } catch (error) {
      console.error('Error deleting post:', error);
      toast.error('Failed to delete post. Please try again.');
    }
  };

  const fetchCommentsForPost = (postId: string) => {
    try {
      const comments = JSON.parse(localStorage.getItem('blog_comments') || '{}');
      return comments[postId] || [];
    } catch (e) {
      return [];
    }
  };

  const deleteComment = (postId: string, commentId: string) => {
    try {
      const comments = JSON.parse(localStorage.getItem('blog_comments') || '{}');
      comments[postId] = (comments[postId] || []).filter((c: any) => c.id !== commentId);
      localStorage.setItem('blog_comments', JSON.stringify(comments));
      toast.success('Comment removed');
      window.dispatchEvent(new Event('blog_posts_updated'));
    } catch (e) {
      console.error(e);
      toast.error('Failed to remove comment');
    }
  };

  // UI helpers: show comment panel per post, allow edit/delete inline
  const [openCommentsFor, setOpenCommentsFor] = useState<string | null>(null);
  const [editingComment, setEditingComment] = useState<{ postId: string; id: string; author: string; text: string } | null>(null);

  const openComments = (postId: string) => {
    setOpenCommentsFor(openCommentsFor === postId ? null : postId);
  };

  const saveEditedComment = () => {
    if (!editingComment) return;
    try {
      const commentsStore = JSON.parse(localStorage.getItem('blog_comments') || '{}');
      commentsStore[editingComment.postId] = (commentsStore[editingComment.postId] || []).map((c: any) => c.id === editingComment.id ? { ...c, author: editingComment.author, text: editingComment.text } : c);
      localStorage.setItem('blog_comments', JSON.stringify(commentsStore));
      toast.success('Comment updated');
      setEditingComment(null);
      window.dispatchEvent(new Event('blog_posts_updated'));
    } catch (e) {
      console.error(e);
      toast.error('Failed to update comment');
    }
  };

  if (!authenticated) {
    return (
      <div className="min-h-screen premium-gradient-bg flex items-center justify-center p-4">
        <Card className="w-full max-w-md glass-effect">
          <CardHeader className="text-center">
            <div className="flex items-center justify-center mb-4">
              <Lock className="w-8 h-8 text-accent" />
            </div>
            <CardTitle className="text-3xl font-bold text-white">Blog Admin</CardTitle>
            <p className="text-muted-foreground">Enter admin password to manage blog posts</p>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="password" className="text-white font-medium">Password</Label>
              <Input
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter admin password"
                type="password"
                className="bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-accent"
                onKeyPress={(e) => e.key === 'Enter' && login()}
              />
            </div>
            <Button
              onClick={login}
              className="w-full bg-accent text-accent-foreground hover:bg-accent/90 hover-lift"
            >
              <Unlock className="w-4 h-4 mr-2" />
              Login to Admin
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen premium-gradient-bg">
      <div className="p-6 max-w-6xl mx-auto">
        <div className="flex items-center justify-between mb-8 fade-in-up">
          <div>
            <h1 className="text-4xl font-bold text-white mb-2">Blog Admin Dashboard</h1>
            <p className="text-muted-foreground">Create and manage your blog posts</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={logout} className="border-accent text-accent hover:bg-accent hover:text-accent-foreground">
              <Lock className="w-4 h-4 mr-2" />
              Logout
            </Button>
          </div>
        </div>

        <Card className="mb-8 glass-effect fade-in-up" style={{animationDelay: '0.1s'}}>
          <CardHeader>
            <CardTitle className="text-2xl text-white flex items-center gap-2">
              <Plus className="w-6 h-6" />
              {editingId ? 'Edit Post' : 'Create New Post'}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="title" className="text-white font-medium">Title *</Label>
                <Input
                  id="title"
                  placeholder="Enter post title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  className="bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-accent"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="category" className="text-white font-medium">Category</Label>
                <Input
                  id="category"
                  placeholder="e.g., Marketing, Technology"
                  value={category}
                  onChange={(e) => setCategory(e.target.value)}
                  className="bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-accent"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="excerpt" className="text-white font-medium">Excerpt</Label>
              <Input
                id="excerpt"
                placeholder="Brief description of the post"
                value={excerpt}
                onChange={(e) => setExcerpt(e.target.value)}
                className="bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-accent"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="content" className="text-white font-medium">Content *</Label>
              <Textarea
                id="content"
                placeholder="Write your post content here (supports markdown and HTML)"
                value={content}
                onChange={(e) => setContent(e.target.value)}
                rows={10}
                className="bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-accent"
              />
            </div>

            <div className="grid md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="featuredImage" className="text-white font-medium">Featured Image URL</Label>
                <Input
                  id="featuredImage"
                  placeholder="https://example.com/image.jpg"
                  value={featuredImage}
                  onChange={(e) => setFeaturedImage(e.target.value)}
                  className="bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-accent"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="tags" className="text-white font-medium">Tags</Label>
                <Input
                  id="tags"
                  placeholder="marketing, strategy, growth (comma separated)"
                  value={tags}
                  onChange={(e) => setTags(e.target.value)}
                  className="bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-accent"
                />
              </div>
            </div>

            <div className="flex items-center gap-3">
              <Switch
                checked={published}
                onCheckedChange={(v) => setPublished(Boolean(v))}
                className="data-[state=checked]:bg-accent"
              />
              <Label className="text-white font-medium">Publish immediately</Label>
            </div>

            <div className="flex gap-3 pt-4">
              <Button
                onClick={createPost}
                className="bg-accent text-accent-foreground hover:bg-accent/90 hover-lift"
              >
                <Save className="w-4 h-4 mr-2" />
                {editingId ? 'Update Post' : 'Create Post'}
              </Button>
              {editingId && (
                <Button
                  variant="outline"
                  onClick={() => {
                    setEditingId(null);
                    setTitle('');
                    setContent('');
                    setExcerpt('');
                    setFeaturedImage('');
                    setTags('');
                  }}
                  className="border-white/20 text-white hover:bg-white/10"
                >
                  <X className="w-4 h-4 mr-2" />
                  Cancel
                </Button>
              )}
            </div>
          </CardContent>
        </Card>

        <Card className="glass-effect fade-in-up" style={{animationDelay: '0.2s'}}>
          <CardHeader>
            <CardTitle className="text-2xl text-white flex items-center gap-2">
              <Eye className="w-6 h-6" />
              Published Posts ({posts.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {loading ? (
                <div className="text-center py-8">
                  <div className="text-white">Loading posts...</div>
                </div>
              ) : posts.length === 0 ? (
                <div className="text-center py-8">
                  <div className="text-muted-foreground">No posts yet. Create your first post above!</div>
                </div>
              ) : (
                posts.map((p, index) => (
                  <Card key={p.id} className="glass-effect hover-lift" style={{animationDelay: `${0.3 + index * 0.1}s`}}>
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="text-lg font-semibold text-white">{p.title}</h3>
                            <Badge className="bg-accent/20 text-accent border-accent/30">
                              {p.category}
                            </Badge>
                            {p.is_featured && (
                              <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">
                                Featured
                              </Badge>
                            )}
                          </div>
                          <p className="text-muted-foreground text-sm mb-3 line-clamp-2">
                            {p.excerpt || p.content.substring(0, 140)}...
                          </p>
                          <div className="flex items-center gap-4 text-xs text-muted-foreground">
                            <span className="flex items-center gap-1">
                              <Calendar className="w-3 h-3" />
                              {new Date(p.created_at).toLocaleDateString()}
                            </span>
                            <span className="flex items-center gap-1">
                              <Eye className="w-3 h-3" />
                              {p.view_count || 0} views
                            </span>
                            {p.tags && (
                              <span>Tags: {p.tags}</span>
                            )}
                          </div>
                        </div>
                        <div className="flex gap-2 ml-4">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => editPost(p.id)}
                            className="border-accent/30 text-accent hover:bg-accent hover:text-accent-foreground"
                          >
                            <Edit className="w-4 h-4 mr-1" />
                            Edit
                          </Button>
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => deletePost(p.id)}
                            className="hover-lift"
                          >
                            <Trash2 className="w-4 h-4 mr-1" />
                            Delete
                          </Button>
                          <Button
                            size="sm"
                            onClick={() => openComments(p.id)}
                            className="bg-accent/20 text-accent hover:bg-accent hover:text-accent-foreground"
                          >
                            {openCommentsFor === p.id ? 'Hide Comments' : 'Comments'}
                          </Button>
                        </div>
                      </div>
                    {openCommentsFor === p.id && (
                      <div className="mt-3 space-y-2">
                        {fetchCommentsForPost(p.id).length === 0 ? (
                          <div className="text-sm text-muted-foreground">No comments yet.</div>
                        ) : (
                          fetchCommentsForPost(p.id).map((c: any) => (
                            <Card key={c.id} className="p-3">
                              <div className="flex items-start justify-between">
                                <div>
                                  <div className="font-medium">{c.author}</div>
                                  <div className="text-sm text-muted-foreground">{c.text}</div>
                                  <div className="text-xs text-muted-foreground mt-1">{new Date(c.created_at).toLocaleString()}</div>
                                </div>
                                <div className="flex flex-col items-end gap-2">
                                  <div className="flex gap-2">
                                    <Button size="sm" variant="outline" onClick={() => setEditingComment({ postId: p.id, id: c.id, author: c.author, text: c.text })}>Edit</Button>
                                    <Button size="sm" variant="destructive" onClick={() => deleteComment(p.id, c.id)}>Delete</Button>
                                  </div>
                                </div>
                              </div>
                            </Card>
                          ))
                        )}
                      </div>
                    )}
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </CardContent>
        </Card>

        {editingComment && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 p-4">
          <Card className="w-full max-w-2xl">
            <CardContent>
              <h3 className="text-xl font-semibold mb-2">Edit Comment</h3>
              <Input value={editingComment.author} onChange={(e) => setEditingComment({ ...editingComment, author: e.target.value })} placeholder="Author" />
              <Textarea value={editingComment.text} onChange={(e) => setEditingComment({ ...editingComment, text: e.target.value })} rows={4} className="mt-2" />
              <div className="flex gap-2 mt-4">
                <Button onClick={saveEditedComment}>Save</Button>
                <Button variant="ghost" onClick={() => setEditingComment(null)}>Cancel</Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
      </div>
    </div>
  );
};

export default BlogAdmin;
