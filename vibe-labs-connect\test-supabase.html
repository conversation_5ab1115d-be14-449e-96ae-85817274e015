<!DOCTYPE html>
<html>
<head>
    <title>Test Supabase Connection</title>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
</head>
<body>
    <h1>Supabase Connection Test</h1>
    <button onclick="testConnection()">Test Connection</button>
    <div id="result"></div>

    <script>
        const supabaseUrl = 'https://pujwroltuqzfqnozymif.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB1andyb2x0dXF6ZnFub3p5bWlmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTYxMDk2NzUsImV4cCI6MjA3MTY4NTY3NX0.GJS-nX8N4HBIB_7JI93XNW_yUIA_bZF6LYjCiUwgaIU';
        
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);

        async function testConnection() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing connection...';

            try {
                // Test 1: Check if we can connect
                const { data: tables, error: tablesError } = await supabase
                    .from('blog_posts')
                    .select('*')
                    .limit(1);

                if (tablesError) {
                    resultDiv.innerHTML = `<p style="color: red;">Error: ${tablesError.message}</p>
                                          <p>This usually means the blog_posts table doesn't exist yet.</p>
                                          <p>Please run the SQL setup in your Supabase dashboard.</p>`;
                    return;
                }

                // Test 2: Try to fetch posts
                const { data: posts, error: postsError } = await supabase
                    .from('blog_posts')
                    .select('*');

                if (postsError) {
                    resultDiv.innerHTML = `<p style="color: red;">Error fetching posts: ${postsError.message}</p>`;
                    return;
                }

                resultDiv.innerHTML = `
                    <p style="color: green;">✅ Connection successful!</p>
                    <p>Found ${posts.length} blog posts in database.</p>
                    <pre>${JSON.stringify(posts, null, 2)}</pre>
                `;

            } catch (error) {
                resultDiv.innerHTML = `<p style="color: red;">Connection failed: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
