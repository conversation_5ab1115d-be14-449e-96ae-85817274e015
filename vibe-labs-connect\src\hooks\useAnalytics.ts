import { useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';
import { analytics } from '@/services/analytics';

/**
 * Hook for easy analytics integration in React components
 */
export const useAnalytics = () => {
  const location = useLocation();
  const startTimeRef = useRef<number>(Date.now());
  const scrollDepthRef = useRef<number>(0);

  // Track page views on route changes
  useEffect(() => {
    analytics.trackPageView(location.pathname + location.search);
    startTimeRef.current = Date.now();
  }, [location]);

  // Track engagement time when component unmounts or route changes
  useEffect(() => {
    return () => {
      const timeSpent = Date.now() - startTimeRef.current;
      if (timeSpent > 5000) { // Only track if user spent more than 5 seconds
        analytics.trackEngagementTime(timeSpent, location.pathname);
      }
    };
  }, [location.pathname]);

  // Track scroll depth
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const documentHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrollPercentage = Math.round((scrollTop / documentHeight) * 100);

      // Track scroll milestones (25%, 50%, 75%, 100%)
      if (scrollPercentage >= 25 && scrollDepthRef.current < 25) {
        analytics.trackScrollDepth(25, location.pathname);
        scrollDepthRef.current = 25;
      } else if (scrollPercentage >= 50 && scrollDepthRef.current < 50) {
        analytics.trackScrollDepth(50, location.pathname);
        scrollDepthRef.current = 50;
      } else if (scrollPercentage >= 75 && scrollDepthRef.current < 75) {
        analytics.trackScrollDepth(75, location.pathname);
        scrollDepthRef.current = 75;
      } else if (scrollPercentage >= 100 && scrollDepthRef.current < 100) {
        analytics.trackScrollDepth(100, location.pathname);
        scrollDepthRef.current = 100;
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [location.pathname]);

  // Reset scroll depth tracking on route change
  useEffect(() => {
    scrollDepthRef.current = 0;
  }, [location.pathname]);

  return {
    // Event tracking methods
    trackEvent: analytics.trackEvent,
    trackAffiliateClick: analytics.trackAffiliateClick,
    trackServiceVisit: analytics.trackServiceVisit,
    trackContactSubmission: analytics.trackContactSubmission,
    trackBlogInteraction: analytics.trackBlogInteraction,
    trackConversion: analytics.trackConversion,
    setUserProperties: analytics.setUserProperties,
    
    // Convenience methods
    trackButtonClick: (buttonName: string, location?: string) => {
      analytics.trackEvent('button_click', {
        category: 'ui_interaction',
        label: buttonName,
        button_location: location
      });
    },
    
    trackFormStart: (formName: string) => {
      analytics.trackEvent('form_start', {
        category: 'form_interaction',
        label: formName
      });
    },
    
    trackFormComplete: (formName: string) => {
      analytics.trackEvent('form_complete', {
        category: 'form_interaction',
        label: formName
      });
    },
    
    trackDownload: (fileName: string, fileType: string) => {
      analytics.trackEvent('file_download', {
        category: 'downloads',
        label: fileName,
        file_type: fileType
      });
    },
    
    trackVideoPlay: (videoTitle: string, videoPosition: number = 0) => {
      analytics.trackEvent('video_play', {
        category: 'video',
        label: videoTitle,
        video_position: videoPosition
      });
    },
    
    trackSearch: (searchTerm: string, resultsCount: number) => {
      analytics.trackEvent('search', {
        category: 'search',
        label: searchTerm,
        search_results: resultsCount
      });
    }
  };
};

export default useAnalytics;
