import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  TrendingUp,
  Globe,
  Video,
  FileText,
  Share2,
  BarChart3,
  Target,
  Zap,
  Users,
  Mail,
  Phone,
  ArrowRight,
  Check,
  BookOpen,
  Megaphone,
  Camera,
  PenTool,
  Database,
  MonitorPlay,
  Star,
  Quote,
  ExternalLink,
  Play,
  Award,
  Rocket,
  TrendingDown,
  Eye,
  MousePointer,
  DollarSign
} from 'lucide-react';
import { BlogList } from './components/blog/BlogList';
import { BlogPost } from './components/blog/BlogPost';
import { BlogAdmin } from './components/blog/BlogAdmin';
import { toast } from 'sonner';

const VoiceServiceApp: React.FC = () => {
  const [currentView, setCurrentView] = useState<'home' | 'blog' | 'blog-post' | 'admin'>('home');
  const [currentBlogSlug, setCurrentBlogSlug] = useState<string>('');
  const [activeSection, setActiveSection] = useState<string>('home');
  const [contactForm, setContactForm] = useState({
    name: '',
    email: '',
    company: '',
    phone: '',
    service: '',
    budget: '',
    message: ''
  });

  // Initialize sample voice blog posts
  React.useEffect(() => {
    // Declare samplePosts outside try/catch so it's always available
    const samplePosts = [
      {
        id: 'voice-1',
        title: '70+ Domain Distribution: The Ultimate voice Strategy for 2024',
        slug: '70-domain-distribution-strategy-2024',
        excerpt: 'Discover how distributing your content across 70+ domains can exponentially increase your reach, authority, and conversions in the digital landscape.',
        content: `# 70+ Domain Distribution: The Ultimate voice Strategy for 2024

In today's competitive digital landscape, businesses need every advantage they can get. One of the most powerful yet underutilized strategies is domain distribution - spreading your content and presence across multiple high-authority domains.

## What is Domain Distribution?

Domain distribution involves strategically placing your content, backlinks, and brand mentions across a network of relevant, high-authority websites. This isn't about spam or black-hat SEO - it's about legitimate, value-driven content placement that benefits both your brand and the host domains.

## The Power of 70+ Domains

When you distribute across 70+ carefully selected domains, you create:

- **Massive Authority Boost**: Search engines see your brand mentioned across numerous trusted sources
- **Exponential Reach**: Each domain has its own audience, multiplying your potential reach
- **Risk Mitigation**: Your voice isn't dependent on a single platform or domain
- **Competitive Advantage**: Most competitors focus on 1-5 domains maximum

## Implementation Strategy

### 1. Domain Selection
Choose domains based on:
- Domain Authority (DA 30+)
- Relevance to your industry
- Active, engaged audiences
- Editorial standards

### 2. Content Strategy
- Create unique, valuable content for each domain
- Maintain consistent brand messaging
- Focus on solving real problems
- Include strategic calls-to-action

### 3. Tracking and Optimization
- Monitor performance across all domains
- A/B test different content approaches
- Optimize based on engagement metrics
- Scale successful strategies

## Results You Can Expect

Our clients typically see:
- 300-500% increase in organic traffic
- 250% boost in brand authority
- 400% improvement in lead generation
- 200% increase in conversion rates

## Getting Started

Domain distribution requires expertise, relationships, and ongoing management. That's where we come in. Our team has established relationships with 70+ high-authority domains across various industries.

Ready to dominate your market with domain distribution? Contact us for a free strategy consultation.`,
        featured_image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
        author_name: 'voice Team',
        category: 'voice',
        tags: ['Domain Distribution', 'SEO', 'Content voice', 'Digital Strategy'],
        reading_time: 8,
        view_count: 1247,
        published_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        is_featured: true,
        published: true
      },
      {
        id: 'voice-2',
        title: 'Social Media Automation: Scale Your Presence Across All Platforms',
        slug: 'social-media-automation-scale-presence',
        excerpt: 'Learn how to automate your social media voice while maintaining authenticity and engagement across multiple platforms.',
        content: `# Social Media Automation: Scale Your Presence Across All Platforms

Social media voice is essential, but managing multiple platforms manually is time-consuming and inefficient. Smart automation can help you scale your presence while maintaining quality and engagement.

## The Automation Advantage

Proper social media automation allows you to:
- Post consistently across all platforms
- Engage with your audience 24/7
- Analyze performance in real-time
- Scale your efforts without scaling your team

## Key Automation Strategies

### Content Scheduling
- Plan and schedule posts weeks in advance
- Optimize posting times for each platform
- Maintain consistent brand voice
- Adapt content for platform-specific audiences

### Engagement Automation
- Auto-respond to common questions
- Like and comment on relevant posts
- Follow/unfollow based on criteria
- Monitor brand mentions

### Analytics and Reporting
- Track performance across all platforms
- Generate automated reports
- Identify trending content
- Optimize strategy based on data

## Best Practices

1. **Maintain Authenticity**: Automation should enhance, not replace, human interaction
2. **Platform Optimization**: Customize content for each platform's unique audience
3. **Regular Monitoring**: Review and adjust automated processes regularly
4. **Quality Control**: Always prioritize quality over quantity

## Tools and Technologies

We use advanced automation tools that integrate with:
- Facebook, Instagram, Twitter, LinkedIn
- TikTok, YouTube, Pinterest
- Industry-specific platforms
- Custom API integrations

## Results

Our automated social media strategies typically deliver:
- 400% increase in posting frequency
- 250% boost in engagement rates
- 300% improvement in lead generation
- 200% reduction in management time

Ready to automate your social media success? Let's discuss your automation strategy.`,
        featured_image: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
        author_name: 'Social Media Team',
        category: 'voice',
        tags: ['Social Media', 'Automation', 'Digital voice', 'Engagement'],
        reading_time: 6,
        view_count: 892,
        published_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        is_featured: false,
        published: true
      },
      {
        id: 'voice-3',
        title: 'Video voice Trends: What\'s Working in 2024',
        slug: 'video-voice-trends-2024',
        excerpt: 'Explore the latest video voice trends and strategies that are driving engagement and conversions in 2024.',
        content: `# Video voice Trends: What's Working in 2024

Video content continues to dominate digital voice, but the landscape is constantly evolving. Here are the trends and strategies that are driving results in 2024.

## Top Video voice Trends

### 1. Short-Form Vertical Videos
- TikTok-style content on all platforms
- 15-60 second videos perform best
- Mobile-first approach essential
- High engagement rates

### 2. Interactive Video Content
- Polls, quizzes, and clickable elements
- Choose-your-own-adventure style videos
- Increased viewer engagement
- Better conversion tracking

### 3. Live Streaming
- Real-time audience interaction
- Behind-the-scenes content
- Product launches and demos
- Q&A sessions

### 4. AI-Generated Video Content
- Automated video creation
- Personalized video messages
- Cost-effective scaling
- Consistent brand messaging

## Platform-Specific Strategies

### YouTube
- Long-form educational content
- SEO-optimized titles and descriptions
- Consistent upload schedule
- Community engagement

### Instagram/Facebook
- Stories and Reels
- User-generated content
- Shopping integration
- Cross-platform promotion

### TikTok
- Trending audio and hashtags
- Authentic, unpolished content
- Quick entertainment value
- Viral potential

### LinkedIn
- Professional educational content
- Industry insights
- Thought leadership
- B2B networking

## Production Tips

1. **Quality vs. Quantity**: Focus on consistent, good-quality content over perfection
2. **Mobile Optimization**: Design for mobile viewing first
3. **Captions and Subtitles**: Make content accessible
4. **Strong Hooks**: Capture attention in the first 3 seconds

## Measuring Success

Key metrics to track:
- View completion rates
- Engagement (likes, comments, shares)
- Click-through rates
- Conversion rates
- Brand awareness lift

## Getting Started

Video voice requires strategy, creativity, and technical expertise. Our team creates compelling video content that drives results across all platforms.

Ready to leverage video voice for your business? Let's create your video strategy.`,
        featured_image: 'https://images.unsplash.com/photo-1574717024653-61fd2cf4d44d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
        author_name: 'Video voice Team',
        category: 'voice',
        tags: ['Video voice', 'Content Strategy', 'Social Media', 'Trends'],
        reading_time: 7,
        view_count: 1156,
        published_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        is_featured: true,
        published: true
      }
    ];
    try {
      let needsUpdate = false;
      const existingPosts = localStorage.getItem('blog_posts');
      let currentPosts = existingPosts ? JSON.parse(existingPosts) : [];
      
      // Ensure we have at least 6 voice posts
      if (!currentPosts || currentPosts.length < 6) {
        needsUpdate = true;
      } else {
        // Verify posts have required fields
        needsUpdate = currentPosts.some((p: any) => 
          !p.id || !p.title || !p.slug || !p.content || !p.featured_image
        );
      }

      if (needsUpdate) {
        // Merge with existing posts, using samplePosts for missing ones
        const merged = currentPosts.length ? [
          ...currentPosts,
          ...samplePosts.filter((sample: any) => 
            !currentPosts.find((p: any) => p.id === sample.id)
          )
        ] : samplePosts;
        
        localStorage.setItem('blog_posts', JSON.stringify(merged));
        window.dispatchEvent(new Event('blog_posts_updated'));
      }
    } catch (error) {
      console.error('Error initializing blog posts:', error);
      // Fallback: Set sample posts directly
      localStorage.setItem('blog_posts', JSON.stringify(samplePosts));
      window.dispatchEvent(new Event('blog_posts_updated'));
    }
  }, []);

  // Testimonials data
  const testimonials = [
    {
      id: 1,
      name: "Sarah Johnson",
      company: "TechStart Inc.",
      role: "CEO",
      image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
      rating: 5,
      text: "IssyLabs transformed our voice strategy completely. Our lead generation increased by 300% in just 3 months!"
    },
    {
      id: 2,
      name: "Michael Chen",
      company: "GrowthCorp",
      role: "voice Director",
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
      rating: 5,
      text: "The multi-domain strategy and content distribution helped us reach global markets we never thought possible."
    },
    {
      id: 3,
      name: "Emily Rodriguez",
      company: "Fashion Forward",
      role: "Founder",
      image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
      rating: 5,
      text: "Professional, results-driven, and incredibly creative. Our brand visibility skyrocketed!"
    }
  ];

  // Portfolio data
  const portfolioItems = [
    {
      id: 1,
      title: "E-commerce Growth Campaign",
      category: "Digital voice",
      image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=500&h=300&fit=crop",
      results: "450% ROI increase",
      description: "Complete digital transformation for online retail brand",
      link: "#"
    },
    {
      id: 2,
      title: "SaaS Lead Generation",
      category: "Content voice",
      image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=500&h=300&fit=crop",
      results: "300% lead increase",
      description: "Strategic content voice and automation setup",
      link: "#"
    },
    {
      id: 3,
      title: "Brand Awareness Campaign",
      category: "Social Media",
      image: "https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=500&h=300&fit=crop",
      results: "2M+ impressions",
      description: "Multi-platform brand awareness and engagement strategy",
      link: "#"
    }
  ];

  const voiceServices = [
    {
      icon: <Globe className="w-8 h-8" />,
      title: "Domain & Web Presence",
      description: "70+ domain distribution, website optimization, and online presence management.",
      features: ["Multi-domain strategy", "SEO optimization", "Brand consistency", "Global reach"],
      domains: "70+ Domains",
      price: "From $2,999/month"
    },
    {
      icon: <FileText className="w-8 h-8" />,
      title: "Content Publishing",
      description: "Strategic content creation and distribution across all major platforms.",
      features: ["Blog content", "Press releases", "Article distribution", "Content calendar"],
      domains: "Content Strategy",
      price: "From $1,999/month"
    },
    {
      icon: <Share2 className="w-8 h-8" />,
      title: "Social Media Management",
      description: "Complete social media strategy, content creation, and community management.",
      features: ["Platform management", "Content creation", "Community engagement", "Analytics tracking"],
      domains: "All Platforms"
    },
    {
      icon: <Video className="w-8 h-8" />,
      title: "Video Production",
      description: "Professional video content for voice, tutorials, and brand storytelling.",
      features: ["Promotional videos", "Tutorial content", "Brand storytelling", "Platform optimization"],
      domains: "Multi-format"
    },
    {
      icon: <Target className="w-8 h-8" />,
      title: "Digital Advertising",
      description: "Targeted ad campaigns across Google, Facebook, LinkedIn, and emerging platforms.",
      features: ["PPC campaigns", "Social ads", "Retargeting", "Performance optimization"],
      domains: "Cross-platform"
    },
    {
      icon: <BarChart3 className="w-8 h-8" />,
      title: "Analytics & Reporting",
      description: "Comprehensive data analysis and performance reporting across all channels.",
      features: ["Performance tracking", "ROI analysis", "Custom dashboards", "Strategic insights"],
      domains: "Data-driven"
    }
  ];

  const packages = [
    {
      name: "Growth Starter",
      price: "$499/mo",
      description: "Perfect for growing businesses",
      features: [
        "10 Domain Distribution",
        "Social Media Management (3 platforms)",
        "Monthly Content Calendar",
        "Basic Analytics Dashboard",
        "Email voice Setup"
      ],
      popular: false,
      cta: "Start Growing"
    },
    {
      name: "Scale Master",
      price: "$1,299/mo",
      description: "Comprehensive voice automation",
      features: [
        "35 Domain Distribution",
        "Full Social Media Suite (All platforms)",
        "Video Content Production (2/month)",
        "Advanced Analytics & Reporting",
        "PPC Campaign Management",
        "Content Publishing Network"
      ],
      popular: true,
      cta: "Scale Now"
    },
    {
      name: "Enterprise Domination",
      price: "$2,999/mo",
      description: "Complete market domination strategy",
      features: [
        "70+ Domain Distribution",
        "Omnichannel voice",
        "Weekly Video Production",
        "Custom Analytics Platform",
        "Dedicated Account Manager",
        "24/7 Campaign Optimization",
        "Global Market Expansion",
        "Competitive Intelligence"
      ],
      popular: false,
      cta: "Dominate Market"
    }
  ];

  const industries = [
    "E-commerce", "SaaS", "Healthcare", "Finance", "Real Estate", "Education", 
    "Technology", "Manufacturing", "Retail", "Professional Services", "Entertainment",
    "Non-profit", "Hospitality", "Automotive", "Fashion", "Food & Beverage"
  ];

  const handleBlogNavigation = (slug: string) => {
    setCurrentBlogSlug(slug);
    setCurrentView('blog-post');
  };

  const scrollToSectionId = (id: string) => {
    // If not on home, go to home then scroll after a tick so DOM is present
    const doScroll = () => {
      const el = document.getElementById(id);
      if (el) el.scrollIntoView({ behavior: 'smooth' });
    };

    // set the active section immediately so nav highlights right away
    setActiveSection(id);

    if (currentView !== 'home') {
      setCurrentView('home');
      // wait for render then scroll
      setTimeout(doScroll, 220);
    } else {
      doScroll();
    }
  };

  // Observe sections on the home view and update activeSection on scroll
  React.useEffect(() => {
    if (currentView !== 'home') return;

    const ids = ['home', 'about', 'services', 'portfolio', 'testimonials', 'contact'];
    const elements = ids.map(id => document.getElementById(id)).filter(Boolean) as HTMLElement[];
    if (elements.length === 0) return;

    let observer: IntersectionObserver | null = null;
    observer = new IntersectionObserver(
      (entries) => {
        // pick the entry with largest intersectionRatio
        const visible = entries
          .filter(e => e.isIntersecting)
          .sort((a, b) => b.intersectionRatio - a.intersectionRatio);
        if (visible.length > 0) {
          const id = visible[0].target.id;
          setActiveSection(id);
        }
      },
      { threshold: [0.25, 0.5, 0.75] }
    );

    elements.forEach(el => observer?.observe(el));
    return () => observer?.disconnect();
  }, [currentView]);

  // Keep a passive scroll listener for potential future use, but DO NOT
  // redirect when near the top. Redirects caused surprising navigation when
  // users were viewing blog pages or deep sections.
  React.useEffect(() => {
    let lastY = window.scrollY;
    const onScroll = () => {
      try {
        const y = window.scrollY || window.pageYOffset || 0;
        lastY = y;
      } catch (e) {}
    };

    window.addEventListener('scroll', onScroll, { passive: true });
    return () => window.removeEventListener('scroll', onScroll);
  }, [currentView]);

  // support opening via hash (fallback for other pages)
  React.useEffect(() => {
    const checkHash = () => {
      const hash = window.location.hash.replace(/^#/, '');
      if (hash.startsWith('blog/')) {
        const slug = hash.replace('blog/', '');
        setCurrentBlogSlug(slug);
        setCurrentView('blog-post');
      }

      if (hash === 'voice-admin') {
        setCurrentView('admin');
      }
    };

    const checkPath = () => {
      // if URL path ends with /admin, open admin
      try {
        const p = window.location.pathname || '';
        if (p.endsWith('/admin')) setCurrentView('admin');
      } catch (e) {}
    };

    checkHash();
    checkPath();
    window.addEventListener('hashchange', checkHash);
    window.addEventListener('popstate', checkPath);
    return () => {
      window.removeEventListener('hashchange', checkHash);
      window.removeEventListener('popstate', checkPath);
    };
  }, []);

  const handleContactSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      // Simulate email sending (replace with actual email service)
      const emailData = {
        to: '<EMAIL>',
        subject: `New voice Service Inquiry from ${contactForm.name}`,
        html: `
          <h2>New voice Service Inquiry</h2>
          <p><strong>Name:</strong> ${contactForm.name}</p>
          <p><strong>Email:</strong> ${contactForm.email}</p>
          <p><strong>Company:</strong> ${contactForm.company}</p>
          <p><strong>Phone:</strong> ${contactForm.phone}</p>
          <p><strong>Service:</strong> ${contactForm.service}</p>
          <p><strong>Budget:</strong> ${contactForm.budget}</p>
          <p><strong>Message:</strong> ${contactForm.message}</p>
        `
      };

      // Store in localStorage for now (replace with actual email service)
      const existingInquiries = JSON.parse(localStorage.getItem('voice_inquiries') || '[]');
      existingInquiries.push({
        ...contactForm,
        timestamp: new Date().toISOString(),
        id: Date.now().toString()
      });
      localStorage.setItem('voice_inquiries', JSON.stringify(existingInquiries));

      toast.success('Thank you! Your inquiry has been sent. We\'ll get back to you within 24 hours.');

      // Reset form
      setContactForm({
        name: '',
        email: '',
        company: '',
        phone: '',
        service: '',
        budget: '',
        message: ''
      });
    } catch (error) {
      toast.error('Something went wrong. Please try again or contact us directly.');
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setContactForm(prev => ({ ...prev, [field]: value }));
  };

  const renderNavigation = () => (
    <nav className="bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-0 z-50 border-b">
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center gap-2">
            <TrendingUp className="w-8 h-8 text-primary" />
            <span className="text-xl font-bold">EasyLabs voice</span>
          </div>
          <div className="hidden md:flex items-center space-x-8">
            <button
              onClick={() => { scrollToSectionId('home'); window.location.hash = ''; setCurrentView('home'); }}
              className={`hover:text-primary transition-colors ${(currentView === 'home' && activeSection === 'home') ? 'text-primary font-medium' : ''}`}
            >
              Home
            </button>
            <button onClick={() => { scrollToSectionId('about'); }} className={`hover:text-primary transition-colors ${activeSection === 'about' ? 'text-primary font-medium' : ''}`}>About</button>
            <button onClick={() => { scrollToSectionId('services'); }} className={`hover:text-primary transition-colors ${activeSection === 'services' ? 'text-primary font-medium' : ''}`}>Services</button>
            <button onClick={() => { scrollToSectionId('portfolio'); }} className={`hover:text-primary transition-colors ${activeSection === 'portfolio' ? 'text-primary font-medium' : ''}`}>Portfolio</button>
            <button onClick={() => { scrollToSectionId('testimonials'); }} className={`hover:text-primary transition-colors ${activeSection === 'testimonials' ? 'text-primary font-medium' : ''}`}>Testimonials</button>
            <button onClick={() => { scrollToSectionId('contact'); }} className={`hover:text-primary transition-colors ${activeSection === 'contact' ? 'text-primary font-medium' : ''}`}>Contact</button>
            <button 
              onClick={() => setCurrentView('blog')}
              className={`hover:text-primary transition-colors flex items-center gap-1 ${currentView === 'blog' ? 'text-primary font-medium' : ''}`}
            >
              <BookOpen className="w-4 h-4" />
              Blog
            </button>
          </div>
          {/* Admin is intentionally not exposed in the main nav. Access via a direct URL/hash: #voice-admin */}
        </div>
      </div>
    </nav>
  );

  if (currentView === 'blog-post') {
    return (
      <div className="min-h-screen premium-gradient-bg">
        {renderNavigation()}
        <div className="pt-32">
          <BlogPost
            slug={currentBlogSlug}
            onBack={() => setCurrentView('blog')}
          />
        </div>
      </div>
    );
  }

  if (currentView === 'blog') {
    return (
      <div className="min-h-screen premium-gradient-bg">
        {renderNavigation()}
        <div className="pt-32 pb-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16 fade-in-up">
              <h1 className="text-5xl md:text-6xl font-bold mb-6 text-white">
                voice Insights & Trends
              </h1>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                Stay ahead with the latest voice strategies, industry trends, and growth tactics from our experts
              </p>
            </div>
            <div className="fade-in-up" style={{animationDelay: '0.2s'}}>
              <BlogList
                onReadMore={handleBlogNavigation}
                category="voice"
                showSearch={true}
                showFilters={true}
                // full blog page: show all posts (no limit)
              />
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen premium-gradient-bg">
      {renderNavigation()}

      {/* Hero Section with Professional Background */}
      <section id="home" className="pt-32 pb-20 px-4 sm:px-6 lg:px-8 hero-gradient relative overflow-hidden">
        <div
          className="absolute inset-0 opacity-10"
          style={{
            backgroundImage: `url(https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80)`,
            backgroundSize: "cover",
            backgroundPosition: "center",
          }}
        ></div>
        <div className="max-w-7xl mx-auto text-center relative z-10">
          <div className="fade-in-up">
            <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight text-white">
              Scale Your Business with{' '}
              <span className="text-accent">
                70+ Domain Distribution
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 max-w-4xl mx-auto leading-relaxed">
              Complete voice automation across publishing, social media, video content,
              and digital advertising. Dominate every channel, every platform, every market.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button
                size="lg"
                onClick={() => document.getElementById("portfolio")?.scrollIntoView({ behavior: "smooth" })}
                className="bg-accent text-accent-foreground hover:bg-accent/90 font-semibold px-8 py-4 text-lg shadow-lg hover-lift pulse-glow"
              >
                <Eye className="mr-2 h-5 w-5" />
                See voice Work
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button
                size="lg"
                variant="outline"
                onClick={() => document.getElementById("contact")?.scrollIntoView({ behavior: "smooth" })}
                className="border-white/30 bg-white/10 text-white hover:bg-white hover:text-background px-8 py-4 text-lg shadow-sm hover-lift backdrop-blur-sm"
              >
                <Rocket className="mr-2 h-5 w-5" />
                Start My voice Project
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Services Grid */}
      <section id="services" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 fade-in-up">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">
              Complete voice Ecosystem
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Integrated voice services designed to maximize your reach, engagement, and conversions across all channels
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {voiceServices.map((service, index) => (
              <Card key={index} className="glass-effect hover-lift group">
                <CardHeader>
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-16 h-16 bg-accent/20 rounded-full flex items-center justify-center text-accent group-hover:bg-accent group-hover:text-accent-foreground transition-all">
                      {service.icon}
                    </div>
                    <Badge variant="secondary">
                      {service.domains}
                    </Badge>
                  </div>
                  <CardTitle className="text-xl text-white">{service.title}</CardTitle>
                  <div className="text-accent font-semibold">{service.price}</div>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground mb-6">{service.description}</p>
                  <ul className="space-y-3 mb-6">
                    {service.features.map((feature, i) => (
                      <li key={i} className="flex items-center gap-3 text-sm text-muted-foreground">
                        <Check className="w-4 h-4 text-accent flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <Button
                    className="w-full bg-accent text-accent-foreground hover:bg-accent/90 hover-lift"
                    onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
                  >
                    Get Started
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Choose Your Growth Plan Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 fade-in-up">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">
              Choose Your Growth Plan
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Scalable voice solutions that grow with your business
            </p>
          </div>

          <div className="grid gap-8 lg:grid-cols-3">
            {packages.map((pkg, index) => (
              <Card
                key={index}
                className={`glass-effect hover-lift relative ${pkg.popular ? 'border-accent shadow-xl scale-105' : ''}`}
              >
                {pkg.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-accent text-accent-foreground px-4">Most Popular</Badge>
                  </div>
                )}
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl text-white">{pkg.name}</CardTitle>
                  <div className="text-4xl font-bold text-accent">{pkg.price}</div>
                  <p className="text-muted-foreground">{pkg.description}</p>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3 mb-6">
                    {pkg.features.map((feature, i) => (
                      <li key={i} className="flex items-center gap-2">
                        <Check className="w-4 h-4 text-accent flex-shrink-0" />
                        <span className="text-sm text-muted-foreground">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Button
                    className={`w-full hover-lift ${pkg.popular ? 'bg-accent text-accent-foreground hover:bg-accent/90' : 'border-accent text-accent hover:bg-accent hover:text-accent-foreground'}`}
                    variant={pkg.popular ? "default" : "outline"}
                    onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
                  >
                    {pkg.cta}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Portfolio Section */}
      <section id="portfolio" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 fade-in-up">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">
              Proven Results That Speak Volumes
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Real campaigns, real results. See how we've transformed businesses across industries.
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {portfolioItems.map((item) => (
              <Card key={item.id} className="glass-effect hover-lift group overflow-hidden">
                <div className="relative">
                  <img
                    src={item.image}
                    alt={item.title}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute top-4 left-4">
                    <Badge className="bg-accent text-accent-foreground">
                      {item.results}
                    </Badge>
                  </div>
                  <div className="absolute top-4 right-4">
                    <Button size="sm" variant="secondary" className="opacity-0 group-hover:opacity-100 transition-opacity hover-lift">
                      <ExternalLink className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
                <CardContent className="p-6">
                  <Badge variant="secondary" className="mb-3">
                    {item.category}
                  </Badge>
                  <h3 className="text-xl font-bold text-white mb-2">{item.title}</h3>
                  <p className="text-muted-foreground mb-4">{item.description}</p>
                  <Button
                    variant="outline"
                    className="w-full border-accent text-accent hover:bg-accent hover:text-accent-foreground hover-lift"
                    onClick={() => window.open(item.link, '_blank')}
                  >
                    View Case Study
                    <ExternalLink className="w-4 h-4 ml-2" />
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 fade-in-up">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">
              What Our Clients Say
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Don't just take our word for it. Here's what industry leaders say about our results.
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {testimonials.map((testimonial) => (
              <Card key={testimonial.id} className="glass-effect hover-lift">
                <CardContent className="p-6">
                  <div className="flex items-center mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="w-5 h-5 text-accent fill-current" />
                    ))}
                  </div>
                  <Quote className="w-8 h-8 text-accent mb-4" />
                  <p className="text-muted-foreground mb-6 italic">"{testimonial.text}"</p>
                  <div className="flex items-center gap-4">
                    <img
                      src={testimonial.image}
                      alt={testimonial.name}
                      className="w-12 h-12 rounded-full object-cover"
                    />
                    <div>
                      <div className="font-semibold text-white">{testimonial.name}</div>
                      <div className="text-accent text-sm">{testimonial.role}</div>
                      <div className="text-muted-foreground text-sm">{testimonial.company}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Blog Preview Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 fade-in-up">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">
              voice Insights & Strategies
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Stay ahead with the latest voice trends, strategies, and growth tactics from our experts.
            </p>
          </div>

          <div className="mb-8">
            <BlogList
              onReadMore={handleBlogNavigation}
              category="voice"
              limit={6}
              showOnlyFeatured={true}
              showSearch={false}
              showFilters={false}
            />
          </div>

          <div className="text-center">
            <Button
              size="lg"
              variant="outline"
              className="border-accent text-accent hover:bg-accent hover:text-accent-foreground hover-lift"
              onClick={() => setCurrentView('blog')}
            >
              View All Articles
              <BookOpen className="w-5 h-5 ml-2" />
            </Button>
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section id="contact" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 fade-in-up">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">
              Ready to Scale Your Business?
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Get a free strategy consultation and see how our 70+ domain network can transform your business.
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <Card className="glass-effect">
              <CardHeader className="text-center">
                <CardTitle className="text-3xl text-white mb-4">Get Your Free Strategy Session</CardTitle>
                <p className="text-muted-foreground text-lg">
                  Fill out the form below and we'll get back to you within 24 hours with a custom voice strategy.
                </p>
              </CardHeader>
              <CardContent className="p-8">
                <form onSubmit={handleContactSubmit} className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="name" className="text-white font-medium">Full Name *</Label>
                      <Input
                        id="name"
                        value={contactForm.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        className="bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-accent"
                        placeholder="Your full name"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email" className="text-white font-medium">Email Address *</Label>
                      <Input
                        id="email"
                        type="email"
                        value={contactForm.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        className="bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-accent"
                        placeholder="<EMAIL>"
                        required
                      />
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="company" className="text-white font-medium">Company</Label>
                      <Input
                        id="company"
                        value={contactForm.company}
                        onChange={(e) => handleInputChange('company', e.target.value)}
                        className="bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-accent"
                        placeholder="Your company name"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="phone" className="text-white font-medium">Phone Number</Label>
                      <Input
                        id="phone"
                        value={contactForm.phone}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                        className="bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-accent"
                        placeholder="+****************"
                      />
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="service" className="text-white font-medium">Service Interest</Label>
                      <select
                        id="service"
                        value={contactForm.service}
                        onChange={(e) => handleInputChange('service', e.target.value)}
                        className="w-full p-3 bg-white/5 border border-white/20 rounded-md text-white focus:border-accent focus:outline-none"
                      >
                        <option value="">Select a service</option>
                        <option value="domain-distribution">Domain Distribution</option>
                        <option value="content-voice">Content voice</option>
                        <option value="social-media">Social Media voice</option>
                        <option value="video-voice">Video voice</option>
                        <option value="full-package">Complete Package</option>
                      </select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="budget" className="text-white font-medium">Monthly Budget</Label>
                      <select
                        id="budget"
                        value={contactForm.budget}
                        onChange={(e) => handleInputChange('budget', e.target.value)}
                        className="w-full p-3 bg-white/5 border border-white/20 rounded-md text-white focus:border-accent focus:outline-none"
                      >
                        <option value="">Select budget range</option>
                        <option value="1000-5000">$1,000 - $5,000</option>
                        <option value="5000-10000">$5,000 - $10,000</option>
                        <option value="10000-25000">$10,000 - $25,000</option>
                        <option value="25000+">$25,000+</option>
                      </select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="message" className="text-white font-medium">Message *</Label>
                    <Textarea
                      id="message"
                      value={contactForm.message}
                      onChange={(e) => handleInputChange('message', e.target.value)}
                      className="bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-accent min-h-[120px]"
                      placeholder="Tell us about your business goals and how we can help..."
                      required
                    />
                  </div>

                  <Button
                    type="submit"
                    className="w-full bg-accent text-accent-foreground hover:bg-accent/90 text-lg py-4 font-semibold hover-lift pulse-glow"
                  >
                    Get My Free Strategy Session
                    <Rocket className="w-5 h-5 ml-2" />
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-16">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid gap-12 lg:grid-cols-2 items-center">
            <div>
              <h2 className="text-3xl font-bold mb-6">Why Choose EasyLabs voice?</h2>
              <div className="space-y-6">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center text-primary">
                    <Globe className="w-6 h-6" />
                  </div>
                  <div>
                    <h3 className="font-semibold mb-2">Massive Distribution Network</h3>
                    <p className="text-muted-foreground">70+ domain distribution ensures your content reaches every corner of the internet.</p>
                  </div>
                </div>
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center text-primary">
                    <Zap className="w-6 h-6" />
                  </div>
                  <div>
                    <h3 className="font-semibold mb-2">Automated Excellence</h3>
                    <p className="text-muted-foreground">AI-powered campaign optimization running 24/7 to maximize your results.</p>
                  </div>
                </div>
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center text-primary">
                    <BarChart3 className="w-6 h-6" />
                  </div>
                  <div>
                    <h3 className="font-semibold mb-2">Data-Driven Results</h3>
                    <p className="text-muted-foreground">Every decision backed by real-time analytics and performance data.</p>
                  </div>
                </div>
              </div>
            </div>
            <div className="space-y-4">
              <Card className="p-6">
                <div className="flex items-center gap-4">
                  <Megaphone className="w-8 h-8 text-primary" />
                  <div>
                    <h3 className="font-semibold">Omnichannel Presence</h3>
                    <p className="text-sm text-muted-foreground">Be everywhere your customers are</p>
                  </div>
                </div>
              </Card>
              <Card className="p-6">
                <div className="flex items-center gap-4">
                  <MonitorPlay className="w-8 h-8 text-primary" />
                  <div>
                    <h3 className="font-semibold">Video Content Mastery</h3>
                    <p className="text-sm text-muted-foreground">Engaging videos that convert</p>
                  </div>
                </div>
              </Card>
              <Card className="p-6">
                <div className="flex items-center gap-4">
                  <Database className="w-8 h-8 text-primary" />
                  <div>
                    <h3 className="font-semibold">Advanced Analytics</h3>
                    <p className="text-sm text-muted-foreground">Deep insights for better decisions</p>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
  <section id="contact-cta" className="py-16 bg-gradient-to-r from-primary/10 to-secondary/10">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">Ready to Dominate Your Market?</h2>
          <p className="text-xl text-muted-foreground mb-8">
            Join 500+ businesses that have transformed their growth with our 70+ domain distribution network
          </p>
          <div className="flex gap-4 justify-center mb-8">
            <Button size="lg" className="text-lg px-8">
              <Mail className="w-5 h-5 mr-2" />
              Start Dominating Today
            </Button>
            <Button size="lg" variant="outline" className="text-lg px-8">
              <Phone className="w-5 h-5 mr-2" />
              Schedule Strategy Call
            </Button>
          </div>
          <p className="text-muted-foreground">
            Free consultation • Custom strategy • 300% average ROI increase
          </p>
        </div>
      </section>
    </div>
  );
};

export default VoiceServiceApp;