import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowRight } from 'lucide-react';
import { blogStorage } from '@/utils/blogStorage';
import BlogCard from '@/apps/marketing-service/components/blog/BlogCard';

interface BlogPreviewProps {
  maxPosts?: number;
  onReadMore?: (slug: string) => void;
}

const BlogPreview: React.FC<BlogPreviewProps> = ({ maxPosts = 3, onReadMore }) => {
  const [latestPosts, setLatestPosts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchLatestPosts();
  }, []);

  const fetchLatestPosts = async () => {
    try {
      const posts = await blogStorage.getPosts();
      const publishedPosts = posts
        .filter(post => post.published !== false)
        .sort((a, b) => new Date(b.created_at).getTime() - 
                        new Date(a.created_at).getTime())
        .slice(0, maxPosts);
      
      setLatestPosts(publishedPosts);
    } catch (error) {
      console.error('Error fetching latest posts:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="py-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(3)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <div className="h-48 bg-muted rounded-t-lg" />
                <CardContent className="p-4">
                  <div className="h-6 bg-muted rounded w-3/4 mb-4" />
                  <div className="h-4 bg-muted rounded w-1/2" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="py-16 bg-background/50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">Latest Marketing Insights</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Stay ahead with the latest marketing trends, strategies, and growth tactics from our experts.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {latestPosts.map((post, index) => (
            <BlogCard
              key={post.id}
              post={post}
              onReadMore={onReadMore}
              variant={index === 0 ? 'featured' : 'default'}
            />
          ))}
        </div>

        <div className="text-center">
          <Button
            variant="outline"
            size="lg"
            onClick={() => window.location.href = '/blog'}
            className="hover:bg-accent hover:text-accent-foreground"
          >
            View All Articles
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default BlogPreview;