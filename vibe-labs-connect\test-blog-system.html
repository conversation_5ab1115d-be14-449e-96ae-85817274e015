<!DOCTYPE html>
<html>
<head>
    <title>Blog System Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .result { margin: 10px 0; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff; }
        .success { background-color: #d4edda; border-left-color: #28a745; }
        .error { background-color: #f8d7da; border-left-color: #dc3545; }
        .warning { background-color: #fff3cd; border-left-color: #ffc107; }
        button { margin: 5px; padding: 12px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 14px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; border: 1px solid #e9ecef; }
        .post-card { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border: 1px solid #e9ecef; }
        .post-title { font-weight: bold; color: #007bff; margin-bottom: 5px; }
        .post-meta { color: #6c757d; font-size: 12px; margin-bottom: 10px; }
        .post-excerpt { color: #495057; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Blog System Test Suite</h1>
        <p>This tool tests the blog functionality to ensure everything works correctly.</p>
        
        <div>
            <button class="btn-primary" onclick="testBlogStorage()">Test Blog Storage</button>
            <button class="btn-success" onclick="initializeSamplePosts()">Initialize Sample Posts</button>
            <button class="btn-warning" onclick="testBlogRetrieval()">Test Blog Retrieval</button>
            <button class="btn-danger" onclick="clearAllData()">Clear All Data</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        function addResult(message, type = 'result') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        function testBlogStorage() {
            clearResults();
            addResult('🔄 Testing blog storage system...', 'result');
            
            try {
                // Check localStorage
                const stored = localStorage.getItem('blog_posts');
                if (stored) {
                    const posts = JSON.parse(stored);
                    addResult(`✅ Found ${posts.length} blog posts in localStorage`, 'success');
                    
                    // Show post details
                    posts.slice(0, 3).forEach((post, index) => {
                        addResult(`
                            <div class="post-card">
                                <div class="post-title">${post.title}</div>
                                <div class="post-meta">Category: ${post.category} | Slug: ${post.slug}</div>
                                <div class="post-excerpt">${post.excerpt}</div>
                            </div>
                        `, 'result');
                    });
                } else {
                    addResult('❌ No blog posts found in localStorage', 'error');
                }
                
                // Test categories
                const categories = [...new Set(JSON.parse(stored || '[]').map(p => p.category))];
                addResult(`📂 Available categories: ${categories.join(', ')}`, 'result');
                
            } catch (error) {
                addResult(`❌ Error testing blog storage: ${error.message}`, 'error');
            }
        }
        
        function initializeSamplePosts() {
            addResult('🔄 Initializing sample blog posts...', 'result');
            
            const samplePosts = [
                {
                    id: 'sample-1',
                    title: 'Welcome to Our Marketing Blog',
                    content: '<h2>Welcome to the Future of Marketing</h2><p>We are excited to share insights, strategies, and trends that will help your business grow.</p>',
                    excerpt: 'Welcome to our marketing blog where we share expert insights, strategies, and trends to help your business grow.',
                    category: 'Marketing',
                    tags: ['welcome', 'marketing', 'blog', 'strategy'],
                    featured_image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=400&fit=crop',
                    is_featured: true,
                    published: true,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString(),
                    slug: 'welcome-to-our-marketing-blog',
                    view_count: 0,
                    seo_title: 'Welcome to Our Marketing Blog - Expert Insights & Strategies',
                    seo_description: 'Discover expert marketing insights, strategies, and trends to grow your business.'
                },
                {
                    id: 'sample-2',
                    title: 'Digital Marketing Trends for 2024',
                    content: '<h2>The Top Digital Marketing Trends Shaping 2024</h2><p>As we navigate through 2024, the digital marketing landscape continues to evolve.</p>',
                    excerpt: 'Discover the top digital marketing trends for 2024, including AI-powered personalization, video-first content, and voice search optimization.',
                    category: 'Marketing',
                    tags: ['digital marketing', 'trends', '2024', 'AI', 'video content'],
                    featured_image: 'https://images.unsplash.com/photo-1432888622747-4eb9a8efeb07?w=800&h=400&fit=crop',
                    is_featured: true,
                    published: true,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString(),
                    slug: 'digital-marketing-trends-for-2024',
                    view_count: 0,
                    seo_title: 'Digital Marketing Trends for 2024 - Stay Ahead of the Curve',
                    seo_description: 'Explore the top digital marketing trends for 2024 including AI personalization and video content strategies.'
                },
                {
                    id: 'sample-3',
                    title: 'Real Estate Marketing Strategies That Work',
                    content: '<h2>Effective Real Estate Marketing in the Digital Age</h2><p>The real estate industry has transformed dramatically with digital marketing.</p>',
                    excerpt: 'Discover proven real estate marketing strategies including virtual tours, social media marketing, and local SEO optimization.',
                    category: 'Real Estate',
                    tags: ['real estate', 'marketing', 'virtual tours', 'social media', 'SEO'],
                    featured_image: 'https://images.unsplash.com/photo-1560518883-ce09059eeffa?w=800&h=400&fit=crop',
                    is_featured: true,
                    published: true,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString(),
                    slug: 'real-estate-marketing-strategies-that-work',
                    view_count: 0,
                    seo_title: 'Real Estate Marketing Strategies That Work - Digital Success',
                    seo_description: 'Learn effective real estate marketing strategies including virtual tours, social media, and local SEO.'
                }
            ];
            
            try {
                localStorage.setItem('blog_posts', JSON.stringify(samplePosts));
                window.dispatchEvent(new Event('blog_posts_updated'));
                addResult(`✅ Successfully initialized ${samplePosts.length} sample blog posts`, 'success');
                addResult('🔄 You can now test the blog retrieval functionality', 'result');
            } catch (error) {
                addResult(`❌ Error initializing sample posts: ${error.message}`, 'error');
            }
        }
        
        function testBlogRetrieval() {
            addResult('🔄 Testing blog retrieval functionality...', 'result');
            
            try {
                const stored = localStorage.getItem('blog_posts');
                if (!stored) {
                    addResult('❌ No posts found. Please initialize sample posts first.', 'error');
                    return;
                }
                
                const posts = JSON.parse(stored);
                
                // Test filtering by category
                const marketingPosts = posts.filter(p => p.category === 'Marketing');
                const realEstatePosts = posts.filter(p => p.category === 'Real Estate');
                
                addResult(`📊 Marketing posts: ${marketingPosts.length}`, 'success');
                addResult(`🏠 Real Estate posts: ${realEstatePosts.length}`, 'success');
                
                // Test featured posts
                const featuredPosts = posts.filter(p => p.is_featured);
                addResult(`⭐ Featured posts: ${featuredPosts.length}`, 'success');
                
                // Test slug lookup
                const testSlug = 'welcome-to-our-marketing-blog';
                const foundPost = posts.find(p => p.slug === testSlug);
                if (foundPost) {
                    addResult(`🔍 Successfully found post by slug: "${foundPost.title}"`, 'success');
                } else {
                    addResult(`❌ Could not find post with slug: ${testSlug}`, 'error');
                }
                
                addResult('✅ All blog retrieval tests passed!', 'success');
                
            } catch (error) {
                addResult(`❌ Error testing blog retrieval: ${error.message}`, 'error');
            }
        }
        
        function clearAllData() {
            if (confirm('Are you sure you want to clear all blog data? This cannot be undone.')) {
                localStorage.removeItem('blog_posts');
                window.dispatchEvent(new Event('blog_posts_updated'));
                addResult('🗑️ All blog data cleared', 'warning');
            }
        }
        
        // Auto-run initial test
        window.onload = function() {
            addResult('🚀 Blog System Test Suite Ready', 'success');
            addResult('Click "Test Blog Storage" to check current state, or "Initialize Sample Posts" to create test data.', 'result');
        };
    </script>
</body>
</html>
