import { useNavigate } from 'react-router-dom';
import { BlogList } from '@/components/blog/BlogList';

export default function Blog() {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4">Blog</h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Stay updated with our latest insights, news, and stories
          </p>
        </div>
        <BlogList 
          onReadMore={(slug) => navigate(`/blog/${slug}`)}
          showSearch={true}
          showFilters={true}
          variant="grid"
        />
      </div>
    </div>
  );
}