/**
 * Pretty Links Service - Affiliate Link Cloaking and Tracking System
 * Provides clean, branded URLs for affiliate links with comprehensive tracking
 */

import { analytics } from './analytics';

export interface PrettyLink {
  id: string;
  slug: string; // The clean URL part (e.g., 'go/product-name')
  destination: string; // The actual affiliate URL
  title: string;
  description?: string;
  category: string;
  tags: string[];
  clicks: number;
  created_at: string;
  updated_at: string;
  active: boolean;
  expires_at?: string;
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
  utm_content?: string;
}

export interface LinkClick {
  id: string;
  link_id: string;
  timestamp: string;
  user_agent: string;
  referrer: string;
  ip_address?: string;
  country?: string;
  device_type: 'desktop' | 'mobile' | 'tablet';
}

// In-memory storage for development (replace with database in production)
let prettyLinksStorage: PrettyLink[] = [];
let linkClicksStorage: LinkClick[] = [];

/**
 * Initialize with sample affiliate links
 */
const initializeSampleLinks = (): void => {
  if (prettyLinksStorage.length === 0) {
    prettyLinksStorage = [
      {
        id: 'link-1',
        slug: 'go/canva-pro',
        destination: 'https://www.canva.com/p/pro/?utm_source=vibelabs&utm_medium=affiliate&utm_campaign=design_tools',
        title: 'Canva Pro - Professional Design Tool',
        description: 'Create stunning designs with Canva Pro\'s premium features',
        category: 'Design Tools',
        tags: ['design', 'graphics', 'marketing', 'canva'],
        clicks: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        active: true,
        utm_source: 'vibelabs',
        utm_medium: 'affiliate',
        utm_campaign: 'design_tools'
      },
      {
        id: 'link-2',
        slug: 'go/shopify-trial',
        destination: 'https://www.shopify.com/free-trial?utm_source=vibelabs&utm_medium=affiliate&utm_campaign=ecommerce',
        title: 'Shopify Free Trial - Start Your Online Store',
        description: 'Build your e-commerce store with Shopify\'s 14-day free trial',
        category: 'E-commerce',
        tags: ['ecommerce', 'shopify', 'online store', 'business'],
        clicks: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        active: true,
        utm_source: 'vibelabs',
        utm_medium: 'affiliate',
        utm_campaign: 'ecommerce'
      },
      {
        id: 'link-3',
        slug: 'go/hostinger-hosting',
        destination: 'https://www.hostinger.com/web-hosting?utm_source=vibelabs&utm_medium=affiliate&utm_campaign=hosting',
        title: 'Hostinger Web Hosting - Fast & Reliable',
        description: 'Get premium web hosting with Hostinger at affordable prices',
        category: 'Web Hosting',
        tags: ['hosting', 'website', 'domain', 'server'],
        clicks: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        active: true,
        utm_source: 'vibelabs',
        utm_medium: 'affiliate',
        utm_campaign: 'hosting'
      }
    ];
    console.log('✅ Pretty Links: Sample links initialized');
  }
};

/**
 * Get all pretty links
 */
export const getAllLinks = (): PrettyLink[] => {
  initializeSampleLinks();
  return prettyLinksStorage.filter(link => link.active);
};

/**
 * Get link by slug
 */
export const getLinkBySlug = (slug: string): PrettyLink | null => {
  initializeSampleLinks();
  return prettyLinksStorage.find(link => link.slug === slug && link.active) || null;
};

/**
 * Create a new pretty link
 */
export const createLink = (linkData: Omit<PrettyLink, 'id' | 'clicks' | 'created_at' | 'updated_at'>): PrettyLink => {
  const newLink: PrettyLink = {
    ...linkData,
    id: `link-${Date.now()}`,
    clicks: 0,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
  
  prettyLinksStorage.push(newLink);
  console.log('✅ Pretty Links: New link created:', newLink.slug);
  return newLink;
};

/**
 * Update an existing link
 */
export const updateLink = (id: string, updates: Partial<PrettyLink>): PrettyLink | null => {
  const linkIndex = prettyLinksStorage.findIndex(link => link.id === id);
  if (linkIndex === -1) return null;
  
  prettyLinksStorage[linkIndex] = {
    ...prettyLinksStorage[linkIndex],
    ...updates,
    updated_at: new Date().toISOString()
  };
  
  console.log('✅ Pretty Links: Link updated:', prettyLinksStorage[linkIndex].slug);
  return prettyLinksStorage[linkIndex];
};

/**
 * Delete a link (soft delete by setting active to false)
 */
export const deleteLink = (id: string): boolean => {
  const linkIndex = prettyLinksStorage.findIndex(link => link.id === id);
  if (linkIndex === -1) return false;
  
  prettyLinksStorage[linkIndex].active = false;
  prettyLinksStorage[linkIndex].updated_at = new Date().toISOString();
  
  console.log('✅ Pretty Links: Link deleted:', prettyLinksStorage[linkIndex].slug);
  return true;
};

/**
 * Track a link click and redirect
 */
export const trackAndRedirect = (slug: string): boolean => {
  const link = getLinkBySlug(slug);
  if (!link) {
    console.error('❌ Pretty Links: Link not found:', slug);
    return false;
  }

  // Check if link has expired
  if (link.expires_at && new Date(link.expires_at) < new Date()) {
    console.error('❌ Pretty Links: Link expired:', slug);
    return false;
  }

  // Track the click
  const clickData: LinkClick = {
    id: `click-${Date.now()}`,
    link_id: link.id,
    timestamp: new Date().toISOString(),
    user_agent: navigator.userAgent,
    referrer: document.referrer,
    device_type: getDeviceType()
  };
  
  linkClicksStorage.push(clickData);
  
  // Update click count
  const linkIndex = prettyLinksStorage.findIndex(l => l.id === link.id);
  if (linkIndex !== -1) {
    prettyLinksStorage[linkIndex].clicks++;
    prettyLinksStorage[linkIndex].updated_at = new Date().toISOString();
  }

  // Track with analytics
  analytics.trackAffiliateClick(link.destination, link.title, slug);

  // Redirect to destination
  window.open(link.destination, '_blank');
  
  console.log('✅ Pretty Links: Click tracked and redirected:', slug);
  return true;
};

/**
 * Get device type from user agent
 */
const getDeviceType = (): 'desktop' | 'mobile' | 'tablet' => {
  const userAgent = navigator.userAgent.toLowerCase();
  
  if (/tablet|ipad|playbook|silk/.test(userAgent)) {
    return 'tablet';
  }
  
  if (/mobile|iphone|ipod|android|blackberry|opera|mini|windows\sce|palm|smartphone|iemobile/.test(userAgent)) {
    return 'mobile';
  }
  
  return 'desktop';
};

/**
 * Get click statistics for a link
 */
export const getLinkStats = (linkId: string): {
  totalClicks: number;
  clicksByDay: Record<string, number>;
  clicksByDevice: Record<string, number>;
  recentClicks: LinkClick[];
} => {
  const clicks = linkClicksStorage.filter(click => click.link_id === linkId);
  
  const clicksByDay: Record<string, number> = {};
  const clicksByDevice: Record<string, number> = {};
  
  clicks.forEach(click => {
    const day = click.timestamp.split('T')[0];
    clicksByDay[day] = (clicksByDay[day] || 0) + 1;
    clicksByDevice[click.device_type] = (clicksByDevice[click.device_type] || 0) + 1;
  });
  
  return {
    totalClicks: clicks.length,
    clicksByDay,
    clicksByDevice,
    recentClicks: clicks.slice(-10).reverse() // Last 10 clicks
  };
};

/**
 * Get links by category
 */
export const getLinksByCategory = (category: string): PrettyLink[] => {
  return getAllLinks().filter(link => link.category === category);
};

/**
 * Search links by title or tags
 */
export const searchLinks = (query: string): PrettyLink[] => {
  const searchTerm = query.toLowerCase();
  return getAllLinks().filter(link => 
    link.title.toLowerCase().includes(searchTerm) ||
    link.description?.toLowerCase().includes(searchTerm) ||
    link.tags.some(tag => tag.toLowerCase().includes(searchTerm))
  );
};

/**
 * Generate a clean slug from title
 */
export const generateSlug = (title: string, prefix: string = 'go'): string => {
  const cleanTitle = title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();

  return `${prefix}/${cleanTitle}`;
};

/**
 * Handle pretty link routing (call this in your router)
 */
export const handlePrettyLinkRoute = (slug: string): boolean => {
  // Remove leading slash if present
  const cleanSlug = slug.startsWith('/') ? slug.substring(1) : slug;

  // Check if this is a pretty link (starts with 'go/')
  if (cleanSlug.startsWith('go/')) {
    return trackAndRedirect(cleanSlug);
  }

  return false;
};

// Export the pretty links service
export const prettyLinks = {
  getAllLinks,
  getLinkBySlug,
  createLink,
  updateLink,
  deleteLink,
  trackAndRedirect,
  getLinkStats,
  getLinksByCategory,
  searchLinks,
  generateSlug,
  handlePrettyLinkRoute
};

export default prettyLinks;
