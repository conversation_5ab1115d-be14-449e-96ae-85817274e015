<!DOCTYPE html>
<html>
<head>
    <title>Test Blog Functionality</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        button { margin: 5px; padding: 10px; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Blog Functionality Test</h1>
    
    <div>
        <button onclick="testLocalStorage()">Test Local Storage</button>
        <button onclick="testBlogNavigation()">Test Blog Navigation</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>
    
    <div id="results"></div>

    <script>
        function addResult(message, isError = false) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${isError ? 'error' : 'success'}`;
            div.innerHTML = message;
            results.appendChild(div);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        function testLocalStorage() {
            try {
                addResult('🔄 Testing localStorage blog functionality...');
                
                // Check if blog posts exist in localStorage
                const stored = localStorage.getItem('blog_posts');
                if (stored) {
                    const posts = JSON.parse(stored);
                    addResult(`✅ Found ${posts.length} blog posts in localStorage`);
                    
                    // Show first few posts
                    posts.slice(0, 3).forEach((post, index) => {
                        addResult(`📝 Post ${index + 1}: "${post.title}" (${post.category})`);
                    });
                } else {
                    addResult('❌ No blog posts found in localStorage', true);
                }
            } catch (error) {
                addResult(`❌ Error testing localStorage: ${error.message}`, true);
            }
        }
        
        function testBlogNavigation() {
            try {
                addResult('🔄 Testing blog navigation...');
                
                // Test navigation to marketing service blog
                const marketingUrl = '/marketing-service#blog';
                addResult(`✅ Marketing blog URL: ${marketingUrl}`);
                
                // Test navigation to specific blog post
                const postUrl = '/marketing-service#blog-post-welcome-to-our-marketing-blog';
                addResult(`✅ Blog post URL: ${postUrl}`);
                
                // Test opening in new tab
                addResult('✅ Blog navigation URLs generated successfully');
                
                // Add buttons to actually test navigation
                const navDiv = document.createElement('div');
                navDiv.innerHTML = `
                    <button onclick="window.open('${marketingUrl}', '_blank')">Open Marketing Blog</button>
                    <button onclick="window.open('${postUrl}', '_blank')">Open Sample Post</button>
                `;
                document.getElementById('results').appendChild(navDiv);
                
            } catch (error) {
                addResult(`❌ Error testing navigation: ${error.message}`, true);
            }
        }
    </script>
</body>
</html>
