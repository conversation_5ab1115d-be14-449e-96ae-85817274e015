<!DOCTYPE html>
<html>
<head>
    <title>Test Blog Supabase Connection</title>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        button { margin: 5px; padding: 10px; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Blog Supabase Connection Test</h1>
    
    <div>
        <button onclick="testConnection()">Test Connection</button>
        <button onclick="testCreatePost()">Test Create Post</button>
        <button onclick="testGetPosts()">Test Get Posts</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>
    
    <div id="results"></div>

    <script>
        const supabaseUrl = 'https://pujwroltuqzfqnozymif.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB1andyb2x0dXF6ZnFub3p5bWlmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTYxMDk2NzUsImV4cCI6MjA3MTY4NTY3NX0.GJS-nX8N4HBIB_7JI93XNW_yUIA_bZF6LYjCiUwgaIU';
        
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);
        
        function addResult(message, isError = false) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${isError ? 'error' : 'success'}`;
            div.innerHTML = message;
            results.appendChild(div);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        async function testConnection() {
            try {
                addResult('🔄 Testing Supabase connection...');
                
                // Test basic connection
                const { data, error } = await supabase.from('blog_posts').select('count', { count: 'exact' });
                
                if (error) {
                    addResult(`❌ Connection failed: ${error.message}`, true);
                    console.error('Connection error:', error);
                } else {
                    addResult(`✅ Connection successful! Found ${data.length} posts in database`);
                }
            } catch (error) {
                addResult(`❌ Connection error: ${error.message}`, true);
                console.error('Connection error:', error);
            }
        }
        
        async function testCreatePost() {
            try {
                addResult('🔄 Testing post creation...');
                
                const testPost = {
                    title: 'Test Post ' + Date.now(),
                    content: '<p>This is a test post created from the test script.</p>',
                    excerpt: 'A test post to verify Supabase functionality.',
                    category: 'Test',
                    tags: ['test', 'supabase'],
                    featured_image: 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=800&h=400&fit=crop',
                    is_featured: false,
                    published: true,
                    view_count: 0,
                    seo_title: 'Test Post',
                    seo_description: 'A test post for Supabase'
                };
                
                // Generate slug
                const slug = testPost.title.toLowerCase().replace(/[^a-z0-9\s]/g, '').replace(/\s+/g, '-');
                testPost.slug = slug;
                
                const { data, error } = await supabase
                    .from('blog_posts')
                    .insert([testPost])
                    .select()
                    .single();
                
                if (error) {
                    addResult(`❌ Post creation failed: ${error.message}`, true);
                    console.error('Create error:', error);
                } else {
                    addResult(`✅ Post created successfully!`);
                    addResult(`<pre>${JSON.stringify(data, null, 2)}</pre>`);
                }
            } catch (error) {
                addResult(`❌ Post creation error: ${error.message}`, true);
                console.error('Create error:', error);
            }
        }
        
        async function testGetPosts() {
            try {
                addResult('🔄 Testing post retrieval...');
                
                const { data, error } = await supabase
                    .from('blog_posts')
                    .select('*')
                    .order('created_at', { ascending: false })
                    .limit(5);
                
                if (error) {
                    addResult(`❌ Post retrieval failed: ${error.message}`, true);
                    console.error('Get error:', error);
                } else {
                    addResult(`✅ Retrieved ${data.length} posts successfully!`);
                    data.forEach((post, index) => {
                        addResult(`<strong>Post ${index + 1}:</strong> ${post.title} (${post.category})`);
                    });
                }
            } catch (error) {
                addResult(`❌ Post retrieval error: ${error.message}`, true);
                console.error('Get error:', error);
            }
        }
    </script>
</body>
</html>
