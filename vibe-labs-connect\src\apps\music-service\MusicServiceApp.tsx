import React, { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Music, 
  Play, 
  Pause, 
  Headphones, 
  Mic, 
  Palette, 
  Globe, 
  TrendingUp,
  Calendar,
  Star,
  User,
  Mail,
  Phone,
  ArrowRight,
  BookOpen
} from 'lucide-react';
import { BlogList } from '@/components/blog/BlogList';
import { BlogPost } from '@/components/blog/BlogPost';
import { BlogAdmin } from '@/components/blog/BlogAdmin';

const MusicServiceApp: React.FC = () => {
  const [currentView, setCurrentView] = useState<'home' | 'blog' | 'blog-post' | 'admin'>('home');
  const [currentBlogSlug, setCurrentBlogSlug] = useState<string>('');
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  const [currentTrack, setCurrentTrack] = useState<number | null>(null);

  // Sample beats for the player
  const sampleBeats = [
    { id: 1, title: "Trap Vibes", genre: "Trap", bpm: 140, price: "$29", preview: "/beats/trap-vibes.mp3" },
    { id: 2, title: "Melodic Dreams", genre: "R&B", bpm: 85, price: "$35", preview: "/beats/melodic-dreams.mp3" },
    { id: 3, title: "Hard Drill", genre: "Drill", bpm: 150, price: "$25", preview: "/beats/hard-drill.mp3" },
    { id: 4, title: "Smooth Jazz", genre: "Jazz", bpm: 120, price: "$40", preview: "/beats/smooth-jazz.mp3" },
  ];

  const services = [
    {
      icon: <Music className="w-8 h-8" />,
      title: "Beat Production",
      description: "Custom beats tailored to your style - from trap to jazz, drill to R&B.",
      features: ["Original compositions", "Unlimited revisions", "Stems included", "Commercial license"],
      price: "From $29"
    },
    {
      icon: <Headphones className="w-8 h-8" />,
      title: "Mixing & Mastering",
      description: "Professional audio engineering to make your tracks radio-ready.",
      features: ["Professional mixing", "Mastering for all platforms", "Stereo & mono versions", "24-48hr turnaround"],
      price: "From $45"
    },
    {
      icon: <Palette className="w-8 h-8" />,
      title: "Cover Art & Branding",
      description: "Eye-catching artwork and complete visual identity for your music brand.",
      features: ["Album/single covers", "Logo design", "Social media kit", "Brand guidelines"],
      price: "From $25"
    },
    {
      icon: <Globe className="w-8 h-8" />,
      title: "Distribution & Marketing",
      description: "Get your music on all major platforms and grow your fanbase.",
      features: ["Spotify, Apple Music, etc.", "Playlist pitching", "Social media promotion", "Performance analytics"],
      price: "From $60"
    }
  ];

  const packages = [
    {
      name: "Starter Artist",
      price: "$99",
      description: "Perfect for new artists getting started",
      features: [
        "1 Custom Beat",
        "Basic Mixing",
        "Cover Art Design",
        "Social Media Kit"
      ],
      popular: false
    },
    {
      name: "Rising Star",
      price: "$249",
      description: "Everything you need to grow your fanbase",
      features: [
        "3 Custom Beats",
        "Professional Mixing & Mastering",
        "Complete Branding Package",
        "Distribution Setup",
        "2 Social Media Campaigns"
      ],
      popular: true
    },
    {
      name: "Pro Artist",
      price: "$499",
      description: "Complete artist development solution",
      features: [
        "5 Custom Beats",
        "Premium Mixing & Mastering",
        "Full Branding & Website",
        "Distribution & Marketing",
        "Playlist Pitching",
        "Monthly Performance Reports"
      ],
      popular: false
    }
  ];

  const playBeat = (beatId: number) => {
    if (currentTrack === beatId && isPlaying) {
      setIsPlaying(false);
    } else {
      setCurrentTrack(beatId);
      setIsPlaying(true);
      // In a real app, you'd implement actual audio playback here
      setTimeout(() => setIsPlaying(false), 30000); // Auto-stop after 30s preview
    }
  };

  const handleBlogNavigation = (slug: string) => {
    setCurrentBlogSlug(slug);
    setCurrentView('blog-post');
  };

  const renderNavigation = () => (
    <nav className="bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-0 z-50 border-b">
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center gap-2">
            <Music className="w-8 h-8 text-primary" />
            <span className="text-xl font-bold">EasyLabs Music</span>
          </div>
          <div className="hidden md:flex items-center space-x-8">
            <button 
              onClick={() => setCurrentView('home')}
              className={`hover:text-primary transition-colors ${currentView === 'home' ? 'text-primary font-medium' : ''}`}
            >
              Home
            </button>
            <a href="#about" className="hover:text-primary transition-colors">About</a>
            <a href="#services" className="hover:text-primary transition-colors">Services</a>
            <a href="#portfolio" className="hover:text-primary transition-colors">Portfolio</a>
            <a href="#testimonials" className="hover:text-primary transition-colors">Testimonials</a>
            <a href="#contact" className="hover:text-primary transition-colors">Contact</a>
            <button 
              onClick={() => setCurrentView('blog')}
              className={`hover:text-primary transition-colors flex items-center gap-1 ${currentView === 'blog' ? 'text-primary font-medium' : ''}`}
            >
              <BookOpen className="w-4 h-4" />
              Blog
            </button>
          </div>
          <Button onClick={() => setCurrentView('admin')} variant="outline">
            Admin
          </Button>
        </div>
      </div>
    </nav>
  );

  if (currentView === 'admin') {
    return (
      <div className="min-h-screen bg-background">
        {renderNavigation()}
        <BlogAdmin />
      </div>
    );
  }

  if (currentView === 'blog-post') {
    return (
      <div className="min-h-screen bg-background">
        {renderNavigation()}
        <BlogPost 
          slug={currentBlogSlug} 
          onBack={() => setCurrentView('blog')} 
        />
      </div>
    );
  }

  if (currentView === 'blog') {
    return (
      <div className="min-h-screen bg-background">
        {renderNavigation()}
        <div className="max-w-7xl mx-auto px-4 py-8">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold mb-4">Music Industry Insights</h1>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Stay updated with the latest trends, tips, and insights from the music industry
            </p>
          </div>
          <BlogList 
            onReadMore={handleBlogNavigation}
            category="Music"
          />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {renderNavigation()}
      
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-primary/10 via-background to-secondary/10 py-20">
        <div className="max-w-7xl mx-auto px-4 text-center">
          <h1 className="text-5xl font-bold mb-6">
            Everything an Artist Needs to Grow —{' '}
            <span className="text-primary">Beats, Branding & Beyond</span>
          </h1>
          <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto">
            From production to promotion, EasyLabs Music helps you sound professional, 
            look professional, and get discovered.
          </p>
          <div className="flex gap-4 justify-center">
            <Button size="lg" className="text-lg px-8">
              Get Started
              <ArrowRight className="w-5 h-5 ml-2" />
            </Button>
            <Button size="lg" variant="outline" className="text-lg px-8">
              Listen to Beats
              <Music className="w-5 h-5 ml-2" />
            </Button>
          </div>
        </div>
      </section>

      {/* Beat Player Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Featured Beats</h2>
            <p className="text-muted-foreground">
              Preview our latest productions. All beats come with stems, unlimited revisions, and commercial licenses.
            </p>
          </div>
          
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            {sampleBeats.map((beat) => (
              <Card key={beat.id} className="group hover:shadow-lg transition-all duration-300">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <Badge variant="secondary">{beat.genre}</Badge>
                    <span className="text-sm text-muted-foreground">{beat.bpm} BPM</span>
                  </div>
                  <CardTitle className="text-lg">{beat.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <span className="font-semibold text-primary">{beat.price}</span>
                    <Button
                      size="sm"
                      variant={currentTrack === beat.id && isPlaying ? "secondary" : "outline"}
                      onClick={() => playBeat(beat.id)}
                    >
                      {currentTrack === beat.id && isPlaying ? (
                        <Pause className="w-4 h-4" />
                      ) : (
                        <Play className="w-4 h-4" />
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section id="services" className="py-16 bg-muted/50">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Our Services</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Professional music services designed to take your artistry to the next level
            </p>
          </div>
          
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
            {services.map((service, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center text-primary mb-4">
                    {service.icon}
                  </div>
                  <CardTitle>{service.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground mb-4">{service.description}</p>
                  <ul className="text-sm space-y-1 mb-4">
                    {service.features.map((feature, i) => (
                      <li key={i} className="flex items-center justify-center gap-2">
                        <Star className="w-3 h-3 text-primary" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <div className="font-semibold text-primary">{service.price}</div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Packages Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Artist Packages</h2>
            <p className="text-muted-foreground">
              Choose the perfect package for your music journey
            </p>
          </div>
          
          <div className="grid gap-8 md:grid-cols-3">
            {packages.map((pkg, index) => (
              <Card 
                key={index} 
                className={`relative ${pkg.popular ? 'border-primary shadow-lg scale-105' : ''}`}
              >
                {pkg.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-primary text-primary-foreground">Most Popular</Badge>
                  </div>
                )}
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl">{pkg.name}</CardTitle>
                  <div className="text-3xl font-bold text-primary">{pkg.price}</div>
                  <p className="text-muted-foreground">{pkg.description}</p>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3 mb-6">
                    {pkg.features.map((feature, i) => (
                      <li key={i} className="flex items-center gap-2">
                        <Star className="w-4 h-4 text-primary" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <Button className="w-full" variant={pkg.popular ? "default" : "outline"}>
                    Choose Package
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-16 bg-muted/50">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid gap-12 lg:grid-cols-2 items-center">
            <div>
              <h2 className="text-3xl font-bold mb-6">Why Choose EasyLabs Music?</h2>
              <div className="space-y-4">
                <div className="flex items-start gap-4">
                  <TrendingUp className="w-6 h-6 text-primary mt-1" />
                  <div>
                    <h3 className="font-semibold mb-2">Proven Results</h3>
                    <p className="text-muted-foreground">We've helped 200+ artists get playlisted on Spotify, Apple Music, and major streaming platforms.</p>
                  </div>
                </div>
                <div className="flex items-start gap-4">
                  <Music className="w-6 h-6 text-primary mt-1" />
                  <div>
                    <h3 className="font-semibold mb-2">Professional Quality</h3>
                    <p className="text-muted-foreground">Industry-standard production, mixing, and mastering that meets professional release standards.</p>
                  </div>
                </div>
                <div className="flex items-start gap-4">
                  <Globe className="w-6 h-6 text-primary mt-1" />
                  <div>
                    <h3 className="font-semibold mb-2">Complete Solution</h3>
                    <p className="text-muted-foreground">From beats to branding, distribution to marketing - everything you need under one roof.</p>
                  </div>
                </div>
              </div>
            </div>
            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardContent className="p-6 text-center">
                  <div className="text-3xl font-bold text-primary mb-2">500+</div>
                  <div className="text-muted-foreground">Beats Produced</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-6 text-center">
                  <div className="text-3xl font-bold text-primary mb-2">200+</div>
                  <div className="text-muted-foreground">Artists Helped</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-6 text-center">
                  <div className="text-3xl font-bold text-primary mb-2">50+</div>
                  <div className="text-muted-foreground">Playlist Placements</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-6 text-center">
                  <div className="text-3xl font-bold text-primary mb-2">24hr</div>
                  <div className="text-muted-foreground">Average Turnaround</div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-16">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">Ready to Launch Your Music Career?</h2>
          <p className="text-xl text-muted-foreground mb-8">
            Whether you're just starting or ready to scale, we'll set up the system that makes your music career flourish.
          </p>
          <div className="flex gap-4 justify-center mb-8">
            <Button size="lg" className="text-lg px-8">
              <Mail className="w-5 h-5 mr-2" />
              Get Started Today
            </Button>
            <Button size="lg" variant="outline" className="text-lg px-8">
              <Phone className="w-5 h-5 mr-2" />
              Book Free Consultation
            </Button>
          </div>
          <p className="text-muted-foreground">
            Join 200+ artists who've transformed their music careers with EasyLabs Music
          </p>
        </div>
      </section>
    </div>
  );
};

export default MusicServiceApp;