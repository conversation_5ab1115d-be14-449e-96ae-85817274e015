import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { toast } from 'sonner';




const ADMIN_PASSWORD = import.meta.env.VITE_MARKETING_ADMIN_PASSWORD || '';

export const BlogAdmin: React.FC = () => {
  const [authenticated, setAuthenticated] = useState(false);
  const [password, setPassword] = useState('');
  const [posts, setPosts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [excerpt, setExcerpt] = useState('');
  const [category, setCategory] = useState('Marketing');
  const [featuredImage, setFeaturedImage] = useState('');
  const [tags, setTags] = useState('');
  const [published, setPublished] = useState(true);
  const [editingId, setEditingId] = useState<string | null>(null);

  // comments not stored here; comments are stored under localStorage key `blog_comments` as { postId: [comments] }

  useEffect(() => {
    if (authenticated) fetchPosts();
  }, [authenticated]);

  const fetchPosts = () => {
    setLoading(true);
    const stored = localStorage.getItem('blog_posts');
    const list = stored ? JSON.parse(stored) : [];
    setPosts(list);
    setLoading(false);
  };

  const savePosts = (list: any[]) => {
    localStorage.setItem('blog_posts', JSON.stringify(list));
    window.dispatchEvent(new Event('blog_posts_updated'));
  };

  const login = () => {
    if (!ADMIN_PASSWORD) {
      toast.error('Admin password not configured. Set VITE_MARKETING_ADMIN_PASSWORD in .env');
      return;
    }

    if (password === ADMIN_PASSWORD) {
      setAuthenticated(true);
      toast.success('Authenticated');
      // Sync URL: set hash and push /blog-admin path for this admin UI
      try {
        // add hash marker
        if (window.location.hash !== '#blog-admin') {
          window.location.hash = 'blog-admin';
        }

        // push /blog-admin at end of current pathname if not already present
        const p = window.location.pathname || '';
        if (!p.endsWith('/blog-admin')) {
          const newPath = p.replace(/\/$/, '') + '/blog-admin';
          window.history.replaceState({}, '', newPath + window.location.search + window.location.hash);
        }
      } catch (e) {
        // ignore
      }
    } else {
      toast.error('Invalid password');
    }
  };

  const logout = () => {
    setAuthenticated(false);
    // remove admin markers from url: hash and pathname
    try {
      if (window.location.hash === '#blog-admin') window.history.replaceState({}, '', window.location.pathname + window.location.search + '');
      // if pathname ends with /blog-admin remove it
      const p = window.location.pathname || '';
      if (p.endsWith('/blog-admin')) {
        const newPath = p.replace(/\/blog-admin$/, '') || '/';
        window.history.replaceState({}, '', newPath + window.location.search + window.location.hash);
      }
    } catch (e) {}
  };

  const createPost = () => {
    if (!title || !content) return toast.error('Title and content required');
    const slug = title.toLowerCase().replace(/[^a-z0-9\s]/g, '').replace(/\s+/g, '-');
    const now = new Date().toISOString();
    const newPost = {
      id: Date.now().toString(),
      title,
      slug,
      content,
      excerpt,
      featured_image: featuredImage,
      author_name: 'Marketing Admin',
      category,
      tags: tags.split(',').map(t => t.trim()).filter(Boolean),
      reading_time: Math.max(1, Math.round((content.length / 800) * 3)),
      view_count: 0,
      published_at: now,
      created_at: now,
      published: published,
    };

    const existing = JSON.parse(localStorage.getItem('blog_posts') || '[]');
    if (editingId) {
      // update existing
      const updated = existing.map((p: any) => p.id === editingId ? { ...p, ...newPost, id: editingId, published_at: p.published_at || now } : p);
      savePosts(updated);
      toast.success('Post updated');
      setEditingId(null);
    } else {
      existing.unshift(newPost);
      savePosts(existing);
      toast.success('Post published');
    }

    // reset
    setTitle(''); setContent(''); setExcerpt(''); setFeaturedImage(''); setTags('');
    fetchPosts();
  };

  const editPost = (id: string) => {
    const stored = JSON.parse(localStorage.getItem('blog_posts') || '[]');
    const p = stored.find((x: any) => x.id === id);
    if (!p) return toast.error('Post not found');
    setEditingId(id);
    setTitle(p.title || '');
    setExcerpt(p.excerpt || '');
    setContent(p.content || '');
    setFeaturedImage(p.featured_image || '');
    setCategory(p.category || 'Marketing');
    setTags((p.tags || []).join(','));
    setPublished(Boolean(p.published));
  };

  const deletePost = (id: string) => {
    const stored = JSON.parse(localStorage.getItem('blog_posts') || '[]');
    const remaining = stored.filter((x: any) => x.id !== id);
    savePosts(remaining);
    // Also remove comments for this post
    try {
      const comments = JSON.parse(localStorage.getItem('blog_comments') || '{}');
      delete comments[id];
      localStorage.setItem('blog_comments', JSON.stringify(comments));
    } catch (e) {}
    toast.success('Post deleted');
    fetchPosts();
  };

  const fetchCommentsForPost = (postId: string) => {
    try {
      const comments = JSON.parse(localStorage.getItem('blog_comments') || '{}');
      return comments[postId] || [];
    } catch (e) {
      return [];
    }
  };

  const deleteComment = (postId: string, commentId: string) => {
    try {
      const comments = JSON.parse(localStorage.getItem('blog_comments') || '{}');
      comments[postId] = (comments[postId] || []).filter((c: any) => c.id !== commentId);
      localStorage.setItem('blog_comments', JSON.stringify(comments));
      toast.success('Comment removed');
      window.dispatchEvent(new Event('blog_posts_updated'));
    } catch (e) {
      console.error(e);
      toast.error('Failed to remove comment');
    }
  };

  // UI helpers: show comment panel per post, allow edit/delete inline
  const [openCommentsFor, setOpenCommentsFor] = useState<string | null>(null);
  const [editingComment, setEditingComment] = useState<{ postId: string; id: string; author: string; text: string } | null>(null);

  const openComments = (postId: string) => {
    setOpenCommentsFor(openCommentsFor === postId ? null : postId);
  };

  const saveEditedComment = () => {
    if (!editingComment) return;
    try {
      const commentsStore = JSON.parse(localStorage.getItem('blog_comments') || '{}');
      commentsStore[editingComment.postId] = (commentsStore[editingComment.postId] || []).map((c: any) => c.id === editingComment.id ? { ...c, author: editingComment.author, text: editingComment.text } : c);
      localStorage.setItem('blog_comments', JSON.stringify(commentsStore));
      toast.success('Comment updated');
      setEditingComment(null);
      window.dispatchEvent(new Event('blog_posts_updated'));
    } catch (e) {
      console.error(e);
      toast.error('Failed to update comment');
    }
  };

  if (!authenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md p-6">
          <CardContent>
            <h2 className="text-2xl font-bold mb-4">Marketing Blog Admin</h2>
            <p className="text-sm text-muted-foreground mb-4">Enter admin password to manage marketing blog posts.</p>
            <Input value={password} onChange={(e) => setPassword(e.target.value)} placeholder="Password" type="password" />
            <div className="mt-4 flex gap-2">
              <Button onClick={login}>Login</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">Marketing Blog Admin</h1>
        <div className="flex gap-2">
          <Button variant="outline" onClick={logout}>Logout</Button>
        </div>
      </div>

      <Card className="mb-6">
        <CardContent>
          <div className="grid gap-3">
            <Input placeholder="Title" value={title} onChange={(e) => setTitle(e.target.value)} />
            <Input placeholder="Excerpt" value={excerpt} onChange={(e) => setExcerpt(e.target.value)} />
            <Textarea placeholder="Content (markdown/HTML)" value={content} onChange={(e) => setContent(e.target.value)} rows={8} />
            <Input placeholder="Featured image URL" value={featuredImage} onChange={(e) => setFeaturedImage(e.target.value)} />
            <Input placeholder="Category" value={category} onChange={(e) => setCategory(e.target.value)} />
            <Input placeholder="Tags (comma separated)" value={tags} onChange={(e) => setTags(e.target.value)} />
            <div className="flex items-center gap-2">
              <Switch checked={published} onCheckedChange={(v) => setPublished(Boolean(v))} />
              <Label>Publish immediately</Label>
            </div>
            <div className="flex gap-2">
              <Button onClick={createPost}>{editingId ? 'Update Post' : 'Create Post'}</Button>
              {editingId && (
                <Button variant="ghost" onClick={() => { setEditingId(null); setTitle(''); setContent(''); setExcerpt(''); setFeaturedImage(''); setTags(''); }}>Cancel</Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="space-y-4">
        {loading ? <div>Loading...</div> : (
          posts.map(p => (
            <Card key={p.id} className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="flex items-center gap-2">
                    <h3 className="font-semibold">{p.title}</h3>
                    <Badge>{p.category}</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground line-clamp-2">{p.excerpt || p.content.substring(0, 140)}</p>
                </div>
                <div className="flex gap-2">
                        <Button size="sm" variant="outline" onClick={() => editPost(p.id)}>Edit</Button>
                        <Button size="sm" variant="destructive" onClick={() => deletePost(p.id)}>Delete</Button>
                        <Button size="sm" onClick={() => openComments(p.id)}>{openCommentsFor === p.id ? 'Hide Comments' : 'Comments'}</Button>
                </div>
              </div>
                    {openCommentsFor === p.id && (
                      <div className="mt-3 space-y-2">
                        {fetchCommentsForPost(p.id).length === 0 ? (
                          <div className="text-sm text-muted-foreground">No comments yet.</div>
                        ) : (
                          fetchCommentsForPost(p.id).map((c: any) => (
                            <Card key={c.id} className="p-3">
                              <div className="flex items-start justify-between">
                                <div>
                                  <div className="font-medium">{c.author}</div>
                                  <div className="text-sm text-muted-foreground">{c.text}</div>
                                  <div className="text-xs text-muted-foreground mt-1">{new Date(c.created_at).toLocaleString()}</div>
                                </div>
                                <div className="flex flex-col items-end gap-2">
                                  <div className="flex gap-2">
                                    <Button size="sm" variant="outline" onClick={() => setEditingComment({ postId: p.id, id: c.id, author: c.author, text: c.text })}>Edit</Button>
                                    <Button size="sm" variant="destructive" onClick={() => deleteComment(p.id, c.id)}>Delete</Button>
                                  </div>
                                </div>
                              </div>
                            </Card>
                          ))
                        )}
                      </div>
                    )}
                  </Card>
          ))
        )}
      </div>
      {editingComment && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 p-4">
          <Card className="w-full max-w-2xl">
            <CardContent>
              <h3 className="text-xl font-semibold mb-2">Edit Comment</h3>
              <Input value={editingComment.author} onChange={(e) => setEditingComment({ ...editingComment, author: e.target.value })} placeholder="Author" />
              <Textarea value={editingComment.text} onChange={(e) => setEditingComment({ ...editingComment, text: e.target.value })} rows={4} className="mt-2" />
              <div className="flex gap-2 mt-4">
                <Button onClick={saveEditedComment}>Save</Button>
                <Button variant="ghost" onClick={() => setEditingComment(null)}>Cancel</Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default BlogAdmin;
