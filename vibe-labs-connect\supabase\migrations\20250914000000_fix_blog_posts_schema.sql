-- Fix blog_posts table schema to match TypeScript interface
-- Drop existing table if it exists with wrong schema
DROP TABLE IF EXISTS public.blog_posts CASCADE;

-- Create blog_posts table with correct schema
CREATE TABLE public.blog_posts (
    id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    category TEXT NOT NULL DEFAULT 'General',
    tags TEXT[] DEFAULT '{}',
    featured_image TEXT,
    is_featured BOOLEAN DEFAULT false,
    published BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    slug TEXT UNIQUE NOT NULL,
    view_count INTEGER DEFAULT 0,
    seo_title TEXT,
    seo_description TEXT
);

-- Enable Row Level Security
ALTER TABLE public.blog_posts ENABLE ROW LEVEL SECURITY;

-- Create policies for public access (adjust as needed for your security requirements)
CREATE POLICY "Anyone can view published blog posts" ON public.blog_posts
    FOR SELECT USING (published = true);

CREATE POLICY "Anyone can insert blog posts" ON public.blog_posts
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Anyone can update blog posts" ON public.blog_posts
    FOR UPDATE USING (true);

CREATE POLICY "Anyone can delete blog posts" ON public.blog_posts
    FOR DELETE USING (true);

-- Create function to auto-update updated_at timestamp
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to auto-update updated_at
CREATE TRIGGER blog_posts_updated_at
    BEFORE UPDATE ON public.blog_posts
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

-- Create function to auto-generate slug if not provided
CREATE OR REPLACE FUNCTION public.generate_slug_from_title()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.slug IS NULL OR NEW.slug = '' THEN
        NEW.slug = lower(regexp_replace(NEW.title, '[^a-zA-Z0-9\s]', '', 'g'));
        NEW.slug = regexp_replace(NEW.slug, '\s+', '-', 'g');
        NEW.slug = trim(both '-' from NEW.slug);
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to auto-generate slug
CREATE TRIGGER blog_posts_generate_slug
    BEFORE INSERT OR UPDATE ON public.blog_posts
    FOR EACH ROW EXECUTE FUNCTION public.generate_slug_from_title();
