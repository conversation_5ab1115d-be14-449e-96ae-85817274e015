/**
 * SEO Optimization Service
 * Provides comprehensive SEO features including meta tags, structured data, and keyword optimization
 */

export interface SEOData {
  title: string;
  description: string;
  keywords?: string[];
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'product' | 'service';
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  section?: string;
  tags?: string[];
  locale?: string;
  siteName?: string;
}

export interface StructuredData {
  '@context': string;
  '@type': string;
  [key: string]: any;
}

/**
 * Update document meta tags for SEO
 */
export const updateMetaTags = (seoData: SEOData): void => {
  const {
    title,
    description,
    keywords = [],
    image,
    url,
    type = 'website',
    author,
    publishedTime,
    modifiedTime,
    section,
    tags = [],
    locale = 'en_US',
    siteName = 'Vibe Labs Connect'
  } = seoData;

  // Update document title
  document.title = title;

  // Helper function to update or create meta tag
  const updateMetaTag = (name: string, content: string, property?: boolean) => {
    const attribute = property ? 'property' : 'name';
    let meta = document.querySelector(`meta[${attribute}="${name}"]`) as HTMLMetaElement;
    
    if (!meta) {
      meta = document.createElement('meta');
      meta.setAttribute(attribute, name);
      document.head.appendChild(meta);
    }
    
    meta.setAttribute('content', content);
  };

  // Basic meta tags
  updateMetaTag('description', description);
  if (keywords.length > 0) {
    updateMetaTag('keywords', keywords.join(', '));
  }
  if (author) {
    updateMetaTag('author', author);
  }

  // Open Graph meta tags
  updateMetaTag('og:title', title, true);
  updateMetaTag('og:description', description, true);
  updateMetaTag('og:type', type, true);
  updateMetaTag('og:locale', locale, true);
  updateMetaTag('og:site_name', siteName, true);
  
  if (url) {
    updateMetaTag('og:url', url, true);
  }
  
  if (image) {
    updateMetaTag('og:image', image, true);
    updateMetaTag('og:image:alt', title, true);
  }

  // Article-specific meta tags
  if (type === 'article') {
    if (author) {
      updateMetaTag('article:author', author, true);
    }
    if (publishedTime) {
      updateMetaTag('article:published_time', publishedTime, true);
    }
    if (modifiedTime) {
      updateMetaTag('article:modified_time', modifiedTime, true);
    }
    if (section) {
      updateMetaTag('article:section', section, true);
    }
    if (tags.length > 0) {
      // Remove existing article:tag meta tags
      document.querySelectorAll('meta[property="article:tag"]').forEach(tag => tag.remove());
      // Add new article:tag meta tags
      tags.forEach(tag => {
        const meta = document.createElement('meta');
        meta.setAttribute('property', 'article:tag');
        meta.setAttribute('content', tag);
        document.head.appendChild(meta);
      });
    }
  }

  // Twitter Card meta tags
  updateMetaTag('twitter:card', image ? 'summary_large_image' : 'summary');
  updateMetaTag('twitter:title', title);
  updateMetaTag('twitter:description', description);
  if (image) {
    updateMetaTag('twitter:image', image);
  }

  console.log('✅ SEO: Meta tags updated for:', title);
};

/**
 * Add structured data (JSON-LD) to the page
 */
export const addStructuredData = (data: StructuredData): void => {
  // Remove existing structured data script
  const existingScript = document.querySelector('script[type="application/ld+json"]');
  if (existingScript) {
    existingScript.remove();
  }

  // Create new structured data script
  const script = document.createElement('script');
  script.type = 'application/ld+json';
  script.textContent = JSON.stringify(data, null, 2);
  document.head.appendChild(script);

  console.log('✅ SEO: Structured data added:', data['@type']);
};

/**
 * Generate structured data for organization
 */
export const generateOrganizationStructuredData = (): StructuredData => {
  return {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'Vibe Labs Connect',
    description: 'Professional web development and digital marketing services for businesses across various industries.',
    url: 'https://vibelabsconnect.com',
    logo: 'https://vibelabsconnect.com/logo.png',
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '******-433-6588',
      contactType: 'customer service',
      availableLanguage: 'English'
    },
    sameAs: [
      'https://twitter.com/vibelabsconnect',
      'https://linkedin.com/company/vibelabsconnect',
      'https://facebook.com/vibelabsconnect'
    ],
    address: {
      '@type': 'PostalAddress',
      addressCountry: 'US'
    }
  };
};

/**
 * Generate structured data for service pages
 */
export const generateServiceStructuredData = (serviceName: string, description: string, category: string): StructuredData => {
  return {
    '@context': 'https://schema.org',
    '@type': 'Service',
    name: serviceName,
    description: description,
    category: category,
    provider: {
      '@type': 'Organization',
      name: 'Vibe Labs Connect',
      url: 'https://vibelabsconnect.com'
    },
    areaServed: {
      '@type': 'Country',
      name: 'United States'
    },
    hasOfferCatalog: {
      '@type': 'OfferCatalog',
      name: `${serviceName} Services`,
      itemListElement: [
        {
          '@type': 'Offer',
          itemOffered: {
            '@type': 'Service',
            name: serviceName,
            description: description
          }
        }
      ]
    }
  };
};

/**
 * Generate structured data for blog articles
 */
export const generateArticleStructuredData = (
  title: string,
  description: string,
  author: string,
  publishedDate: string,
  modifiedDate: string,
  image?: string,
  url?: string
): StructuredData => {
  return {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: title,
    description: description,
    author: {
      '@type': 'Person',
      name: author
    },
    publisher: {
      '@type': 'Organization',
      name: 'Vibe Labs Connect',
      logo: {
        '@type': 'ImageObject',
        url: 'https://vibelabsconnect.com/logo.png'
      }
    },
    datePublished: publishedDate,
    dateModified: modifiedDate,
    ...(image && {
      image: {
        '@type': 'ImageObject',
        url: image
      }
    }),
    ...(url && { url })
  };
};

/**
 * Generate structured data for FAQ sections
 */
export const generateFAQStructuredData = (faqs: Array<{ question: string; answer: string }>): StructuredData => {
  return {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map(faq => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer
      }
    }))
  };
};

/**
 * SEO service object with all methods
 */
export const seo = {
  updateMetaTags,
  addStructuredData,
  generateOrganizationStructuredData,
  generateServiceStructuredData,
  generateArticleStructuredData,
  generateFAQStructuredData
};

export default seo;
