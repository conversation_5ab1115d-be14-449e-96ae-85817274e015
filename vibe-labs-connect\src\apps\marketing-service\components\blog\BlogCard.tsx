import React from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calendar, Clock, Eye } from 'lucide-react';

interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  featured_image: string;
  author_name: string;
  category: string;
  tags: string[];
  reading_time: number;
  view_count: number;
  published_at: string;
}

interface BlogCardProps {
  post: BlogPost;
  onReadMore?: (slug: string) => void;
  variant?: 'default' | 'featured' | 'compact';
}

export const BlogCard: React.FC<BlogCardProps> = ({ 
  post, 
  onReadMore, 
  variant = 'default' 
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (variant === 'compact') {
    return (
      <Card className="glass-effect hover-lift group cursor-pointer" onClick={() => onReadMore(post.slug)}>
        <CardContent className="p-4">
          <div className="flex gap-4">
            {post.featured_image && (
              <div className="w-20 h-20 rounded-lg overflow-hidden flex-shrink-0">
                <img
                  src={post.featured_image}
                  alt={post.title}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
              </div>
            )}
            <div className="flex-1 min-w-0">
              <Badge variant="secondary" className="text-xs mb-2 bg-accent/20 text-accent border-accent/30">
                {post.category}
              </Badge>
              <h3 className="font-semibold text-sm line-clamp-2 group-hover:text-accent transition-colors text-white">
                {post.title}
              </h3>
              <div className="flex items-center gap-3 mt-2 text-xs text-muted-foreground">
                <span className="flex items-center gap-1">
                  <Calendar className="w-3 h-3" />
                  {formatDate(post.published_at)}
                </span>
                <span className="flex items-center gap-1">
                  <Clock className="w-3 h-3" />
                  {post.reading_time}m
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const cardClass = variant === 'featured'
    ? "glass-effect hover-lift group border-2 border-accent/30 premium-shadow scale-in"
    : "glass-effect hover-lift group scale-in";

  return (
    <Card className={`${cardClass} cursor-pointer`} onClick={() => onReadMore(post.slug)}>
      {post.featured_image && (
        <div className="relative overflow-hidden rounded-t-lg">
          <img
            src={post.featured_image}
            alt={post.title}
            className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
          />
          <div className="absolute top-4 left-4">
            <Badge className="bg-accent/90 text-accent-foreground">
              {post.category}
            </Badge>
          </div>
          {variant === 'featured' && (
            <div className="absolute top-4 right-4">
              <Badge className="bg-accent text-accent-foreground pulse-glow">
                Featured
              </Badge>
            </div>
          )}
        </div>
      )}
      
      <CardHeader>
        <div className="space-y-2">
          <h3 className={`font-bold group-hover:text-accent transition-colors text-white ${
            variant === 'featured' ? 'text-xl' : 'text-lg'
          }`}>
            {post.title}
          </h3>

          <p className="text-muted-foreground text-sm line-clamp-3">
            {post.excerpt}
          </p>

          <div className="flex flex-wrap gap-2">
            {post.tags.slice(0, 3).map((tag) => (
              <Badge key={tag} variant="outline" className="text-xs border-accent/30 text-accent hover:bg-accent hover:text-accent-foreground">
                {tag}
              </Badge>
            ))}
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <span className="flex items-center gap-1">
            <Calendar className="w-4 h-4" />
            {formatDate(post.published_at)}
          </span>
          <span className="flex items-center gap-1">
            <Clock className="w-4 h-4" />
            {post.reading_time}m read
          </span>
          <span className="flex items-center gap-1">
            <Eye className="w-4 h-4" />
            {post.view_count}
          </span>
        </div>
      </CardContent>
    </Card>
  );
};

export default BlogCard;
