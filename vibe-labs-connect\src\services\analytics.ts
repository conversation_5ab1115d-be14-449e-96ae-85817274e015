/**
 * Google Analytics 4 (GA4) Integration Service
 * Provides comprehensive tracking for visitor analytics, conversions, and affiliate link clicks
 */

// GA4 Configuration
const GA4_MEASUREMENT_ID = import.meta.env.VITE_GA4_MEASUREMENT_ID || 'G-XXXXXXXXXX';

// Declare gtag function for TypeScript
declare global {
  interface Window {
    gtag: (...args: any[]) => void;
    dataLayer: any[];
  }
}

/**
 * Initialize Google Analytics 4
 */
export const initializeGA4 = (): void => {
  // Only initialize in production or when GA4_MEASUREMENT_ID is provided
  if (!GA4_MEASUREMENT_ID || GA4_MEASUREMENT_ID === 'G-XXXXXXXXXX') {
    console.log('🔍 GA4: Measurement ID not configured, skipping initialization');
    return;
  }

  try {
    // Create dataLayer if it doesn't exist
    window.dataLayer = window.dataLayer || [];
    
    // Define gtag function
    window.gtag = function() {
      window.dataLayer.push(arguments);
    };

    // Initialize GA4
    window.gtag('js', new Date());
    window.gtag('config', GA4_MEASUREMENT_ID, {
      // Enhanced measurement settings
      send_page_view: true,
      allow_google_signals: true,
      allow_ad_personalization_signals: true,
      // Custom settings
      custom_map: {
        'custom_parameter_1': 'service_type',
        'custom_parameter_2': 'user_category'
      }
    });

    // Load GA4 script
    const script = document.createElement('script');
    script.async = true;
    script.src = `https://www.googletagmanager.com/gtag/js?id=${GA4_MEASUREMENT_ID}`;
    document.head.appendChild(script);

    console.log('✅ GA4: Successfully initialized with ID:', GA4_MEASUREMENT_ID);
  } catch (error) {
    console.error('❌ GA4: Error initializing:', error);
  }
};

/**
 * Track page views
 */
export const trackPageView = (pagePath: string, pageTitle?: string): void => {
  if (typeof window.gtag === 'function') {
    window.gtag('config', GA4_MEASUREMENT_ID, {
      page_path: pagePath,
      page_title: pageTitle || document.title
    });
    console.log('📊 GA4: Page view tracked:', pagePath);
  }
};

/**
 * Track custom events
 */
export const trackEvent = (eventName: string, parameters: Record<string, any> = {}): void => {
  if (typeof window.gtag === 'function') {
    window.gtag('event', eventName, {
      event_category: parameters.category || 'engagement',
      event_label: parameters.label,
      value: parameters.value,
      ...parameters
    });
    console.log('📊 GA4: Event tracked:', eventName, parameters);
  }
};

/**
 * Track affiliate link clicks
 */
export const trackAffiliateClick = (linkUrl: string, linkText: string, position?: string): void => {
  trackEvent('affiliate_click', {
    category: 'affiliate',
    label: linkText,
    link_url: linkUrl,
    link_position: position,
    value: 1
  });
};

/**
 * Track service page visits
 */
export const trackServiceVisit = (serviceName: string, serviceCategory: string): void => {
  trackEvent('service_visit', {
    category: 'services',
    label: serviceName,
    service_category: serviceCategory,
    value: 1
  });
};

/**
 * Track contact form submissions
 */
export const trackContactSubmission = (formType: string, serviceType?: string): void => {
  trackEvent('contact_form_submit', {
    category: 'lead_generation',
    label: formType,
    service_type: serviceType,
    value: 1
  });
};

/**
 * Track blog interactions
 */
export const trackBlogInteraction = (action: string, postTitle: string, category: string): void => {
  trackEvent('blog_interaction', {
    category: 'content',
    label: postTitle,
    blog_action: action,
    blog_category: category,
    value: 1
  });
};

/**
 * Track conversions (when users complete desired actions)
 */
export const trackConversion = (conversionType: string, value?: number, currency: string = 'USD'): void => {
  trackEvent('conversion', {
    category: 'conversions',
    label: conversionType,
    value: value,
    currency: currency
  });
};

/**
 * Track user engagement time
 */
export const trackEngagementTime = (timeSpent: number, pagePath: string): void => {
  trackEvent('engagement_time', {
    category: 'engagement',
    label: pagePath,
    value: Math.round(timeSpent / 1000), // Convert to seconds
    engagement_time_msec: timeSpent
  });
};

/**
 * Track scroll depth
 */
export const trackScrollDepth = (percentage: number, pagePath: string): void => {
  trackEvent('scroll_depth', {
    category: 'engagement',
    label: pagePath,
    value: percentage,
    scroll_percentage: percentage
  });
};

/**
 * Set user properties
 */
export const setUserProperties = (properties: Record<string, any>): void => {
  if (typeof window.gtag === 'function') {
    window.gtag('config', GA4_MEASUREMENT_ID, {
      user_properties: properties
    });
    console.log('👤 GA4: User properties set:', properties);
  }
};

/**
 * Enhanced ecommerce tracking (for future use)
 */
export const trackPurchase = (transactionId: string, items: any[], value: number, currency: string = 'USD'): void => {
  trackEvent('purchase', {
    transaction_id: transactionId,
    value: value,
    currency: currency,
    items: items
  });
};

// Export the analytics service
export const analytics = {
  initialize: initializeGA4,
  trackPageView,
  trackEvent,
  trackAffiliateClick,
  trackServiceVisit,
  trackContactSubmission,
  trackBlogInteraction,
  trackConversion,
  trackEngagementTime,
  trackScrollDepth,
  setUserProperties,
  trackPurchase
};

export default analytics;
