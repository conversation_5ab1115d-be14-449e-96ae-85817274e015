import React from "react";
import ReactD<PERSON> from "react-dom/client";
import { <PERSON><PERSON>er<PERSON>outer } from "react-router-dom";  // ⬅️ add this
import "../../index.css";
import MusicServiceApp from "./PodcastServiceApp.tsx";

ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <BrowserRouter>
      <MusicServiceApp />
    </BrowserRouter>
  </React.StrictMode>
);
    


